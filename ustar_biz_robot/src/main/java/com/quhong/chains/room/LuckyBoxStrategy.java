package com.quhong.chains.room;

import com.quhong.chains.AbstractRobotStrategy;
import com.quhong.executors.http.LuckyBoxHandler;
import com.quhong.robot.Robot;
import org.springframework.util.StringUtils;

public class LuckyBoxStrategy extends AbstractRobotStrategy {
    private final LuckyBoxHandler luckyBoxHandler;


    public LuckyBoxStrategy(Robot robot, LuckyBoxHandler luckyBoxHandler, String taskId) {
        super(robot);
        this.needTick = true;
        this.luckyBoxHandler = luckyBoxHandler;
        robot.getRobotData().setLuckyBoxId(taskId);
    }

    @Override
    protected void doDispose() {
        // for override
    }

    @Override
    public void start() {

    }

    @Override
    protected void doOnTick() {
        synchronized (this.robot) {
            if (!StringUtils.isEmpty(robot.getRoomId()) && !StringUtils.isEmpty(robot.getRobotData().getLuckyBoxId())) {
                luckyBoxHandler.rushRequest(this.robot, robot.getRobotData().getLuckyBoxId());
            }
        }
    }
}

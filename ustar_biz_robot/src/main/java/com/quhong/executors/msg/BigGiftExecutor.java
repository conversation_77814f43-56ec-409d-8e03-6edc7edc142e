package com.quhong.executors.msg;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.constant.RobotConstant;
import com.quhong.core.annotation.MsgExecutor;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ConfigTaskData;
import com.quhong.data.EnterRoomTask;
import com.quhong.enums.Cmd;
import com.quhong.msg.room.BigGiftMsg;
import com.quhong.mysql.data.BizRobotConfigData;
import com.quhong.robot.Robot;
import com.quhong.service.RoomRiskService;
import com.quhong.service.RoomTaskService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

@SuppressWarnings("all")
@Component
@MsgExecutor
public class BigGiftExecutor extends RobotMsgExecutor<BigGiftMsg> {
    private static final Logger logger = LoggerFactory.getLogger(BigGiftExecutor.class);
    private static final Interner<String> stringPool = Interners.newWeakInterner();
    private static final Cache<String, Boolean> roomBigGift = Caffeine.newBuilder()
            .expireAfterWrite(10, TimeUnit.MINUTES)
            .initialCapacity(100)
            .maximumSize(1000)
            .build();
    // 防止重复刷礼物，进入过多的机器人
    public static final Cache<String, AtomicInteger> bigGiftCount = Caffeine.newBuilder()
            .expireAfterWrite(3, TimeUnit.MINUTES)
            .initialCapacity(100)
            .maximumSize(5000)
            .build();
    @Resource
    private RoomRiskService roomRiskService;
    @Resource
    private RoomTaskService roomTaskService;

    public BigGiftExecutor() {
        super(Cmd.BIG_GIFT_MSG);
    }


    @Override
    public void doExecute(Robot connector, BigGiftMsg msg) {
        String roomId = msg.getFromRoomId();
        String lockKey = getLockKey(msg);
        if (Boolean.TRUE.equals(roomBigGift.getIfPresent(lockKey))) {
            return;
        }
        synchronized (stringPool.intern(lockKey)) {
            if (Boolean.TRUE.equals(roomBigGift.getIfPresent(lockKey))) {
                return;
            }
            roomBigGift.put(lockKey, true);
            AtomicInteger count = bigGiftCount.get(roomId, k -> new AtomicInteger(0));
            int totalCount = count.incrementAndGet();
            if (totalCount > 8) {
                // 不再增派额外的机器人了
                logger.info("big gift skip send robot. boxId={} totalCount={}", lockKey, totalCount);
                return;
            }
            for (ConfigTaskData taskData : roomTaskService.getTaskMap().values()) {
                BizRobotConfigData configData = taskData.getConfigData();
                if (taskData.isStopping() || configData.blockEnterRoom(roomId)) {
                    continue;
                }
                if (0 == configData.getBgEnterRoomFrom() || 0 == configData.getBgEnterRoomTo()) {
                    continue;
                }
//                if (roomRiskService.isBlock(roomId, taskData)) {
//                    logger.info("bigGift enter room. room has block. roomId={}", roomId);
//                    continue;
//                }
                int needEnterRobotCount = ThreadLocalRandom.current().nextInt(configData.getBgEnterRoomFrom(), configData.getBgEnterRoomTo());
                taskData.setAddBgTaskTime(DateHelper.getNowSeconds());
                long nowMillis = System.currentTimeMillis();
                List<EnterRoomTask> taskList = new ArrayList<>(needEnterRobotCount);
                for (int i = 0; i < needEnterRobotCount; i++) {
                    long enterTime = ThreadLocalRandom.current().nextLong(1000, 7000);
                    taskList.add(new EnterRoomTask(nowMillis + enterTime, RobotConstant.BIG_GIFT_ENTER_ROOM));
                }
                taskList.sort(Comparator.comparing(EnterRoomTask::getEnterTime));
                logger.info("add enterRoom task from big gift. roomId={} key={} taskId={} taskList={}", roomId, lockKey, configData.getId(), taskList.size());
                roomTaskService.addEnterRoomTask(roomId, taskData, taskList);
            }
        }
    }

    private String getLockKey(BigGiftMsg msg) {
        return msg.getFromRoomId() + "-" + msg.getHeader().getTimestamp();
    }
}

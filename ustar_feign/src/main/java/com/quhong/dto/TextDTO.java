package com.quhong.dto;

import com.quhong.handler.HttpEnvData;

public class TextDTO extends HttpEnvData {
    private String text;     // 检测文本
    private String origin;  // 检测场景
    private String fromUid;  // 来源uid

    public TextDTO(String text) {
        this.text = text;
    }

    public TextDTO(String text, String origin) {
        this.text = text;
        this.origin = origin;
    }

    public TextDTO(String text, String origin, String fromUid) {
        this.text = text;
        this.origin = origin;
        this.fromUid = fromUid;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getOrigin() {
        return origin;
    }

    public void setOrigin(String origin) {
        this.origin = origin;
    }

    public String getFromUid() {
        return fromUid;
    }

    public void setFromUid(String fromUid) {
        this.fromUid = fromUid;
    }
}

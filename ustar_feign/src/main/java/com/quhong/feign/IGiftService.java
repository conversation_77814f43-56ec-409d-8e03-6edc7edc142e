package com.quhong.feign;

import com.quhong.datas.HttpResult;
import com.quhong.dto.SendGiftDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;


/**
 * 仅k8s服务可用
 */
@FeignClient(name = "ustar-java-gift", url = "ustar-java-gift:8080", fallback = IGiftService.DefaultFallback.class)
public interface IGiftService {

    /**
     * 发送礼物
     */
    @PostMapping(value = "/inner/gift/restSend")
    HttpResult<Object> sendGift(SendGiftDTO dto);

    @Component
    class DefaultFallback implements IGiftService {
        private static final Logger logger = LoggerFactory.getLogger(IGiftService.class);

        @Override
        public HttpResult<Object> sendGift(SendGiftDTO dto) {
            logger.error("inner sendGift error uid={}", dto.getUid());
            return HttpResult.getError();
        }
    }
}

package com.quhong.feign;

import com.alibaba.fastjson.JSON;
import com.quhong.data.dto.DataCenterPageDTO;
import com.quhong.data.dto.MoneyTypeDTO;
import com.quhong.data.dto.MoneyDetailReq;
import com.quhong.enums.ApiResult;
import com.quhong.enums.HttpCode;
import com.quhong.mysql.data.MoneyDetail;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;


/**
 * 仅k8s服务可用
 */
@FeignClient(name = "ustar-data-center", url = "ustar-data-center:8080", fallback = DataCenterService.DefaultFallback.class)
public interface DataCenterService {

    /**
     * 加钻
     */
    @PostMapping(value = "/inner/data_center/charge_beans")
    ApiResult<String> chargeBeans(@RequestBody MoneyDetailReq moneyDetailReq);

    /**
     * 减钻
     * 成功返回0，1钻石余额不足，1002服务器错误
     */
    @PostMapping(value = "/inner/data_center/reduce_beans")
    ApiResult<String> reduceBeans(@RequestBody MoneyDetailReq moneyDetailReq);


    /**
     * es流水记录
     */
    @PostMapping(value = "/inner/data_center/es_money_detail")
    ApiResult<List<MoneyDetail>> esMoneyDetail(@RequestBody DataCenterPageDTO req);

    @PostMapping(value = "/inner/data_center/search_user_money_detail")
    ApiResult<List<MoneyDetail>> searchUserMoneyDetails(@RequestBody DataCenterPageDTO req);

    /**
     * es某个类型总数
     */
    @PostMapping(value = "/inner/data_center/es_money_type_total")
    ApiResult<Long> esMoneyTypeTotal(@RequestBody MoneyTypeDTO req);

    @PostMapping(value = "/inner/data_center/es_money_total_beans")
    ApiResult<Long> userTotalBeans(@RequestBody MoneyTypeDTO req);


    @Component
    class DefaultFallback implements DataCenterService {
        private static final Logger logger = LoggerFactory.getLogger(DataCenterService.class);

        @Override
        public ApiResult<String> chargeBeans(MoneyDetailReq moneyDetailReq) {
            logger.error("charge beans error. {}", JSON.toJSONString(moneyDetailReq));
            return ApiResult.getError(HttpCode.SERVER_ERROR);
        }

        @Override
        public ApiResult<String> reduceBeans(MoneyDetailReq moneyDetailReq) {
            logger.error("reduce beans error. {}", JSON.toJSONString(moneyDetailReq));
            return ApiResult.getError(HttpCode.SERVER_ERROR);
        }

        @Override
        public ApiResult<List<MoneyDetail>> esMoneyDetail(DataCenterPageDTO req) {
            logger.error("es money detail error. {}", JSON.toJSONString(req));
            return ApiResult.getError(HttpCode.SERVER_ERROR);
        }

        @Override
        public ApiResult<List<MoneyDetail>> searchUserMoneyDetails(DataCenterPageDTO req) {
            logger.error("search user es money detail error. {}", JSON.toJSONString(req));
            return ApiResult.getError(HttpCode.SERVER_ERROR);
        }

        @Override
        public ApiResult<Long> esMoneyTypeTotal(MoneyTypeDTO req) {
            logger.error("esMoneyTypeTotal error. {}", JSON.toJSONString(req));
            return ApiResult.getError(HttpCode.SERVER_ERROR);
        }

        @Override
        public ApiResult<Long> userTotalBeans(MoneyTypeDTO req) {
            logger.error("userTotalBeans error. {}", JSON.toJSONString(req));
            return ApiResult.getError(HttpCode.SERVER_ERROR);
        }
    }
}

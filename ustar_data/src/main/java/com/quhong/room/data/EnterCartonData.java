package com.quhong.room.data;

public class EnterCartonData {
    private String smallIcon;//进场动画预览图
    private int forbidOnTime; ////禁言时间 ms
    private int joinCartoonId = 0; //进入房间动画id
    private int sourceType = 0; //资源类型
    private String sourceUrl; //资源Url
    private String sourceMd5; //资源Md5
    private int joinCartoonType = 0;//进场动画类型
    private long cartoonTimes = 0;//进场动画持续时间 ms
    private String entryEffectUrl; // 进场通知资源url
    private String entryEffectUrlAr; // 进场通知资源url

    public EnterCartonData(){

    }

    public String getSmallIcon() {
        return smallIcon;
    }

    public void setSmallIcon(String smallIcon) {
        this.smallIcon = smallIcon;
    }

    public int getForbidOnTime() {
        return forbidOnTime;
    }

    public void setForbidOnTime(int forbidOnTime) {
        this.forbidOnTime = forbidOnTime;
    }

    public int getJoinCartoonId() {
        return joinCartoonId;
    }

    public void setJoinCartoonId(int joinCartoonId) {
        this.joinCartoonId = joinCartoonId;
    }

    public int getSourceType() {
        return sourceType;
    }

    public void setSourceType(int sourceType) {
        this.sourceType = sourceType;
    }

    public String getSourceUrl() {
        return sourceUrl;
    }

    public void setSourceUrl(String sourceUrl) {
        this.sourceUrl = sourceUrl;
    }

    public String getSourceMd5() {
        return sourceMd5;
    }

    public void setSourceMd5(String sourceMd5) {
        this.sourceMd5 = sourceMd5;
    }

    public int getJoinCartoonType() {
        return joinCartoonType;
    }

    public void setJoinCartoonType(int joinCartoonType) {
        this.joinCartoonType = joinCartoonType;
    }

    public long getCartoonTimes() {
        return cartoonTimes;
    }

    public void setCartoonTimes(long cartoonTimes) {
        this.cartoonTimes = cartoonTimes;
    }

    public String getEntryEffectUrl() {
        return entryEffectUrl;
    }

    public void setEntryEffectUrl(String entryEffectUrl) {
        this.entryEffectUrl = entryEffectUrl;
    }

    public String getEntryEffectUrlAr() {
        return entryEffectUrlAr;
    }

    public void setEntryEffectUrlAr(String entryEffectUrlAr) {
        this.entryEffectUrlAr = entryEffectUrlAr;
    }
}

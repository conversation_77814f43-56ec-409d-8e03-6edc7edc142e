package com.quhong.mongo.data;

import com.quhong.mongo.dao.MReportDao;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/23
 */
@Document(collection = MReportDao.TABLE_NAME)
public class MReportData {

    @Id
    private ObjectId _id;

    private String uid;

    private String aid;

    private String reason;  // 举报原因，固定标签4选1

    private String remark;  // 举报原因，自写内容

    private int mtime;

    private String room_id;    // 房间id

    private String mid;        // 朋友圈id

    private String cid;        // 朋友圈评论id

    private List<String> img_list;

    private int new_type;  // 1: 举报用户  2: 举报房间  3: 举报朋友圈  4：举报房间活动

    private String target_id;    // 重复举报key

    private int status;  // 处理状态 1:已处理

    public ObjectId get_id() {
        return _id;
    }

    public void set_id(ObjectId _id) {
        this._id = _id;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getAid() {
        return aid;
    }

    public void setAid(String aid) {
        this.aid = aid;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public int getMtime() {
        return mtime;
    }

    public void setMtime(int mtime) {
        this.mtime = mtime;
    }

    public String getRoom_id() {
        return room_id;
    }

    public void setRoom_id(String room_id) {
        this.room_id = room_id;
    }

    public String getMid() {
        return mid;
    }

    public void setMid(String mid) {
        this.mid = mid;
    }

    public String getCid() {
        return cid;
    }

    public void setCid(String cid) {
        this.cid = cid;
    }

    public List<String> getImg_list() {
        return img_list;
    }

    public void setImg_list(List<String> img_list) {
        this.img_list = img_list;
    }

    public int getNew_type() {
        return new_type;
    }

    public void setNew_type(int new_type) {
        this.new_type = new_type;
    }

    public String getTarget_id() {
        return target_id;
    }

    public void setTarget_id(String target_id) {
        this.target_id = target_id;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }
}

package com.quhong.mongo.data;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import com.quhong.mongo.dao.AdvancedGiftDao;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

/**
 * 进阶礼物配置
 */
@Document(collection = AdvancedGiftDao.TABLE_NAME)
public class AdvancedGiftData {
    @Id
    @JSONField(serialize = false)
    private ObjectId _id;
    protected Integer giftId; // playType为0或2时，为普通礼物id，playType为1时，为进阶礼物id
    protected String giftIcon;
    protected String giftVideo;
    protected Integer playType;   // 0: 发送特定礼物数量展示不同礼物动效  1: 发送configList礼物，全满足giftNum，播放主礼物动效 2:解锁礼物
    protected String screenEn;
    protected String screenAr;
    protected List<AdvancedConfig> configList;
    protected Integer mtime;
    protected Integer ctime;
    protected Integer playTimeOut; // playType为1时，礼物玩法的超时时间,单位秒,其他为0
    protected Integer rateNum; // playType为2时有效，1个礼物对应多少的解锁值
    protected String lockGiftTag; // 解锁玩法礼物的tag s a b

    public static class AdvancedConfig {
        protected Integer giftId; // playType为0时，为进阶礼物id，playType为1时，为普通礼物id，playType为2时，为解锁礼物id
        protected Integer giftNum;//playType为2时,为总的解锁值
        protected String giftIcon;
        protected String giftVideo;
        protected String screenEn;
        protected String screenAr;
        protected Integer lockTimeOut; // playType为2时有效，该解锁礼物的解锁时间,单位秒
        protected Integer rateNum; // playType为2时有效，1个礼物对应多少的解锁值
        protected String lockGiftTag; // 解锁玩法礼物的tag s a b

        public Integer getGiftId() {
            return giftId;
        }

        public void setGiftId(Integer giftId) {
            this.giftId = giftId;
        }

        public Integer getGiftNum() {
            return giftNum;
        }

        public void setGiftNum(Integer giftNum) {
            this.giftNum = giftNum;
        }

        public String getScreenEn() {
            return screenEn;
        }

        public void setScreenEn(String screenEn) {
            this.screenEn = screenEn;
        }

        public String getScreenAr() {
            return screenAr;
        }

        public void setScreenAr(String screenAr) {
            this.screenAr = screenAr;
        }

        public String getGiftVideo() {
            return giftVideo;
        }

        public void setGiftVideo(String giftVideo) {
            this.giftVideo = giftVideo;
        }

        public String getGiftIcon() {
            return giftIcon;
        }

        public void setGiftIcon(String giftIcon) {
            this.giftIcon = giftIcon;
        }

        public Integer getLockTimeOut() {
            return lockTimeOut;
        }

        public void setLockTimeOut(Integer lockTimeOut) {
            this.lockTimeOut = lockTimeOut;
        }

        public Integer getRateNum() {
            return rateNum;
        }

        public void setRateNum(Integer rateNum) {
            this.rateNum = rateNum;
        }

        public String getLockGiftTag() {
            return lockGiftTag;
        }

        public void setLockGiftTag(String lockGiftTag) {
            this.lockGiftTag = lockGiftTag;
        }

        @Override
        public String toString() {
            return JSON.toJSONString(this);
        }
    }

    public ObjectId get_id() {
        return _id;
    }

    public void set_id(ObjectId _id) {
        this._id = _id;
    }

    public Integer getGiftId() {
        return giftId;
    }

    public void setGiftId(Integer giftId) {
        this.giftId = giftId;
    }

    public Integer getPlayType() {
        return playType;
    }

    public void setPlayType(Integer playType) {
        this.playType = playType;
    }

    public String getScreenEn() {
        return screenEn;
    }

    public void setScreenEn(String screenEn) {
        this.screenEn = screenEn;
    }

    public String getScreenAr() {
        return screenAr;
    }

    public void setScreenAr(String screenAr) {
        this.screenAr = screenAr;
    }

    public List<AdvancedConfig> getConfigList() {
        return configList;
    }

    public void setConfigList(List<AdvancedConfig> configList) {
        this.configList = configList;
    }

    public Integer getMtime() {
        return mtime;
    }

    public void setMtime(Integer mtime) {
        this.mtime = mtime;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }

    public Integer getPlayTimeOut() {
        return playTimeOut;
    }

    public void setPlayTimeOut(Integer playTimeOut) {
        this.playTimeOut = playTimeOut;
    }

    public String getGiftIcon() {
        return giftIcon;
    }

    public void setGiftIcon(String giftIcon) {
        this.giftIcon = giftIcon;
    }

    public String getGiftVideo() {
        return giftVideo;
    }

    public void setGiftVideo(String giftVideo) {
        this.giftVideo = giftVideo;
    }

    public Integer getRateNum() {
        return rateNum;
    }

    public void setRateNum(Integer rateNum) {
        this.rateNum = rateNum;
    }

    public String getLockGiftTag() {
        return lockGiftTag;
    }

    public void setLockGiftTag(String lockGiftTag) {
        this.lockGiftTag = lockGiftTag;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}

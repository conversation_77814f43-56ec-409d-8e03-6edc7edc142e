package com.quhong.mongo.data;

import com.quhong.mongo.dao.RankingActivityDao;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

/**
 * 冲榜活动模板
 */
@Document(collection = RankingActivityDao.TABLE_NAME)
public class RankingActivity {

    @Id
    private ObjectId _id;
    private String acNameEn; // 英文活动名
    private String acNameAr; // 阿语活动名
    private String acUrl; // h5地址(自动生成)
    private Integer startTime; // 活动开始时间
    private Integer endTime; // 活动结束时间
    private ActivityConfig config; // 活动配置
    private ConquerConfig conquerConfig; // 征服活动房间配置
    private List<ActivityGift> activityGiftList; // 活动礼物，都是按照钻石数来进行排行
    private List<RankingConfig> rankingConfigList; // 排行榜奖励配置
    private List<ReachingConfig> reachingConfigList; // 等级奖励，列表不为空时在奖励页面展示
    private Integer status; // 1 是否已结束
    private Integer templateType;   // 模板类型
    private Integer ctime; // 创建时间
    private Integer mtime; // 更新时间


    public static class ActivityConfig {
        private Integer homePageType; // 大头页类型 1静态 2动态 3静态动态
        private String homePagePicture; // 大头页静态图
        private String homePagePictureAr; // 大头页静态图ar
        private String homePagePictureDt; // 大头页动态图
        private String homePagePictureDtAr; // 大头页动态图ar
        private String mainPictureEn; // 英文主图
        private String mainPictureAr; // 阿语主图
        private String ruleTitleEn; // 英文规则标题
        private String ruleTitleAr; // 阿语规则标题
        private String acExplainEn; // 英文活动说明
        private String acExplainAr; // 阿语活动说明
        private String acPersonalData; // 活动个人页面url
        private String titleColor; // 标题颜色
        private String countDownColor; // 倒计时颜色
        private String textColor; // 正文颜色
        private String userDataColor; // 用户数据颜色
        private String btnCssUrl; // 返回按钮css的url
        private String pagePictureEn; // 英文页面主图url
        private String pagePictureAr; // 阿语页面主图url
        private int hasGradeReward; // 是否有等级奖励 1有
        private int noRankReward; // 1没有排行奖励，0有排行奖励(兼容旧版本)
        private String pageColor; // 页面背景色

        private int rulesPopup;  // 是否展示规则弹窗
        private String rulesButtonEn;
        private String rulesButtonAr;
        private String rulesPageEn;
        private String rulesPageAr;

        public Integer getHomePageType() {
            return homePageType;
        }

        public void setHomePageType(Integer homePageType) {
            this.homePageType = homePageType;
        }

        public String getHomePagePicture() {
            return homePagePicture;
        }

        public void setHomePagePicture(String homePagePicture) {
            this.homePagePicture = homePagePicture;
        }

        public String getHomePagePictureDt() {
            return homePagePictureDt;
        }

        public void setHomePagePictureDt(String homePagePictureDt) {
            this.homePagePictureDt = homePagePictureDt;
        }

        public String getMainPictureEn() {
            return mainPictureEn;
        }

        public void setMainPictureEn(String mainPictureEn) {
            this.mainPictureEn = mainPictureEn;
        }

        public String getHomePagePictureAr() {
            return homePagePictureAr;
        }

        public void setHomePagePictureAr(String homePagePictureAr) {
            this.homePagePictureAr = homePagePictureAr;
        }

        public String getHomePagePictureDtAr() {
            return homePagePictureDtAr;
        }

        public void setHomePagePictureDtAr(String homePagePictureDtAr) {
            this.homePagePictureDtAr = homePagePictureDtAr;
        }

        public String getMainPictureAr() {
            return mainPictureAr;
        }

        public void setMainPictureAr(String mainPictureAr) {
            this.mainPictureAr = mainPictureAr;
        }

        public String getRuleTitleEn() {
            return ruleTitleEn;
        }

        public void setRuleTitleEn(String ruleTitleEn) {
            this.ruleTitleEn = ruleTitleEn;
        }

        public String getRuleTitleAr() {
            return ruleTitleAr;
        }

        public void setRuleTitleAr(String ruleTitleAr) {
            this.ruleTitleAr = ruleTitleAr;
        }

        public String getAcExplainEn() {
            return acExplainEn;
        }

        public void setAcExplainEn(String acExplainEn) {
            this.acExplainEn = acExplainEn;
        }

        public String getAcExplainAr() {
            return acExplainAr;
        }

        public void setAcExplainAr(String acExplainAr) {
            this.acExplainAr = acExplainAr;
        }

        public String getAcPersonalData() {
            return acPersonalData;
        }

        public void setAcPersonalData(String acPersonalData) {
            this.acPersonalData = acPersonalData;
        }

        public String getTitleColor() {
            return titleColor;
        }

        public void setTitleColor(String titleColor) {
            this.titleColor = titleColor;
        }

        public String getCountDownColor() {
            return countDownColor;
        }

        public void setCountDownColor(String countDownColor) {
            this.countDownColor = countDownColor;
        }

        public String getTextColor() {
            return textColor;
        }

        public void setTextColor(String textColor) {
            this.textColor = textColor;
        }

        public String getUserDataColor() {
            return userDataColor;
        }

        public void setUserDataColor(String userDataColor) {
            this.userDataColor = userDataColor;
        }

        public String getBtnCssUrl() {
            return btnCssUrl;
        }

        public void setBtnCssUrl(String btnCssUrl) {
            this.btnCssUrl = btnCssUrl;
        }

        public String getPagePictureEn() {
            return pagePictureEn;
        }

        public void setPagePictureEn(String pagePictureEn) {
            this.pagePictureEn = pagePictureEn;
        }

        public String getPagePictureAr() {
            return pagePictureAr;
        }

        public void setPagePictureAr(String pagePictureAr) {
            this.pagePictureAr = pagePictureAr;
        }

        public int getHasGradeReward() {
            return hasGradeReward;
        }

        public void setHasGradeReward(int hasGradeReward) {
            this.hasGradeReward = hasGradeReward;
        }

        public int getNoRankReward() {
            return noRankReward;
        }

        public void setNoRankReward(int noRankReward) {
            this.noRankReward = noRankReward;
        }

        public String getPageColor() {
            return pageColor;
        }

        public void setPageColor(String pageColor) {
            this.pageColor = pageColor;
        }

        public int getRulesPopup() {
            return rulesPopup;
        }

        public void setRulesPopup(int rulesPopup) {
            this.rulesPopup = rulesPopup;
        }

        public String getRulesButtonEn() {
            return rulesButtonEn;
        }

        public void setRulesButtonEn(String rulesButtonEn) {
            this.rulesButtonEn = rulesButtonEn;
        }

        public String getRulesButtonAr() {
            return rulesButtonAr;
        }

        public void setRulesButtonAr(String rulesButtonAr) {
            this.rulesButtonAr = rulesButtonAr;
        }

        public String getRulesPageEn() {
            return rulesPageEn;
        }

        public void setRulesPageEn(String rulesPageEn) {
            this.rulesPageEn = rulesPageEn;
        }

        public String getRulesPageAr() {
            return rulesPageAr;
        }

        public void setRulesPageAr(String rulesPageAr) {
            this.rulesPageAr = rulesPageAr;
        }
    }


    public static class ConquerConfig{
        private Integer roomPage; // 房间按钮
        private String titleEn; // 标题
        private String titleAr; // 标题
        private String titleExplanationEn; // 标题说明英语
        private String titleExplanationAr; // 标题说明阿语
        private List<BackgroundConfig> BackgroundList; // 房间背景图列表

        public static class BackgroundConfig {
            private String backgroundImgEn;  // 房间背景图英语
            private String backgroundImgAr;  // 房间背景图阿语
            private String backgroundExplanationEn;  // 房间背景图英语说明
            private String backgroundExplanationAr;  // 房间背景图阿语说明

            public String getBackgroundImgEn() {
                return backgroundImgEn;
            }

            public void setBackgroundImgEn(String backgroundImgEn) {
                this.backgroundImgEn = backgroundImgEn;
            }

            public String getBackgroundImgAr() {
                return backgroundImgAr;
            }

            public void setBackgroundImgAr(String backgroundImgAr) {
                this.backgroundImgAr = backgroundImgAr;
            }

            public String getBackgroundExplanationEn() {
                return backgroundExplanationEn;
            }

            public void setBackgroundExplanationEn(String backgroundExplanationEn) {
                this.backgroundExplanationEn = backgroundExplanationEn;
            }

            public String getBackgroundExplanationAr() {
                return backgroundExplanationAr;
            }

            public void setBackgroundExplanationAr(String backgroundExplanationAr) {
                this.backgroundExplanationAr = backgroundExplanationAr;
            }
        }


        public Integer getRoomPage() {
            return roomPage;
        }

        public void setRoomPage(Integer roomPage) {
            this.roomPage = roomPage;
        }

        public String getTitleEn() {
            return titleEn;
        }

        public void setTitleEn(String titleEn) {
            this.titleEn = titleEn;
        }

        public String getTitleAr() {
            return titleAr;
        }

        public void setTitleAr(String titleAr) {
            this.titleAr = titleAr;
        }

        public String getTitleExplanationEn() {
            return titleExplanationEn;
        }

        public void setTitleExplanationEn(String titleExplanationEn) {
            this.titleExplanationEn = titleExplanationEn;
        }

        public String getTitleExplanationAr() {
            return titleExplanationAr;
        }

        public void setTitleExplanationAr(String titleExplanationAr) {
            this.titleExplanationAr = titleExplanationAr;
        }

        public List<BackgroundConfig> getBackgroundList() {
            return BackgroundList;
        }

        public void setBackgroundList(List<BackgroundConfig> backgroundList) {
            BackgroundList = backgroundList;
        }
    }

    public static class ActivityGift {
        private Integer giftId; // 活动礼物id
        private String giftName; // 活动礼物名称
        private String giftNameAr; // 阿语活动礼物名称
        private Integer giftPrice; // 活动礼物价格
        private String giftPictureUrl; // 活动礼物图片地址

        public Integer getGiftId() {
            return giftId;
        }

        public void setGiftId(Integer giftId) {
            this.giftId = giftId;
        }

        public String getGiftName() {
            return giftName;
        }

        public void setGiftName(String giftName) {
            this.giftName = giftName;
        }

        public String getGiftNameAr() {
            return giftNameAr;
        }

        public void setGiftNameAr(String giftNameAr) {
            this.giftNameAr = giftNameAr;
        }

        public Integer getGiftPrice() {
            return giftPrice;
        }

        public void setGiftPrice(Integer giftPrice) {
            this.giftPrice = giftPrice;
        }

        public String getGiftPictureUrl() {
            return giftPictureUrl;
        }

        public void setGiftPictureUrl(String giftPictureUrl) {
            this.giftPictureUrl = giftPictureUrl;
        }
    }

    public static class RankingConfig {
        private String rankingTitleEn; // 英文标题
        private String rankingTitleAr; // 阿语标题
        private String topOneEn; // top1
        private String topOneAr; // top1
        private String topTwoEn; // top2
        private String topTwoAr; // top2
        private String topThreeEn; // top3
        private String topThreeAr; // top3
        private String rankingAwardEn; // 英文奖励说明
        private String rankingAwardAr; // 阿语奖励说明
        private String rankingImageEn; // 英文排行榜图片
        private String rankingImageAr; // 阿语排行榜图片
        private Integer rankingAttribute; // 排行榜属性 1发送者 2接收者 3房间 4征服房间数+礼物发送钻数
        private Integer giftId; // 活动的礼物id（如果此值设置，将以此作为排行榜依据，如果未设置，使用activityGiftList中的礼物来统计）
        private boolean associateUser; // 是否关联用户，关联用户时需要统计，如果是发送榜，则是接收数据，如果是接收榜，则是发送数据，如果是房间榜，则是消耗数据
        private int rankingGender; //  排行榜展示对象：0全体用户 1男性用户 2女性用户
        private int associateGender; // 关联用户限制：0全体用户 1男性用户 2女性用户
        private String rankingExplainEn; // 英文说明
        private String rankingExplainAr; // 阿语说明
        private List<RankingRewardConfig> rankingRewardConfigList; // 排行榜奖励配置
        private int supportRoomUser;// 展示每个房间的top3贡献者 0 不展示 1展示

        public String getRankingTitleEn() {
            return rankingTitleEn;
        }

        public void setRankingTitleEn(String rankingTitleEn) {
            this.rankingTitleEn = rankingTitleEn;
        }

        public String getRankingTitleAr() {
            return rankingTitleAr;
        }

        public void setRankingTitleAr(String rankingTitleAr) {
            this.rankingTitleAr = rankingTitleAr;
        }

        public String getTopOneEn() {
            return topOneEn;
        }

        public void setTopOneEn(String topOneEn) {
            this.topOneEn = topOneEn;
        }

        public String getTopOneAr() {
            return topOneAr;
        }

        public void setTopOneAr(String topOneAr) {
            this.topOneAr = topOneAr;
        }

        public String getTopTwoEn() {
            return topTwoEn;
        }

        public void setTopTwoEn(String topTwoEn) {
            this.topTwoEn = topTwoEn;
        }

        public String getTopTwoAr() {
            return topTwoAr;
        }

        public void setTopTwoAr(String topTwoAr) {
            this.topTwoAr = topTwoAr;
        }

        public String getTopThreeEn() {
            return topThreeEn;
        }

        public void setTopThreeEn(String topThreeEn) {
            this.topThreeEn = topThreeEn;
        }

        public String getTopThreeAr() {
            return topThreeAr;
        }

        public void setTopThreeAr(String topThreeAr) {
            this.topThreeAr = topThreeAr;
        }

        public String getRankingAwardEn() {
            return rankingAwardEn;
        }

        public void setRankingAwardEn(String rankingAwardEn) {
            this.rankingAwardEn = rankingAwardEn;
        }

        public String getRankingAwardAr() {
            return rankingAwardAr;
        }

        public void setRankingAwardAr(String rankingAwardAr) {
            this.rankingAwardAr = rankingAwardAr;
        }

        public String getRankingImageEn() {
            return rankingImageEn;
        }

        public void setRankingImageEn(String rankingImageEn) {
            this.rankingImageEn = rankingImageEn;
        }

        public String getRankingImageAr() {
            return rankingImageAr;
        }

        public void setRankingImageAr(String rankingImageAr) {
            this.rankingImageAr = rankingImageAr;
        }

        public Integer getRankingAttribute() {
            return rankingAttribute;
        }

        public void setRankingAttribute(Integer rankingAttribute) {
            this.rankingAttribute = rankingAttribute;
        }

        public boolean isAssociateUser() {
            return associateUser;
        }

        public void setAssociateUser(boolean associateUser) {
            this.associateUser = associateUser;
        }

        public Integer getGiftId() {
            return giftId;
        }

        public void setGiftId(Integer giftId) {
            this.giftId = giftId;
        }

        public String getRankingExplainEn() {
            return rankingExplainEn;
        }

        public void setRankingExplainEn(String rankingExplainEn) {
            this.rankingExplainEn = rankingExplainEn;
        }

        public String getRankingExplainAr() {
            return rankingExplainAr;
        }

        public void setRankingExplainAr(String rankingExplainAr) {
            this.rankingExplainAr = rankingExplainAr;
        }

        public List<RankingRewardConfig> getRankingRewardConfigList() {
            return rankingRewardConfigList;
        }

        public void setRankingRewardConfigList(List<RankingRewardConfig> rankingRewardConfigList) {
            this.rankingRewardConfigList = rankingRewardConfigList;
        }

        public int getRankingGender() {
            return rankingGender;
        }

        public void setRankingGender(int rankingGender) {
            this.rankingGender = rankingGender;
        }

        public int getAssociateGender() {
            return associateGender;
        }

        public void setAssociateGender(int associateGender) {
            this.associateGender = associateGender;
        }

        public int getSupportRoomUser() {
            return supportRoomUser;
        }

        public void setSupportRoomUser(int supportRoomUser) {
            this.supportRoomUser = supportRoomUser;
        }
    }

    public static class RankingRewardConfig {
        private List<Integer> rewardObject; // 奖励对象, [1],[2],[3],[4,5,6]...
        private String rewardResourceKey; // 奖励资源key
        private String rewardTitleEn; // 英语奖励标题
        private String rewardTitleAr; // 阿语奖励标题
        private List<RewardConfigDetail> rewardConfigDetailList; // 奖励配置

        public List<Integer> getRewardObject() {
            return rewardObject;
        }

        public void setRewardObject(List<Integer> rewardObject) {
            this.rewardObject = rewardObject;
        }

        public String getRewardResourceKey() {
            return rewardResourceKey;
        }

        public void setRewardResourceKey(String rewardResourceKey) {
            this.rewardResourceKey = rewardResourceKey;
        }

        public String getRewardTitleEn() {
            return rewardTitleEn;
        }

        public void setRewardTitleEn(String rewardTitleEn) {
            this.rewardTitleEn = rewardTitleEn;
        }

        public String getRewardTitleAr() {
            return rewardTitleAr;
        }

        public void setRewardTitleAr(String rewardTitleAr) {
            this.rewardTitleAr = rewardTitleAr;
        }

        public List<RewardConfigDetail> getRewardConfigDetailList() {
            return rewardConfigDetailList;
        }

        public void setRewardConfigDetailList(List<RewardConfigDetail> rewardConfigDetailList) {
            this.rewardConfigDetailList = rewardConfigDetailList;
        }
    }

    public static class RewardConfigDetail {
        // 资源类型 背包礼物:gift 麦位框:mic 气泡框:buddle 入场动画:ride 麦位声波:ripple 钻石:diamond 勋章:badge 浮屏:float_screen 钻石:diamond
        private String rewardType;
        private int sourceId; // 奖励资源id
        private String rewardIcon; // 奖励资源介绍图片
        private int rewardTime; //资源时长（天） 0永久
        private int rewardNum; // 礼物数量 可能为空
        private String otherRewardUrl; // 其他其他奖励图片url
        private String otherRewardRemark; // 其他奖励备注
        private String otherRewardRemarkAr; // 阿语其他奖励备注
        // web配置和业务无关
        private Integer rewardTimes;
        private Boolean disabled;
        private String preview;

        public String getRewardType() {
            return rewardType;
        }

        public void setRewardType(String rewardType) {
            this.rewardType = rewardType;
        }

        public int getSourceId() {
            return sourceId;
        }

        public void setSourceId(int sourceId) {
            this.sourceId = sourceId;
        }

        public String getRewardIcon() {
            return rewardIcon;
        }

        public void setRewardIcon(String rewardIcon) {
            this.rewardIcon = rewardIcon;
        }

        public int getRewardTime() {
            return rewardTime;
        }

        public void setRewardTime(int rewardTime) {
            this.rewardTime = rewardTime;
        }

        public int getRewardNum() {
            return rewardNum;
        }

        public void setRewardNum(int rewardNum) {
            this.rewardNum = rewardNum;
        }

        public String getOtherRewardUrl() {
            return otherRewardUrl;
        }

        public void setOtherRewardUrl(String otherRewardUrl) {
            this.otherRewardUrl = otherRewardUrl;
        }

        public String getOtherRewardRemark() {
            return otherRewardRemark;
        }

        public void setOtherRewardRemark(String otherRewardRemark) {
            this.otherRewardRemark = otherRewardRemark;
        }

        public String getOtherRewardRemarkAr() {
            return otherRewardRemarkAr;
        }

        public void setOtherRewardRemarkAr(String otherRewardRemarkAr) {
            this.otherRewardRemarkAr = otherRewardRemarkAr;
        }

        public Integer getRewardTimes() {
            return rewardTimes;
        }

        public void setRewardTimes(Integer rewardTimes) {
            this.rewardTimes = rewardTimes;
        }

        public Boolean getDisabled() {
            return disabled;
        }

        public void setDisabled(Boolean disabled) {
            this.disabled = disabled;
        }

        public String getPreview() {
            return preview;
        }

        public void setPreview(String preview) {
            this.preview = preview;
        }
    }

    public static class ReachingReward {
        private Integer giftNum; // 礼物数量(礼物钻石总数)
        private List<RewardConfigDetail> rewardConfigDetailList; // 奖励配置

        public Integer getGiftNum() {
            return giftNum;
        }

        public void setGiftNum(Integer giftNum) {
            this.giftNum = giftNum;
        }

        public List<RewardConfigDetail> getRewardConfigDetailList() {
            return rewardConfigDetailList;
        }

        public void setRewardConfigDetailList(List<RewardConfigDetail> rewardConfigDetailList) {
            this.rewardConfigDetailList = rewardConfigDetailList;
        }
    }

    public static class ReachingConfig {
        private String titleEn; // 英文标题
        private String titleAr; // 阿语标题
        private String explainEn; // 英文说明
        private String explainAr; // 阿语说明
        private Integer reachingRewardType; // 等级奖励属性 1发送者 2接收者 3征服活动
        private int reachingGender; // 奖励对象 0全体用户 1男性用户 2女性用户
        private int showType; // 奖励展示类型，0横板(旧版本，为0时，reachingRewardList中的rewardConfigDetailList只能单选)，1竖版
        private List<Integer> giftIdList; // 活动礼物id列表
        private Integer calculateMethod; // 计算方式 1计算礼物数 2计算钻石数
        private List<ReachingReward> reachingRewardList; // 新版奖励配置，支持多种类型的奖励

        public String getTitleEn() {
            return titleEn;
        }

        public void setTitleEn(String titleEn) {
            this.titleEn = titleEn;
        }

        public String getTitleAr() {
            return titleAr;
        }

        public void setTitleAr(String titleAr) {
            this.titleAr = titleAr;
        }

        public String getExplainEn() {
            return explainEn;
        }

        public void setExplainEn(String explainEn) {
            this.explainEn = explainEn;
        }

        public String getExplainAr() {
            return explainAr;
        }

        public void setExplainAr(String explainAr) {
            this.explainAr = explainAr;
        }

        public Integer getReachingRewardType() {
            return reachingRewardType;
        }

        public void setReachingRewardType(Integer reachingRewardType) {
            this.reachingRewardType = reachingRewardType;
        }

        public Integer getReachingGender() {
            return reachingGender;
        }

        public void setReachingGender(int reachingGender) {
            this.reachingGender = reachingGender;
        }

        public List<Integer> getGiftIdList() {
            return giftIdList;
        }

        public void setGiftIdList(List<Integer> giftIdList) {
            this.giftIdList = giftIdList;
        }

        public Integer getCalculateMethod() {
            return calculateMethod;
        }

        public void setCalculateMethod(Integer calculateMethod) {
            this.calculateMethod = calculateMethod;
        }

        public int getShowType() {
            return showType;
        }

        public void setShowType(int showType) {
            this.showType = showType;
        }

        public List<ReachingReward> getReachingRewardList() {
            return reachingRewardList;
        }

        public void setReachingRewardList(List<ReachingReward> reachingRewardList) {
            this.reachingRewardList = reachingRewardList;
        }
    }

    public static class BadgeRewardConfig {
        private Integer badgeId; // 勋章id
        private Integer giftNum; // 礼物数量(礼物钻石总数)
        private String rewardIcon; // 奖励资源介绍图片


        public Integer getBadgeId() {
            return badgeId;
        }

        public void setBadgeId(Integer badgeId) {
            this.badgeId = badgeId;
        }

        public Integer getGiftNum() {
            return giftNum;
        }

        public void setGiftNum(Integer giftNum) {
            this.giftNum = giftNum;
        }

        public String getRewardIcon() {
            return rewardIcon;
        }

        public void setRewardIcon(String rewardIcon) {
            this.rewardIcon = rewardIcon;
        }
    }

    public ObjectId get_id() {
        return _id;
    }

    public void set_id(ObjectId _id) {
        this._id = _id;
    }

    public String getAcNameEn() {
        return acNameEn;
    }

    public void setAcNameEn(String acNameEn) {
        this.acNameEn = acNameEn;
    }

    public String getAcNameAr() {
        return acNameAr;
    }

    public void setAcNameAr(String acNameAr) {
        this.acNameAr = acNameAr;
    }

    public String getAcUrl() {
        return acUrl;
    }

    public void setAcUrl(String acUrl) {
        this.acUrl = acUrl;
    }

    public Integer getStartTime() {
        return startTime;
    }

    public void setStartTime(Integer startTime) {
        this.startTime = startTime;
    }

    public Integer getEndTime() {
        return endTime;
    }

    public void setEndTime(Integer endTime) {
        this.endTime = endTime;
    }

    public ActivityConfig getConfig() {
        return config;
    }

    public void setConfig(ActivityConfig config) {
        this.config = config;
    }

    public ConquerConfig getConquerConfig() {
        return conquerConfig;
    }

    public void setConquerConfig(ConquerConfig conquerConfig) {
        this.conquerConfig = conquerConfig;
    }

    public List<ActivityGift> getActivityGiftList() {
        return activityGiftList;
    }

    public void setActivityGiftList(List<ActivityGift> activityGiftList) {
        this.activityGiftList = activityGiftList;
    }

    public List<RankingConfig> getRankingConfigList() {
        return rankingConfigList;
    }

    public void setRankingConfigList(List<RankingConfig> rankingConfigList) {
        this.rankingConfigList = rankingConfigList;
    }

    public List<ReachingConfig> getReachingConfigList() {
        return reachingConfigList;
    }

    public void setReachingConfigList(List<ReachingConfig> reachingConfigList) {
        this.reachingConfigList = reachingConfigList;
    }

    public Integer getTemplateType() {
        return templateType;
    }

    public void setTemplateType(Integer templateType) {
        this.templateType = templateType;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }

    public Integer getMtime() {
        return mtime;
    }

    public void setMtime(Integer mtime) {
        this.mtime = mtime;
    }
}

package com.quhong.mongo.data;

import com.quhong.mongo.dao.FriendsNumDao;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 好友数
 * <AUTHOR>
 * @date 2022/5/25
 */
@Document(collection = FriendsNumDao.TABLE_NAME)
public class FriendsNumData implements Serializable {

    private static final long serialVersionUID = -3187891797885030003L;
    @Id
    private ObjectId _id;
    private String uid;
    private Integer friends;
    @Field("new_friends")
    private Integer newFriends;
    @Field("new_friends_list")
    private List<String> newFriendsList = new ArrayList<>();
    private Integer like;
    @Field("new_like")
    private Integer newLike;
    @Field("new_like_list")
    private List<String> newLikeList = new ArrayList<>();

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public Integer getFriends() {
        return friends;
    }

    public void setFriends(Integer friends) {
        this.friends = friends;
    }

    public Integer getNewFriends() {
        return newFriends;
    }

    public void setNewFriends(Integer newFriends) {
        this.newFriends = newFriends;
    }

    public List<String> getNewFriendsList() {
        return newFriendsList;
    }

    public void setNewFriendsList(List<String> newFriendsList) {
        this.newFriendsList = newFriendsList;
    }

    public Integer getLike() {
        return like;
    }

    public void setLike(Integer like) {
        this.like = like;
    }

    public Integer getNewLike() {
        return newLike;
    }

    public void setNewLike(Integer newLike) {
        this.newLike = newLike;
    }

    public List<String> getNewLikeList() {
        return newLikeList;
    }

    public void setNewLikeList(List<String> newLikeList) {
        this.newLikeList = newLikeList;
    }

    @Override
    public String toString() {
        return "FriendsNumData{" +
                "uid='" + uid + '\'' +
                ", friends=" + friends +
                ", newFriends=" + newFriends +
                ", newFriendsList=" + newFriendsList +
                ", like=" + like +
                ", newLike=" + newLike +
                ", newLikeList=" + newLikeList +
                '}';
    }
}

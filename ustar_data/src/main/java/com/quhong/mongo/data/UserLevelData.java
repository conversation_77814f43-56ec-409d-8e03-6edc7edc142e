package com.quhong.mongo.data;

import com.quhong.mongo.dao.UserLevelDao;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.ArrayList;
import java.util.List;

@Document(collection = UserLevelDao.TABLE_NAME)
public class UserLevelData {
    @Id
    private ObjectId _id;
    private String uid;
    private int level;
    private int exp;
    /**
     * 记录上次领取福利的等级，确保只发放一次
     */
    private int last_benefit_level;
    // 新版用户等级
    private long new_exp;
    private long active_exp;// 活跃值
    private long charm_exp;//魅力值
    private long wealth_exp;//财富值
    @Field("reached")
    private List<String> reached = new ArrayList<>();

    public UserLevelData() {

    }

    public ObjectId get_id() {
        return _id;
    }

    public void set_id(ObjectId _id) {
        this._id = _id;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public int getLevel() {
        return level;
    }

    public void setLevel(int level) {
        this.level = level;
    }

    public int getExp() {
        return exp;
    }

    public void setExp(int exp) {
        this.exp = exp;
    }

    public int getLast_benefit_level() {
        return last_benefit_level;
    }

    public void setLast_benefit_level(int last_benefit_level) {
        this.last_benefit_level = last_benefit_level;
    }

    public long getNew_exp() {
        return new_exp;
    }

    public void setNew_exp(long new_exp) {
        this.new_exp = new_exp;
    }

    public long getActive_exp() {
        return active_exp;
    }

    public void setActive_exp(long active_exp) {
        this.active_exp = active_exp;
    }

    public long getCharm_exp() {
        return charm_exp;
    }

    public void setCharm_exp(long charm_exp) {
        this.charm_exp = charm_exp;
    }

    public long getWealth_exp() {
        return wealth_exp;
    }

    public void setWealth_exp(long wealth_exp) {
        this.wealth_exp = wealth_exp;
    }

    public void setWealth_exp(int wealth_exp) {
        this.wealth_exp = wealth_exp;
    }

    public List<String> getReached() {
        return reached;
    }

    public void setReached(List<String> reached) {
        this.reached = reached;
    }
}

package com.quhong.mongo.data;

import com.quhong.data.*;
import com.quhong.mongo.dao.ActorDao;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Document(collection = ActorDao.TABLE_NAME)
public class MongoActorData {
    public static final Logger logger = LoggerFactory.getLogger(MongoActorData.class);
    @Id
    private ObjectId _id;
    private int beans;
    private int gold;
    private int rid;
    private int originalRid; // 原始rid
    @Field("alpha_rid")
    private String alphaRid; // 靓号
    private int alphaLevel; // 靓号等级
    private String name;
    private int gender;
    private int fb_gender;
    private String head;
    private String os;
    private int ride_option;
    private String video_option;
    private int age;
    private String country;
    private int slang;
    private String channel; // 华为渠道专属字段
    @Field("app_package_name")
    private String appPackageName; // 华为渠道专属字段
    @Field("is_face")
    private int isFace; // 1真人
    private int valid; // 1有效 其他被封
    @Field("label_list")
    private List<Integer> labelList = new ArrayList<>();
    private String ip;
    private String ipCodeCountry; // 国家码及国家
    @Field("account_status")
    private int accountStatus;  // 0:正常账号  1：已标记删除，30天内删除  2：账号已删除，无法登录
    private Map<String, Object> push;
    private Map<String, Object> general_conf;
    @Field("login_type")
    private int loginType;
    @Field("heart_got")
    private int heartGot;
    private String idfa;
    private String birthday;
    private String android_id;
    private int vchat_status;
    private String promotion_id;
    private String tn_id;
    private String distinct_id;
    private Map<String, Object> last_login;
    private int familyId; // 公会id，主键id
    private String firstTnId; // 新账号的新设备id（仅新注册账号且为新设备时才会写入）
    private int version_code;
    private String version_name;

    public MongoActorData() {

    }

    public RidData getRidData() {
        if (ObjectUtils.isEmpty(alphaRid)) {
            return new RidData(String.valueOf(rid), 0);
        }
        return new RidData(alphaRid, alphaLevel);
    }

    public String getStrRid() {
        if (ObjectUtils.isEmpty(alphaRid)) {
            return String.valueOf(rid);
        }
        return alphaRid;
    }

    public void copyTo(ActorData actorData) {
        actorData.setUid(_id.toString());
        actorData.setBeans(beans);
        actorData.setGold(gold);
        actorData.setRid(rid);
        actorData.setOriginalRid(originalRid);
        actorData.setAlphaRid(alphaRid);
        actorData.setAlphaLevel(alphaLevel);
        actorData.setName(name);
        actorData.setGender(gender);
        actorData.setFb_gender(fb_gender);
        actorData.setHead(head);
        actorData.setOs(os);
        actorData.setRide_option(ride_option);
        actorData.setVideo_option(video_option);
        actorData.setAge(age);
        actorData.setCountry(country);
        actorData.setSlang(slang);
        actorData.setChannel(channel);
        actorData.setIsFace(isFace);
        actorData.setAppPackageName(appPackageName);
        actorData.setValid(valid);
        actorData.setLabelList(labelList);
        actorData.setIp(ip);
        actorData.setIpCodeCountry(ipCodeCountry);
        actorData.setAccountStatus(accountStatus);
        actorData.setLoginType(loginType);
        actorData.setHeartGot(heartGot);
        actorData.setIdfa(idfa);
        actorData.setBirthday(birthday);
        actorData.setAndroid_id(android_id);
        actorData.setVchat_status(vchat_status);
        actorData.setPromotion_id(promotion_id);
        GeneralConfActorData generalConfActorData = new GeneralConfActorData();
        generalConfActorData.setSayHelloSwitch(getHelloSwitch());
        actorData.setGeneralConfActorData(generalConfActorData);
        ActorPushData actorPushData = buildActorPushData();
        actorData.setPush(actorPushData);
        actorData.setTn_id(tn_id);
        actorData.setLastLogin(buildActorLastLogin());
        actorData.setFamilyId(familyId);
        actorData.setFirstTnId(firstTnId);
        actorData.setVersion_code(version_code);
        actorData.setVersion_name(version_name);
    }

    public ObjectId get_id() {
        return _id;
    }

    public void set_id(ObjectId _id) {
        this._id = _id;
    }

    public int getBeans() {
        return beans;
    }

    public void setBeans(int beans) {
        this.beans = beans;
    }

    public int getRid() {
        return rid;
    }

    public void setRid(int rid) {
        this.rid = rid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getGender() {
        return gender;
    }

    public void setGender(int gender) {
        this.gender = gender;
    }

    public int getFb_gender() {
        return fb_gender;
    }

    public void setFb_gender(int fb_gender) {
        this.fb_gender = fb_gender;
    }

    public String getHead() {
        return head;
    }

    public void setHead(String head) {
        this.head = head;
    }

    public String getOs() {
        return os;
    }

    public void setOs(String os) {
        this.os = os;
    }

    public int getRide_option() {
        return ride_option;
    }

    public void setRide_option(int ride_option) {
        this.ride_option = ride_option;
    }

    public int getAge() {
        return age;
    }

    public void setAge(int age) {
        this.age = age;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public int getSlang() {
        return slang;
    }

    public void setSlang(int slang) {
        this.slang = slang;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public int getIsFace() {
        return isFace;
    }

    public void setIsFace(int isFace) {
        this.isFace = isFace;
    }

    public String getAppPackageName() {
        return appPackageName;
    }

    public void setAppPackageName(String appPackageName) {
        this.appPackageName = appPackageName;
    }

    public int getValid() {
        return valid;
    }

    public void setValid(int valid) {
        this.valid = valid;
    }

    public List<Integer> getLabelList() {
        return labelList;
    }

    public void setLabelList(List<Integer> labelList) {
        this.labelList = labelList;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getIpCodeCountry() {
        return ipCodeCountry;
    }

    public void setIpCodeCountry(String ipCodeCountry) {
        this.ipCodeCountry = ipCodeCountry;
    }

    public int getAccountStatus() {
        return accountStatus;
    }

    public void setAccountStatus(int accountStatus) {
        this.accountStatus = accountStatus;
    }

    public Map<String, Object> getGeneral_conf() {
        return general_conf;
    }

    public void setGeneral_conf(Map<String, Object> general_conf) {
        this.general_conf = general_conf;
    }

    public int getLoginType() {
        return loginType;
    }

    public void setLoginType(int loginType) {
        this.loginType = loginType;
    }

    public int getHeartGot() {
        return heartGot;
    }

    public void setHeartGot(int heartGot) {
        this.heartGot = heartGot;
    }

    public String getIdfa() {
        return idfa;
    }

    public void setIdfa(String idfa) {
        this.idfa = idfa;
    }

    public String getBirthday() {
        return birthday;
    }

    public void setBirthday(String birthday) {
        this.birthday = birthday;
    }

    public String getAndroid_id() {
        return android_id;
    }

    public void setAndroid_id(String android_id) {
        this.android_id = android_id;
    }

    public int getVchat_status() {
        return vchat_status;
    }

    public void setVchat_status(int vchat_status) {
        this.vchat_status = vchat_status;
    }

    public String getPromotion_id() {
        return promotion_id;
    }

    public void setPromotion_id(String promotion_id) {
        this.promotion_id = promotion_id;
    }

    public Map<String, Object> getPush() {
        return push;
    }

    public void setPush(Map<String, Object> push) {
        this.push = push;
    }

    private int getHelloSwitch() {
        if (null == general_conf || null == general_conf.get("sayHelloSwitch")) {
            return 0;
        }
        try {
            Number sayHelloSwitch = (Number) general_conf.get("sayHelloSwitch");
            return sayHelloSwitch.intValue();
        } catch (Exception e) {
            logger.error("getHelloSwitch error. msg= {}", e.getMessage(), e);
            return 0;
        }
    }

    private Integer getIntValueFromPush(String key) {
        if (null == push || null == push.get(key)) {
            return null;
        }
        try {
            Number nkey = (Number) push.get(key);
            return nkey.intValue();
//            return (int) push.get(key);
        } catch (Exception e) {
            logger.error("get int value {} from push error. msg={}", key, e.getMessage(), e);
            return null;
        }
    }

    private Long getLongValueFromLastLogin(String key) {
        if (null == last_login || null == last_login.get(key)) {
            return null;
        }
        try {
            Number nkey = (Number) last_login.get(key);
            return nkey.longValue();
//            return (int) push.get(key);
        } catch (Exception e) {
            logger.error("get long value {} from last_login error. msg={}", key, e.getMessage(), e);
            return null;
        }
    }

    public int getFamilyId() {
        return familyId;
    }

    public void setFamilyId(int familyId) {
        this.familyId = familyId;
    }

    private ActorPushData buildActorPushData() {
        ActorPushData data = null;
        if (!CollectionUtils.isEmpty(push)) {
            data = new ActorPushData();
            data.setVlog(getIntValueFromPush("vlog"));
            data.setOnline(getIntValueFromPush("online"));
            data.setComments(getIntValueFromPush("comments"));
            data.setFollow(getIntValueFromPush("follow"));
            data.setMessage(getIntValueFromPush("message"));
            data.setMisscall(getIntValueFromPush("misscall"));
            data.setVibrator(getIntValueFromPush("vibrator"));
            data.setLive(getIntValueFromPush("live"));
            data.setRoom_live(getIntValueFromPush("room_live"));
            data.setPrivate_message(getIntValueFromPush("private_message"));
            data.setSystem_switch(getIntValueFromPush("system_switch"));
            data.setToken((String) push.get("token"));
        }
        return data;
    }

    private ActorLastLoginData buildActorLastLogin() {
        ActorLastLoginData data = null;
        if (!CollectionUtils.isEmpty(last_login)) {
            data = new ActorLastLoginData();
            Long status = getLongValueFromLastLogin("status");
            data.setStatus(Math.toIntExact(status == null ? 0 : status));
            data.setLoginTime(getLongValueFromLastLogin("login_time"));
            data.setLogoutTime(getLongValueFromLastLogin("logout_time"));
        }
        return data;
    }

    public String getVideo_option() {
        return video_option;
    }

    public void setVideo_option(String video_option) {
        this.video_option = video_option;
    }

    public String getTn_id() {
        return tn_id;
    }

    public void setTn_id(String tn_id) {
        this.tn_id = tn_id;
    }

    public int getGold() {
        return gold;
    }

    public void setGold(int gold) {
        this.gold = gold;
    }

    public String getDistinct_id() {
        return distinct_id;
    }

    public void setDistinct_id(String distinct_id) {
        this.distinct_id = distinct_id;
    }

    public int getOriginalRid() {
        return originalRid;
    }

    public void setOriginalRid(int originalRid) {
        this.originalRid = originalRid;
    }

    public String getAlphaRid() {
        return alphaRid;
    }

    public void setAlphaRid(String alphaRid) {
        this.alphaRid = alphaRid;
    }

    public int getAlphaLevel() {
        return alphaLevel;
    }

    public void setAlphaLevel(int alphaLevel) {
        this.alphaLevel = alphaLevel;
    }

    public Map<String, Object> getLast_login() {
        return last_login;
    }

    public void setLast_login(Map<String, Object> last_login) {
        this.last_login = last_login;
    }

    public String getFirstTnId() {
        return firstTnId;
    }

    public void setFirstTnId(String firstTnId) {
        this.firstTnId = firstTnId;
    }

    public int getVersion_code() {
        return version_code;
    }

    public void setVersion_code(int version_code) {
        this.version_code = version_code;
    }

    public String getVersion_name() {
        return version_name;
    }

    public void setVersion_name(String version_name) {
        this.version_name = version_name;
    }
}

package com.quhong.mongo.data;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Map;

@Document(collection = "sys_config")
public class SysConfigData {

    @Id
    private String key;

    private Map<String,Object> section;

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public Map<String, Object> getSection() {
        return section;
    }

    public void setSection(Map<String, Object> section) {
        this.section = section;
    }

    @Override
    public String toString() {
        return "SysConfigData{" +
                "key='" + key + '\'' +
                ", section=" + section +
                '}';
    }
}

package com.quhong.mongo.dao;

import com.quhong.mongo.config.MongoBean;
import com.quhong.mongo.data.MonitorNsfwData;
import com.quhong.mongo.data.OfficialData;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Lazy
@Component
public class MonitorNsfwDao {
    private static final Logger logger = LoggerFactory.getLogger(MonitorNsfwDao.class);

    public static final String TABLE_NAME = "monitor_nsfw";

    @Resource(name = MongoBean.MOVIES)
    private MongoTemplate mongoTemplate;

    public MonitorNsfwData findData(String id) {
        try {
            if ("None".equals(id)) {
                return null;
            }
            Criteria criteria = Criteria.where("_id").is(new ObjectId(id));
            return mongoTemplate.findOne(new Query(criteria), MonitorNsfwData.class);
        } catch (Exception e) {
            logger.info("get MonitorNsfwData data error. id={} {}", id, e.getMessage(), e);
        }
        return null;
    }


    public MonitorNsfwData save(MonitorNsfwData data) {
        try {
            return mongoTemplate.save(data);
        } catch (Exception e) {
            logger.error("save MonitorNsfwData data error uid={} msg={}", data.getUid(), e.getMessage(), e);
            return null;
        }
    }

}

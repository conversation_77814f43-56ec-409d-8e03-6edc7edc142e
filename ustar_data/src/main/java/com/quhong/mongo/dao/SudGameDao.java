package com.quhong.mongo.dao;

import com.quhong.config.CaffeineCacheConfig;
import com.quhong.data.BaseInitData;
import com.quhong.enums.SudGameConstant;
import com.quhong.mongo.config.MongoBean;
import com.quhong.mongo.data.SudGameData;
import com.quhong.mongo.data.SudGamePlayerData;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2022/7/1
 */
@Component
public class SudGameDao {

    public static final Logger logger = LoggerFactory.getLogger(SudGameDao.class);
    public static final String TABLE_NAME = "sud_game";

    @Resource(name = MongoBean.MOVIES)
    private MongoTemplate mongoTemplate;
    @Resource
    protected BaseInitData baseInitData;

    public SudGameData save(SudGameData data) {
        return mongoTemplate.save(data);
    }

    public SudGameData findData(String gameId) {
        try {
            if (StringUtils.isEmpty(gameId)) {
                return null;
            }
            Criteria criteria = Criteria.where("_id").is(new ObjectId(gameId));
            return mongoTemplate.findOne(new Query(criteria), SudGameData.class);
        } catch (Exception e) {
            logger.error("get sud game data from db error. gameId={} {}", gameId, e.getMessage(), e);
        }
        return null;
    }

    public List<SudGameData> findDataByRoomId(String roomId, int limit) {
        try {
            Criteria criteria = Criteria.where("roomId").is(roomId);
            Query query = new Query(criteria);
            Sort sort = Sort.by(Sort.Direction.DESC, "createTime");
            query.with(sort).limit(limit);
            return mongoTemplate.find(query, SudGameData.class);
        } catch (Exception e) {
            logger.error("get sud game data from db error. roomId={} {}", roomId, e.getMessage(), e);
        }
        return Collections.emptyList();
    }

    public List<SudGameData> findMatchingByGameType(int gameType, int roomType) {
        try {
            Criteria criteria = Criteria.where("status").is(SudGameConstant.GAME_MATCHING);
            if (gameType != -1) {
                criteria.and("gameType").is(gameType);
            }
            if (roomType == SudGameConstant.GAME_ROOM) {
                criteria.and("gameRoomType").is(SudGameConstant.GAME_ROOM);
            } else {
                criteria.and("gameRoomType").ne(SudGameConstant.GAME_ROOM);
            }
            return mongoTemplate.find(new Query(criteria), SudGameData.class);
        } catch (Exception e) {
            logger.error("findMatchingByGameType from db error. gameType={} {}", gameType, e.getMessage(), e);
        }
        return Collections.emptyList();
    }


    public List<SudGameData> findProcessingByGameType(int gameType, int roomType) {
        try {
            Criteria criteria = Criteria.where("status").is(SudGameConstant.GAME_PROCESSING)
                    .and("gameType").is(gameType);
            if (roomType == SudGameConstant.GAME_ROOM) {
                criteria.and("gameRoomType").is(SudGameConstant.GAME_ROOM);
            } else {
                criteria.and("gameRoomType").ne(SudGameConstant.GAME_ROOM);
            }
            return mongoTemplate.find(new Query(criteria), SudGameData.class);
        } catch (Exception e) {
            logger.error("findProcessingByGameType from db error. gameType={} {}", gameType, e.getMessage(), e);
        }
        return Collections.emptyList();
    }

    /**
     * 获取最近玩游戏的玩家头像列表
     */
    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public Set<String> findRecentlyPlayer(String uid, int gameType) {
        try {
            Set<String> resultSet = new HashSet<>();
//            Criteria criteria = Criteria.where("status").in(SudGameConstant.GAME_FINISH, SudGameConstant.GAME_PROCESSING).and("gameType").is(gameType);
            Criteria criteria = Criteria.where("status").is(SudGameConstant.GAME_FINISH).and("gameType").is(gameType);
            Query query = new Query(criteria);
            Sort sort = Sort.by(Sort.Direction.DESC, "_id");
            query.with(sort).limit(5);
            List<SudGameData> gameDataList = mongoTemplate.find(query, SudGameData.class);
            for (SudGameData sudGameData : gameDataList) {
                for (SudGamePlayerData playerData : sudGameData.getPlayerList()) {
                    if (resultSet.size() <= 20 && !uid.equals(playerData.getUid())) {
                        resultSet.add(playerData.getHead());
                    }
                }
            }
            if (resultSet.size() == 0) {
                // 头像列表返回空客户端会有问题
                for (int i = 0; i < 5; i++) {
                    resultSet.add(baseInitData.generateRandomHead(2));
                }
            }
            return resultSet;
        } catch (Exception e) {
            logger.error("findMatchingByGameType from db error. gameType={} {}", gameType, e.getMessage(), e);
        }
        return Collections.emptySet();
    }


    public void batchDelete(int start, int end) {
        try {
            Criteria criteria = Criteria.where("createTime").gt(start).lte(end);
            mongoTemplate.remove(new Query(criteria), TABLE_NAME);
        } catch (Exception e) {
            logger.error("batchDelete error. start={} end={} msg:{}", start, end, e.getMessage(), e);
        }
    }

    public List<SudGameData> findGameRoomByStatus(int status) {
        try {
            Criteria criteria = Criteria.where("status").is(status);
            criteria.and("gameRoomType").is(SudGameConstant.GAME_ROOM);
            return mongoTemplate.find(new Query(criteria), SudGameData.class);
        } catch (Exception e) {
            logger.error("findGameRoomByStatus from db error. status={} {}", status, e.getMessage(), e);
        }
        return Collections.emptyList();
    }
}

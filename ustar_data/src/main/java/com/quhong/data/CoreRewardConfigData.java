package com.quhong.data;


/**
 * 基本奖励资源配置
 */
public class CoreRewardConfigData {
    private int rewardId;             // 奖品id
    private String resourceNameEn;    // 奖品名称英语
    private String resourceNameAr;    // 奖品名称阿语
    private String resourceIcon;      // 奖品图标
    private int resourceId;           // 奖品资源id
    private int resourceDebugId;           // 测试环境奖品资源id
    private int resourceType;         // 奖品类型  -1:钻石 其余type见BaseDataResourcesConstant
    private int resourceTime;         // 奖品奖励时长
    private int resourceNum;          // 奖品奖励数量
    private int orderNum;             // 排序

    public CoreRewardConfigData() {
    }

    public CoreRewardConfigData(int rewardId, String resourceNameEn, String resourceNameAr,
                                String resourceIcon, int resourceId, int resourceDebugId,
                                int resourceType, int resourceTime,
                                int resourceNum, int orderNum) {
        this.rewardId = rewardId;
        this.resourceNameEn = resourceNameEn;
        this.resourceNameAr = resourceNameAr;
        this.resourceIcon = resourceIcon;
        this.resourceId = resourceId;
        this.resourceDebugId = resourceDebugId;
        this.resourceType = resourceType;
        this.resourceTime = resourceTime;
        this.resourceNum = resourceNum;
        this.orderNum = orderNum;
    }

    public int getRewardId() {
        return rewardId;
    }

    public void setRewardId(int rewardId) {
        this.rewardId = rewardId;
    }

    public String getResourceNameEn() {
        return resourceNameEn;
    }

    public void setResourceNameEn(String resourceNameEn) {
        this.resourceNameEn = resourceNameEn;
    }

    public String getResourceNameAr() {
        return resourceNameAr;
    }

    public void setResourceNameAr(String resourceNameAr) {
        this.resourceNameAr = resourceNameAr;
    }

    public String getResourceIcon() {
        return resourceIcon;
    }

    public void setResourceIcon(String resourceIcon) {
        this.resourceIcon = resourceIcon;
    }

    public int getResourceId() {
        return resourceId;
    }

    public void setResourceId(int resourceId) {
        this.resourceId = resourceId;
    }

    public int getResourceType() {
        return resourceType;
    }

    public void setResourceType(int resourceType) {
        this.resourceType = resourceType;
    }

    public int getResourceTime() {
        return resourceTime;
    }

    public void setResourceTime(int resourceTime) {
        this.resourceTime = resourceTime;
    }

    public int getResourceNum() {
        return resourceNum;
    }

    public void setResourceNum(int resourceNum) {
        this.resourceNum = resourceNum;
    }

    public int getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(int orderNum) {
        this.orderNum = orderNum;
    }

    public int getResourceDebugId() {
        return resourceDebugId;
    }

    public void setResourceDebugId(int resourceDebugId) {
        this.resourceDebugId = resourceDebugId;
    }

    @Override
    public String toString() {
        final StringBuffer sb = new StringBuffer("CoreRewardConfigData{");
        sb.append("rewardId=").append(rewardId);
        sb.append(", resourceNameEn='").append(resourceNameEn).append('\'');
        sb.append(", resourceNameAr='").append(resourceNameAr).append('\'');
        sb.append(", resourceIcon='").append(resourceIcon).append('\'');
        sb.append(", resourceId=").append(resourceId);
        sb.append(", resourceDebugId=").append(resourceDebugId);
        sb.append(", resourceType=").append(resourceType);
        sb.append(", resourceTime=").append(resourceTime);
        sb.append(", resourceNum=").append(resourceNum);
        sb.append(", orderNum=").append(orderNum);
        sb.append('}');
        return sb.toString();
    }
}

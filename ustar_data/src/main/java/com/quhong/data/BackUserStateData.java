package com.quhong.data;


import java.util.Map;
import java.util.Set;

/**
 * 回归活动用户状态
 * 被邀请用户日活状态
 */
public class BackUserStateData {

    private String uid;

    /**
     * 回归时间
     */
    private Integer backTime;

    /**
     * 回归用户字段-活动充值的productid
     */
    private String rechargeProductId;

    /**
     * 0 待完成 1 待领取 2 已完成
     */
    private int dau2State;
    /**
     * 0 待完成 1 待领取 2 已完成
     */
    private int dau3State;

    /**
     * 0 待完成 1 待领取 2 已完成
     */
    private int dau5State;

    /**
     * 0 待完成 1 待领取 2 已完成
     */
    private int dau7State;

    /**
     * 活跃日期列表，按掉startPage接口记录日活
     */
    private Set<String> dayActiveSet;

    /**
     * 拉新用户，新版活跃日期列表,按上麦行为记录日活
     */
    private Set<String> dayNewActiveSet;

    /**
     * 回归用户字段 活动充值的productid set
     */
    private Set<String> rechargeProductIdSet;

    /**
     * 回归用户字段 回归前最后一次登出时间
     */
    private Integer lastLoginOutTime;


    /**
     * 回归用户
     * 59,299,1999,9999
     * gift1-gift4
     * <p>
     * 拉新用户
     * 500,1000,2000
     * gift1-gift3
     * 送礼价值 map 状态
     * 状态值 0 待完成 1 待领取 2 已完成
     */
    private Map<String, Integer> giftMapStatus;

    /**
     * 拉新用户字段
     * 0.99,19.99,49.99
     * dollar1-dollar3
     * 累积充值 map 状态
     */
    private Map<String, Integer> dollarsMapStatus;


    /**
     * 1,5,10,20
     * friend1-friend4
     * 朋友数 map 状态
     */
    private Map<String, Integer> friendsMapStatus;

    /**
     * 共用字段
     * 累积发送礼物价值
     */
    private int giftBeans;

    /**
     * 拉新用户字段
     * 累积充值美元
     */
    private double totalDollars;

    /**
     * 回归用户
     * 加好友列表
     */
    private Set<String> friendsSet;

    /**
     * 0 没有弹窗  1已经弹窗
     */
    private int isDoneShowPop;


    /**
     * 拉新用户字段 注册奖励领取状态
     * 0 待完成 1 待领取 2 已完成
     */
    private int regStatus;

    /**
     * 回归用户字段 完成绑定操作（自动下发）
     * 0 待完成 1 待领取 2 已完成
     */
    private int backCodeStatus;


    /**
     * 回归用户字段  与邀请者玩1局克罗姆游戏状态（自动下发）
     * 0 待完成 1 待领取 2 已完成
     */
    private int playGameStatus;


    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public Integer getBackTime() {
        return backTime;
    }

    public void setBackTime(Integer backTime) {
        this.backTime = backTime;
    }

    public String getRechargeProductId() {
        return rechargeProductId;
    }

    public void setRechargeProductId(String rechargeProductId) {
        this.rechargeProductId = rechargeProductId;
    }

    public int getDau2State() {
        return dau2State;
    }

    public void setDau2State(int dau2State) {
        this.dau2State = dau2State;
    }

    public int getDau3State() {
        return dau3State;
    }

    public void setDau3State(int dau3State) {
        this.dau3State = dau3State;
    }

    public int getDau5State() {
        return dau5State;
    }

    public void setDau5State(int dau5State) {
        this.dau5State = dau5State;
    }

    public int getDau7State() {
        return dau7State;
    }

    public void setDau7State(int dau7State) {
        this.dau7State = dau7State;
    }

    public Set<String> getDayActiveSet() {
        return dayActiveSet;
    }

    public void setDayActiveSet(Set<String> dayActiveSet) {
        this.dayActiveSet = dayActiveSet;
    }

    public Integer getLastLoginOutTime() {
        return lastLoginOutTime;
    }

    public void setLastLoginOutTime(Integer lastLoginOutTime) {
        this.lastLoginOutTime = lastLoginOutTime;
    }

    public Set<String> getRechargeProductIdSet() {
        return rechargeProductIdSet;
    }

    public void setRechargeProductIdSet(Set<String> rechargeProductIdSet) {
        this.rechargeProductIdSet = rechargeProductIdSet;
    }

    public int getGiftBeans() {
        return giftBeans;
    }

    public void setGiftBeans(int giftBeans) {
        this.giftBeans = giftBeans;
    }

    public Set<String> getFriendsSet() {
        return friendsSet;
    }

    public void setFriendsSet(Set<String> friendsSet) {
        this.friendsSet = friendsSet;
    }

    public int getIsDoneShowPop() {
        return isDoneShowPop;
    }

    public void setIsDoneShowPop(int isDoneShowPop) {
        this.isDoneShowPop = isDoneShowPop;
    }

    public Map<String, Integer> getGiftMapStatus() {
        return giftMapStatus;
    }

    public void setGiftMapStatus(Map<String, Integer> giftMapStatus) {
        this.giftMapStatus = giftMapStatus;
    }

    public Map<String, Integer> getFriendsMapStatus() {
        return friendsMapStatus;
    }

    public void setFriendsMapStatus(Map<String, Integer> friendsMapStatus) {
        this.friendsMapStatus = friendsMapStatus;
    }

    public Map<String, Integer> getDollarsMapStatus() {
        return dollarsMapStatus;
    }

    public void setDollarsMapStatus(Map<String, Integer> dollarsMapStatus) {
        this.dollarsMapStatus = dollarsMapStatus;
    }

    public double getTotalDollars() {
        return totalDollars;
    }

    public void setTotalDollars(double totalDollars) {
        this.totalDollars = totalDollars;
    }

    public Set<String> getDayNewActiveSet() {
        return dayNewActiveSet;
    }

    public void setDayNewActiveSet(Set<String> dayNewActiveSet) {
        this.dayNewActiveSet = dayNewActiveSet;
    }

    public int getRegStatus() {
        return regStatus;
    }

    public void setRegStatus(int regStatus) {
        this.regStatus = regStatus;
    }

    public int getBackCodeStatus() {
        return backCodeStatus;
    }

    public void setBackCodeStatus(int backCodeStatus) {
        this.backCodeStatus = backCodeStatus;
    }

    public int getPlayGameStatus() {
        return playGameStatus;
    }

    public void setPlayGameStatus(int playGameStatus) {
        this.playGameStatus = playGameStatus;
    }
}

package com.quhong.data;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;

import java.util.Map;

public class StarBeatPoolRewardData {
    private float rewardNum; // 当前游戏奖池
    private float rewardLuckyNum; // 当前幸运奖池
    private Map<Integer, Integer> luckyNumMap; // 各幸运物品剩余奖池数量
    private int mTime;  //更新时间

    public StarBeatPoolRewardData() {
    }


    public float getRewardNum() {
        return rewardNum;
    }

    public void setRewardNum(float rewardNum) {
        this.rewardNum = rewardNum;
    }

    public float getRewardLuckyNum() {
        return rewardLuckyNum;
    }

    public void setRewardLuckyNum(float rewardLuckyNum) {
        this.rewardLuckyNum = rewardLuckyNum;
    }

    public Map<Integer, Integer> getLuckyNumMap() {
        return luckyNumMap;
    }

    public void setLuckyNumMap(Map<Integer, Integer> luckyNumMap) {
        this.luckyNumMap = luckyNumMap;
    }

    public int getmTime() {
        return mTime;
    }

    public void setmTime(int mTime) {
        this.mTime = mTime;
    }


    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}

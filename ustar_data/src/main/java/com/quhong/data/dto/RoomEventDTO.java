package com.quhong.data.dto;

import com.quhong.handler.HttpEnvData;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/12/6
 */
public class RoomEventDTO extends HttpEnvData {

    /**
     * 活动名称
     */
    private String name;

    /**
     * 活动描述
     */
    private String description;

    /**
     * 活动类型
     */
    private Integer type;

    /**
     * 活动开始时间
     */
    private Integer startTime;

    /**
     * 活动期间 单位：小时
     */
    private Integer duration;

    /**
     * 活动海报url
     */
    private String posterUrl;

    /**
     * 活动封面
     */
    private String eventCoverUrl;

    /**
     * 是否首页的列表 0否 1是
     */
    private Integer isHomepage;

    private Integer page;

    /**
     * 房间活动id
     */
    private Integer eventId;

    /**
     * 订阅操作 0订阅 1取消订阅
     */
    private Integer subOpt;

    /**
     * mine列表类型 0Subscribed 1Created
     */
    private Integer listType;

    /**
     * 是否是机器人
     */
    private boolean isRobot;

    /**
     * 是否申请活动支持
     */
    private int eventSupport;
    /**
     * 活动校验支持人
     */
    private String eventHostUser;

    private List<String> hostUserList;   // 提交上来的是uid列表

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getStartTime() {
        return startTime;
    }

    public void setStartTime(Integer startTime) {
        this.startTime = startTime;
    }

    public Integer getDuration() {
        return duration;
    }

    public void setDuration(Integer duration) {
        this.duration = duration;
    }

    public String getPosterUrl() {
        return posterUrl;
    }

    public void setPosterUrl(String posterUrl) {
        this.posterUrl = posterUrl;
    }

    public Integer getIsHomepage() {
        return isHomepage;
    }

    public void setIsHomepage(Integer isHomepage) {
        this.isHomepage = isHomepage;
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getEventId() {
        return eventId;
    }

    public void setEventId(Integer eventId) {
        this.eventId = eventId;
    }

    public Integer getSubOpt() {
        return subOpt;
    }

    public void setSubOpt(Integer subOpt) {
        this.subOpt = subOpt;
    }

    public Integer getListType() {
        return listType;
    }

    public void setListType(Integer listType) {
        this.listType = listType;
    }

    public boolean isRobot() {
        return isRobot;
    }

    public void setRobot(boolean robot) {
        isRobot = robot;
    }

    public String getEventCoverUrl() {
        return eventCoverUrl;
    }

    public void setEventCoverUrl(String eventCoverUrl) {
        this.eventCoverUrl = eventCoverUrl;
    }

    public int getEventSupport() {
        return eventSupport;
    }

    public void setEventSupport(int eventSupport) {
        this.eventSupport = eventSupport;
    }

    public String getEventHostUser() {
        return eventHostUser;
    }

    public void setEventHostUser(String eventHostUser) {
        this.eventHostUser = eventHostUser;
    }

    public List<String> getHostUserList() {
        return hostUserList;
    }

    public void setHostUserList(List<String> hostUserList) {
        this.hostUserList = hostUserList;
    }
}

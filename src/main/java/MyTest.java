import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.google.i18n.phonenumbers.NumberParseException;
import com.google.i18n.phonenumbers.PhoneNumberUtil;
import com.google.i18n.phonenumbers.Phonenumber;
import com.sun.javafx.fxml.builder.URLBuilder;
import io.jsonwebtoken.*;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.net.MalformedURLException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.PublicKey;
import java.security.spec.RSAPublicKeySpec;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.NumberFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import org.apache.commons.codec.binary.Base64;
import org.springframework.util.StringUtils;
import org.springframework.web.util.UriComponentsBuilder;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;

public class MyTest {
    private static final PhoneNumberUtil phoneNumberUtil = PhoneNumberUtil.getInstance();
    private static final String DEFAULT_COUNTRY = "CN";
    private static final Pattern phonePattern = Pattern.compile("^\\+\\d{9,20}$");

    private static final Pattern numPattern = Pattern.compile("[\\w|\\s|\\.]+");

    private static final Pattern unVisiblePattern = Pattern.compile("[\\p{C}]");

    private static void t1() {
        double a = 23;
        double b = 44;

        int c = (int) (a / b * 100);
        System.out.println(c);
    }

    private static void t2() {
        double a = 23;
        int b = 44;
        int c = 55;

        a += b;
        a -= c;
        System.out.println(a);
    }

    private static void t3(int level) {

//        Integer a = null;
//        int atype = a;
//
//
//        System.out.println(atype == a);

        Integer b = 0;
        Integer c = 0;

        List<Integer> chargeBeansList = new ArrayList<>();
        for (int i = 0; i < 100; i++) {
            chargeBeansList.add(i * 100 * 1000);
        }
        for (int i = 0; i < 10; i++) {
            chargeBeansList.add((50 + i * 1000) * 1000);
        }
        for (int i = 10; i <= 50; i++) {
            chargeBeansList.add(i * 1000 * 1000);
        }
        Comparator<Integer> comparator = Comparator.comparing(o -> o);
        chargeBeansList.sort(comparator);
        System.out.println(" size=" + chargeBeansList.size() + " level=" + level +
                " beans=" + chargeBeansList.get(level));
//        System.out.println(b.equals(c) + " max=" + Integer.MAX_VALUE);
    }

    private static void t5() {

        int a = 1000;
        float rote = (float) 118 / 4840;
        int b = (int) (a * rote);

        float creditMoney = (float) 100.58;
        float debtMoney = (float) 60.65;
        int rate = creditMoney > 0 ? (int) ((creditMoney - debtMoney) / creditMoney * 100) : 0;

        System.out.println("rote=" + rote + " b=" + b + " b++=" + b++ + " after_b=" + b + " ++b=" + ++b
        +" rate="+rate);

        String fields = "r:432234_5cdsjkljfa_6c214784_ ";
        String[] allField = fields.split("_");
        System.out.println("allField:" + Arrays.toString(allField));

        List<String> alist = new ArrayList<String>();
        alist.add("a");
        alist.add("b");

        System.out.println("alist:" + alist);

        ConcurrentMap<String, String> cacheMap = new ConcurrentHashMap<>();
        cacheMap.put("1", "a");
        cacheMap.put("2", "b");
        cacheMap.put("2", "c");
        System.out.println("cacheMap:" + cacheMap);


        Double in = new Double("234");
        Number nin = in;
        System.out.println("nin:" + nin.intValue());
    }

    /**
     * @param phoneNumber 手机号
     * @param countryCode 手机区号
     * @Author: Jet
     * @Description 手机校验逻辑
     * @Date: 2018/5/9 9:21
     */
    public static boolean doValid(String phoneNumber, String countryCode) {
        int ccode = Integer.parseInt(countryCode);
        long phone = Long.parseLong(phoneNumber);
        Phonenumber.PhoneNumber pn = new Phonenumber.PhoneNumber();
        pn.setCountryCode(ccode);
        pn.setNationalNumber(phone);
        return phoneNumberUtil.isValidNumber(pn);
    }

    public static boolean doPossible(String phoneNumber, String countryCode) {
        int ccode = Integer.parseInt(countryCode);
        long phone = Long.parseLong(phoneNumber);
        Phonenumber.PhoneNumber pn = new Phonenumber.PhoneNumber();
        pn.setCountryCode(ccode);
        pn.setNationalNumber(phone);
        return phoneNumberUtil.isPossibleNumber(pn);
    }

    /**
     * @param phone “+8617717031234 +008617717031234 8617717031234 177-1703-1234””
     * @return 电话实体类 Phonenumber.PhoneNumber
     * @Author: Jet
     * @Description 电话解析逻辑
     * @Date: 2018/5/9 9:21
     * https://repo1.maven.org/maven2/com/googlecode/libphonenumber/
     */
    public static Phonenumber.PhoneNumber doParse(String phone) {
        try {
            return phoneNumberUtil.parse(phone, DEFAULT_COUNTRY);
        } catch (NumberParseException e) {
            throw new NumberFormatException("invalid phone number: " + phone);
        }
    }

    private static void testPhoneNum(String code, String num) {
//        String num = "13565692958";
//        String code = "86";
        boolean retV = doValid(num, code);
        boolean retP = doPossible(num, code);

        Phonenumber.PhoneNumber pn = doParse(num);
        boolean retV2 = phoneNumberUtil.isValidNumber(pn);
        boolean retP2 = phoneNumberUtil.isPossibleNumber(pn);

        System.out.println("doValid " + retV + " doPossible " + retP +
                "\n china isValid " + retV2 + "  china isPossible " + retP2);

    }

    private static void testInteger() {
        List<Integer> VIP_MIC_LIST = Arrays.asList(3, 4, 5, 6, 186, 187, 188, 334, 355);
        boolean ret = VIP_MIC_LIST.contains(null);

        System.out.println("ret " + ret + "int max=" + Integer.MAX_VALUE + " long max=" + Long.MAX_VALUE);

    }

    public static String formatDevotes(long devotes) {
        if (devotes >= 1000000) {
            return new BigDecimal(devotes / 1000000f).setScale(1, RoundingMode.FLOOR) + "M";
        } else if (devotes >= 1000) {
            return new BigDecimal(devotes / 1000f).setScale(1, RoundingMode.FLOOR) + "K";
        } else {
            return devotes + "";
        }
    }

    public static class Father {
        protected int a;

        public int getA() {
            return a;
        }

        public void setA(int a) {
            this.a = a;
        }

        @Override
        public String toString() {
            return JSON.toJSONString(this);
        }
    }

    public static class Son extends Father {
        private int b;
        private boolean isOk;

        public int getB() {
            return b;
        }

        public void setB(int b) {
            this.b = b;
        }

        public boolean isOk() {
            return isOk;
        }

        public void setOk(boolean ok) {
            isOk = ok;
        }
    }

    static void pp() {
        System.out.println("Son " + new Son());
    }

    private static void ttJwt(String token) {
        try {
            DecodedJWT jwt = JWT.decode(token);
            String kid = jwt.getHeaderClaim("kid").asString();
            String alg = jwt.getHeaderClaim("alg").asString();
//            System.out.println("getHeader=" + jwt.getHeader()+" getPayload="+jwt.getPayload()+"getSubject="+jwt.getSubject());
            System.out.println("kid=" + kid + " alg=" + alg + " getClaims=" + jwt.getClaims() + " getKeyId=" + jwt.getKeyId() + " sub=" + jwt.getClaim("sub").asString());
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }


    private static void ttStream() {
        List<Integer> my_list = Arrays.asList(3, 4, 5, 6, 186, 187, 188, 334, 355);
        System.out.println("my_list sub=" + my_list.subList(0, 4));

//        List<SendRecvFirstData> my_list = Collections.emptyList();
//        Map<Integer, Long> gidNumMap = my_list.stream().collect(Collectors.toMap(SendRecvFirstData::getGiftId, SendRecvFirstData::getGiftNum));
//        System.out.println("gidNumMap=" +gidNumMap);
    }

    /**
     * 校验token是否正确
     */
    public static boolean verifyToken(String token) {
        // https://appleid.apple.com/auth/keys
        // https://blog.csdn.net/weixin_41221717/article/details/*********
        try {
//            String n = "lxrwmuYSAsTfn-lUu4goZSXBD9ackM9OJuwUVQHmbZo6GW4Fu_auUdN5zI7Y1dEDfgt7m7QXWbHuMD01HLnD4eRtY-RNwCWdjNfEaY_esUPY3OVMrNDI15Ns13xspWS3q-13kdGv9jHI28P87RvMpjz_JCpQ5IM44oSyRnYtVJO-320SB8E2Bw92pmrenbp67KRUzTEVfGU4-obP5RZ09OxvCr1io4KJvEOjDJuuoClF66AT72WymtoMdwzUmhINjR0XSqK6H0MdWsjw7ysyd_JhmqX5CAaT9Pgi0J8lU_pcl215oANqjy7Ob-VMhug9eGyxAWVfu_1u6QJKePlE-w";
//            String e = "AQAB";

            String n = "1JiU4l3YCeT4o0gVmxGTEK1IXR-Ghdg5Bzka12tzmtdCxU00ChH66aV-4HRBjF1t95IsaeHeDFRgmF0lJbTDTqa6_VZo2hc0zTiUAsGLacN6slePvDcR1IMucQGtPP5tGhIbU-HKabsKOFdD4VQ5PCXifjpN9R-1qOR571BxCAl4u1kUUIePAAJcBcqGRFSI_I1j_jbN3gflK_8ZNmgnPrXA0kZXzj1I7ZHgekGbZoxmDrzYm2zmja1MsE5A_JX7itBYnlR41LOtvLRCNtw7K3EFlbfB6hkPL-Swk5XNGbWZdTROmaTNzJhV-lWT0gGm6V1qWAK2qOZoIDa_3Ud0Gw";
            String e = "AQAB";

//            BigInteger modulus = new BigInteger(1, Base64.getMimeDecoder().decode(n));
//            BigInteger publicExponent = new BigInteger(1, Base64.getMimeDecoder().decode(e));


            BigInteger modulus = new BigInteger(1, Base64.decodeBase64(n));
            BigInteger publicExponent = new BigInteger(1, Base64.decodeBase64(e));

            RSAPublicKeySpec spec = new RSAPublicKeySpec(modulus, publicExponent);
            KeyFactory kf = KeyFactory.getInstance("RSA");
            PublicKey publicKey = kf.generatePublic(spec);

//            JwtParser jwtParser = Jwts.parser();
//            JWT jwt = jwtParser.parse(token);

            DecodedJWT jwt = JWT.decode(token);
            String iss = jwt.getClaim("iss").asString();
            String aud = jwt.getClaim("aud").asString();
            String sub = jwt.getClaim("sub").asString();

//            String[] tokenInfo = token.split("\\.");
//            String header = new String(Base64.getDecoder().decode(tokenInfo[0]));
//            String payload = new String(Base64.getDecoder().decode(tokenInfo[1]));
//            String sub = JSONObject.parseObject(payload).getString("sub");

            verifyAppleLoginCode(publicKey, token, iss, aud, sub);
            return true;
        } catch (Exception exception) {
            System.out.println("exception=" + exception);
            exception.printStackTrace();
            return false;
        }
    }

    private static boolean verifyAppleLoginCode(PublicKey publicKey, String identityToken, String iss, String aud, String sub) {
        boolean result = false;
        JwtParser jwtParser = Jwts.parser().setSigningKey(publicKey);
        jwtParser.requireIssuer(iss);
        jwtParser.requireAudience(aud);
        jwtParser.requireSubject(sub);
        try {
            Jws<Claims> claim = jwtParser.parseClaimsJws(identityToken);
            System.out.println("header=" + JSON.toJSONString(claim.getHeader()) + " body=" + JSON.toJSONString(claim.getBody()));
            if (claim != null && claim.getBody().containsKey("auth_time")) {
                result = true;
            }
        } catch (ExpiredJwtException e) {
            e.printStackTrace();
//            throw new RuntimeException(String.format("apple登录授权identityToken过期.", e));
        } catch (SignatureException e) {
            e.printStackTrace();
//            throw new RuntimeException(String.format("apple登录授权identityToken非法.", e));
        }
        System.out.println("result=" + result);
        return result;
    }

    private static void parseNum(String oldNum) {
        // ARABIC_INDIC = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٩']
        char[] aa = oldNum.toCharArray();
        StringBuilder newNum = new StringBuilder();
        for (Character ch : aa) {
            newNum.append(parseUserPwd(ch.toString()));
        }
        System.out.println("newNum=" + newNum);

    }


    private static String parseUserPwd(String userPwd) {
        String userStrPwd = userPwd;
        try {
            // 针对阿语数字进行特殊处理
            userStrPwd = Integer.parseInt(userPwd) + "";
        } catch (NumberFormatException e) {
//            e.printStackTrace();
            System.out.println("e=" + e);
        }
        return userStrPwd;
    }

    private static void claims() {
        Map<String, Object> claims = new HashMap<>();
        claims.put("a", "av");
        claims.put("b", 3);

        List<Integer> tnRisk = new ArrayList<>();
        tnRisk.add(1);
        tnRisk.add(2);

        Set<String> deviceAllUid = new HashSet<>();
        deviceAllUid.add("gtge");
        deviceAllUid.add("dsad");
        System.out.println("claims=" + claims + " tnRisk=" + tnRisk + " deviceAllUid=" + deviceAllUid);

        ConcurrentMap<String, String> actorMap = new ConcurrentHashMap<>();

        actorMap.put("a", "a");
        actorMap.put("b", "b");
        actorMap.put("c", "c");
        actorMap.put("d", "d");

        for (String item : actorMap.values()) {
            if ("b".equals(item)) {
                actorMap.remove(item);
            }
        }

        System.out.println("actorMap size=" + actorMap.size() + " actorMap=" + actorMap);

        System.out.println("isChinese=" + isChinese("fbrt你好啊"));
    }

    private static boolean isChinese(String src) {
        Pattern p = Pattern.compile("[\u4e00-\u9fa5]");
        Matcher m = p.matcher(src);
        return m.find();
    }

    private static boolean isArb(String src) {
        Pattern p = Pattern.compile("[\u0600-\u06ff]|[\u0750-\u077f]|[\ufb50-\ufc3f]|[\ufe70-\ufefc]");
        Matcher m = p.matcher(src);
        return m.find();
    }

    private static void checkArb() {
        String b = "+9720***********";
        String a = "0123456789";
        String a2 = "+0123456789";
        System.out.println("sub=" + a.substring(a.length() - 10, a.length()) + " sub2=" + a2.substring(a2.length() - 7, a2.length()));

        String str = "ABN \uD81A\uDD0DBARK ABN \uD81A\uDD0DBARK";
//        String str = "https://cloudcdn.qmovies.tv/1005568/IMG_٢٠٢١٠٨١٦_٢١٠٠٤٧٣٣٤.jpg";
//        String str = "https://cloudcdn.qmovies.tv//20181014/head_1539500850806.jpg";
//        String str = "ABN \uD81A\uDD0DBARK ABN ?";

        boolean ret = isArb(str);

        Matcher mV = unVisiblePattern.matcher(str);
        String afterStr = str.replaceAll("[\\p{C}]", "");
        Matcher mV2 = unVisiblePattern.matcher(afterStr);

        String encodedString = new String(str.getBytes(), StandardCharsets.UTF_8);
        System.out.println("before_str=" + str + " 之前存在不可见字符=" + mV.find() + " after_Str=" + afterStr + " 之后存在不可见字符=" + mV2.find() + " encodedString=" + encodedString);
//        Matcher m = phonePattern.matcher(str);


        Matcher m = numPattern.matcher(str);
        String lenStr = "MDFAMTAzMTg1MTcxQDM0MzQxZjFlYTBlN2UzM2IxNDU0MGZiaMmJjNDFhOGQ0QGVhNDgyYTZlMmVhNjViaNjgwNWI4YjRmODYwMzhiaOGQyYjc4MWIzZDhhYmM2YzUyMWUyZDc5ZThj";
        System.out.println("isArb=" + ret + " find=" + m.find() + " matches=" + m.matches() + " len=" + lenStr.length());


        String head = "https://cloudcdn.qmovies.tv//20181014/head_1539500850806.jpg";
        String oldStr = "null-";
        String newStr = oldStr.replace("null-", "");
        String[] split = oldStr.split("-");
        newStr = split[split.length - 1];
        System.out.println("find//=" + head.lastIndexOf("//"));
        System.out.println("ret=" + (4 / 5) + " newStr=" + newStr + " ");
    }

    private static void dateUtils(String date) {
        DateTimeFormatter yyyy_mm_dd = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        DateTimeFormatter yyyy_mm = DateTimeFormatter.ofPattern("yyyy_MM");
        DateTimeFormatter yyyyMMdd = DateTimeFormatter.ofPattern("yyyyMMdd");
        DateTimeFormatter yyyy_m_d = DateTimeFormatter.ofPattern("yyyy-M-d");

        // //去除不可打印字符 比如 \u202C \u202D   特殊字符对照表 https://blog.csdn.net/weixin_45874277/article/details/106469853
        String afterDate = date.replaceAll("[\\p{C}]", "");
        System.out.println("beforeDate=" + date + " afterDate=" + afterDate);

        LocalDate LocalDate_yyyy_m_d = LocalDate.parse(afterDate, yyyy_m_d);
        System.out.println("LocalDate_yyyy_m_d=" + LocalDate_yyyy_m_d);

//        LocalDate LocalDate_yyyy_mm_dd = LocalDate.parse(afterDate, yyyy_mm_dd);
//        System.out.println("LocalDate_yyyy_mm_dd=" + LocalDate_yyyy_mm_dd );

    }


    private static String getConstellation(String birthday) {
        String[] CONSTELLATION_ARR = {"水瓶座", "双鱼座", "白羊座", "金牛座", "双子座", "巨蟹座", "狮子座", "处女座", "天秤座", "天蝎座", "射手座", "魔羯座"};
//         int[] CONSTELLATION_ARR = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12};
        int[] CONSTELLATION_EDGE_DAY = {20, 19, 21, 21, 21, 22, 23, 23, 23, 23, 22, 22};
        LocalDate birthDate;
        try {
            birthDate = DateSupport.parseYyyymd(birthday);
        } catch (DateTimeParseException e) {
            birthDate = DateSupport.parse(birthday);
        }
        Date date = DateSupport.UTC.getDate(birthDate);
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int month = cal.get(Calendar.MONTH);//0-11
        int day = cal.get(Calendar.DAY_OF_MONTH);
        // {20, 19, 21, 21, 21, 22, 23, 23, 23, 23, 22, 22}
        System.out.println("date=" + date + " cal=" + cal + " month=" + month + " day=" + day);
        System.out.println("month=" + month + " day=" + day);
        if (day < CONSTELLATION_EDGE_DAY[month]) {
            month = month - 1;
        }
        if (month >= 0) {
            return CONSTELLATION_ARR[month];
        }
        return CONSTELLATION_ARR[11];
    }

    private static void testPrettyFormatJson() {
        JSONObject jsonObject = new JSONObject(true);

//        Map<String,Object> jsonObject = new TreeMap<>();
        jsonObject.put("告警描述", "非法youstar，疑似骚扰，请市场运营关注此用户");
        jsonObject.put("用户id", "111");
        jsonObject.put("IP地址", "1.1.1");
        jsonObject.put("IP所属国家区域", "china");
        jsonObject.put("图灵顿设备id", "123");
        jsonObject.put("图灵顿风险标签", "216");
//                        String detail = JSONObject.toJSONString(jsonObject);
        String content = JSON.toJSONString(jsonObject, SerializerFeature.PrettyFormat,
                SerializerFeature.WriteMapNullValue, SerializerFeature.WriteDateUseDateFormat,
                SerializerFeature.WriteNullListAsEmpty);
        System.out.println("content=" + content);
    }

    private static void ttUrl(String url) {
        try {
            URL urlD = new URL(url);
            String[] spUrl = urlD.getPath().split("/");
            String fileName = spUrl[spUrl.length - 1];
            String uid = "5cb1c79c644f8e0022dff564";
            int dSec = 99;
            int d = dSec / 50;
            int m = dSec % 50;

//            List<String> bizRobotSet = Arrays.asList("aaa","bbb","ccc");
            List<String> bizRobotSet = new ArrayList<>();
            bizRobotSet.add("ABN \uD81A\uDD0DBARK ABN ?");
            bizRobotSet.add("bbb");
            bizRobotSet.add("ccc");
            bizRobotSet.add("ddd");

            Collections.shuffle(bizRobotSet);
            List<String> myRobotSet = new ArrayList<>(bizRobotSet.subList(0, 3));

//            List<String> myRobotSet = new ArrayList<>(bizRobotSet);
//            List<String> myRobotSet = new ArrayList<>();
            bizRobotSet.clear();

            UriComponentsBuilder urlBuilder = UriComponentsBuilder.fromHttpUrl(url);
            urlBuilder.replaceQueryParam("activityId", "10");
            String newUrl = urlBuilder.build(false).encode().toUriString();


            List<SendRecvFirstData> old = new ArrayList<>();
            old.add(new SendRecvFirstData("aa", "bb", 1, 2L, 3));
            old.add(new SendRecvFirstData("aa2", "bb2", 12, 22L, 32));
            old.add(new SendRecvFirstData("aa3", "bb3", 13, 23L, 33));
            old.add(new SendRecvFirstData("aa4", "bb4", 14, 24L, 34));

            List<SendRecvFirstData> newa = new ArrayList<>(old);
            old.clear();
            old.add(new SendRecvFirstData("bb", "cc", 52, 2L, 3));
            List<SendRecvFirstData> newb = new ArrayList<>(old);
            old.clear();

            String fromUrl = "  hhh ";
            fromUrl = StringUtils.isEmpty(fromUrl) ? null : StringUtils.isEmpty(fromUrl.trim()) ? null : fromUrl.trim();

            System.out.println("fileName=" + fileName + " path=" + urlD.getPath() + " userInfo=" + urlD.getUserInfo()
                    + " ref=" + urlD.getRef() + " query=" + urlD.getQuery() + " subStr=" + uid.substring(uid.length() - 10) + " d=" + d + " m=" + m
                    + " myRobotSet=" + myRobotSet.remove(0) + " after myRobotSet=" + myRobotSet + " bizRobotSet=" + bizRobotSet
                    + "\n old=" + old + " newa=" + newa + " newb=" + newb
                    + "\n newUrl=" + newUrl
                    + "\n fromUrl=" + fromUrl);

        } catch (MalformedURLException e) {
            e.printStackTrace();
        }
    }

    public static void getStartTimeByDayLoop(String day, int loop) {
        int begin = (int) (DateHelper.ARABIAN.parseDateInDay(day) / 1000);
        int to = begin + (loop - 1) * 50;

        NumberFormat numberFormat = NumberFormat.getNumberInstance();
        System.out.println("day = " + day + ", loop = " + loop + ", begin = " + begin + ", to = " + to + " numberFormat" + numberFormat.format(45131313131L));
    }


    public static void getCountryCode(String country) {
        String code = null;
        try {
            code = country.split("_")[0];
        } catch (Exception e) {
            e.printStackTrace();
        }
        DayTimeData dayTimeData = new DayTimeData();
        System.out.println("code = " + code + ", country = " + country + " ,moreThenToday=" + dayTimeData.isMoreThenToday());
    }

    public static void caclcMath() {
        int preBeans = 500;
        int hitOdds = 5;
        int rota = 9;
        int minFakeBean = hitOdds;
        int beans = Math.max((preBeans - hitOdds * rota), minFakeBean);
        String ll = "MDFAMTAzMTg1MTcxQDQ4OTY0MTA4MzIyNjAzMGUzYmUwMzA2NWI2MGVlYjA3QGFkMDk3YTM1YzU2NjA4N2NhYjA5ZjhhZDJlNjNlMDEwMGUwNDQ5N2EwNmUyMWQxNzZhOTJlNQ";
        System.out.println("beans = " + beans + " size=" + ll.length() + "max int=" + Integer.MAX_VALUE);
    }

    public static void ttaa() {

        String userName = " هعغهغ\uD83C\uDF84ヾ(@^▽^@)ノ\""; //  阿语名字 waho 测试服1001164  正确为笑脸在左
//        String userName = "هعغهغ\uD83C\uDF84ヾ(@^▽^@)ノ";
//        String userName = "abcd"; // 英语名字
//        String TEXT_AR = "aaa #username# ";
//        String newStr = TEXT_AR.replace("#username#", userName);
//
//        String TEXT_AR_S = "bbb %s";
//        String newStr2 =String.format(TEXT_AR_S,userName);
        // u202b 从右至左  u202c 从左至右


         String TEXT_NEW_EN = "Congratulations! \u202b#username#\u202c has achieved Top #rank# in today's room contribution leaderboard.";
        String TEXT_NEW_EN_F = "Congratulations! \u202b%s\u202c has achieved Top %s in today's room contribution leaderboard.";
        String TEXT_NEW_AR = "تهانينا! حقق #username# المرتبة #rank# في قائمة تصنيف إسهام الغرف اليوم.";


        String en = TEXT_NEW_EN.replace("#username#", userName).replace("#rank#", +3 + "");
        String en2 =String.format(TEXT_NEW_EN_F,userName,3);
        String ar = TEXT_NEW_AR.replace("#username#",userName).replace("#rank#", 3 + "");

        System.out.println(userName); //
        System.out.println("\u202b"+userName); //
        System.out.println(en); //
        System.out.println(en2);//正确为笑脸在左
        System.out.println(ar);//正确为笑脸在左
    }


    private static void t6() {
        int twoDaysLater = DateHelper.ARABIAN.currentTimeZoneSeconds()+ (int) TimeUnit.DAYS.toSeconds(2);
        int threeDaysLater = twoDaysLater + (int) TimeUnit.DAYS.toSeconds(1); // 允许一天的误差范围
        System.out.println("twoDaysLater = " + twoDaysLater + ", threeDaysLater = " + threeDaysLater);
    }

    public static String fbDelete(String signedRequest) {
        System.out.println("fbDelete signedRequest:"+signedRequest);
        String[] parts = signedRequest.split("\\.", 2);
        if (parts.length != 2) {
            throw new IllegalArgumentException("Invalid signed_request format");
        }

        String encodedSig = parts[0];
        String payload = parts[1];

        // 1. 解码 payload
        String dataJson = new String(java.util.Base64.getUrlDecoder().decode(payload));
        System.out.println("fbDelete dataJson:"+dataJson);
        JSONObject data = JSONObject.parseObject(dataJson);

        // 2. 校验签名
        if (!verifySignature(encodedSig, payload)) {
            System.out.println("fbDelete verifySignature fail");
            throw new SecurityException("Invalid signed_request signature");
        }

        // 3. 获取用户 ID
        String userId = data.getString("user_id");

        // 4. 删除用户数据（这里示例仅打印，实际要操作数据库）
        System.out.println("Deleting user data for userId: " + userId);

        // TODO: 数据库删除/标记删除用户相关数据
        // userRepository.deleteByFacebookId(userId);

        // 5. 生成确认码
        String confirmationCode = "del_" + userId + "_" + System.currentTimeMillis();

        // 6. 返回 Facebook 要求的响应
        Map<String, String> response = new HashMap<>();
        response.put("url", "https://yourdomain.com/deletion-status/" + confirmationCode);
        response.put("confirmation_code", confirmationCode);
        return JSON.toJSONString(response);
    }

    private static boolean verifySignature(String encodedSig, String payload) {
        try {
            String APP_SECRET = "1a989224a726c484a9ff0a862e90c354"; // 从Facebook开发者后台获取
            // String APP_SECRET = "1f73f6f15e0b583546afbfc4c8e3d437";
            // String APP_SECRET = "af8ccf52b31097c562eff2df393c4f8c";
            byte[] sig = java.util.Base64.getUrlDecoder().decode(encodedSig);

            Mac hmac = Mac.getInstance("HmacSHA256");
            hmac.init(new SecretKeySpec(APP_SECRET.getBytes(), "HmacSHA256"));
            byte[] expectedSig = hmac.doFinal(payload.getBytes());

            return Arrays.equals(sig, expectedSig);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    private static boolean ddd() {
        System.out.println("result2="+"bcd".equals("bcd"));
        boolean result1 = null==null;
        System.out.println("result1= "+ result1);
        return true;
    }


    public static void main(String[] args) {
        ddd();
//        ttaa();
//         t6();
//         fbDelete("fnHzpEB0zZsWMdXoro5hqDkyxx6dOEPHctvnPO4YgtI.eyJ1c2VyX2lkIjoiMTkzNTQ2NzQzMDYzNDk3MCIsImFsZ29yaXRobSI6IkhNQUMtU0hBMjU2IiwiaXNzdWVkX2F0IjoxNzU2MjY2MzE1fQ");
//        caclcMath();
//        Object[] obj = {"水瓶座", null, 2, "10", "双子座", "巨蟹座", "狮子座", "处女座", "天秤座", "天蝎座", "射手座", "魔羯座"};
//        System.out.println("ret = " + StringUtils.arrayToDelimitedString(obj, "-"));
//        getCountryCode("CN_ded");
//        getStartTimeByDayLoop("2023-05-18", 1);
//        testPrettyFormatJson();
//        t1();
//        t2();
//        t3(1);
//        t3(100);
//        t3(150);
//       String ret = formatDevotes(1500);
//        String ret = "<EMAIL>";
//        System.out.println("ret " + ret.endsWith("@gmail.com")  );
//        testPhoneNum();
//        testInteger();
//        t5();
//        pp();
//        claims();
//        String token = "eyJraWQiOiJZdXlYb1kiLCJhbGciOiJSUzI1NiJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.th808Di1s6hpSTAfDuT2TQ8MOkZtJmxDNQDCP4Ekz6hmfbIPu__PYmdMrGJiTwhfkFa1ru3q35e92Nk8X1zz2Ddu3zvBNeeewcB99kTuNgoxsVeqHCiDPrnxM5O3Nho1hP-EegKleOT-IueGlrWZDUP76yph_siqeDMJ4K0pgNMqw-aM-dn_4kbkL5T6xwWZHb8vVlzLF5IG7yasgmmElQtumOExh9DQ71_aOQ6zMQ-c5ziXkQHGPmW8JFISNDpQyUa8mAEqy2JjKZ4iGuV75L0c5H6CBie3nA1eZ2CKMCsVfkZ-cfLmnwjgYhK8yABn9V781gYg20Z52XWAlJPJCg";
//        ttJwt(token);
//        verifyToken(token);
        //['٠,'١', '٢', '٣', '٤', '٥', '٦', '٧','۸', '٩']
//        String afStr = parseUserPwd("٩۸٧٦٥٤٣٢١٠");
//        parseNum("+958٩۸٧٦٥٤٣٢١٠");
//        parseNum("+966٠٥٣١٣٠١٢٧٠");
//        parseNum("٢٠٠٥-٠٢-٢٤");
//        String account = "ilc5IpT403MP2dLtxFjcjZgvO8uk5XsvwICbytXPD7oB2HQ3v/A4Fgt3Dc974gUgCKzSECKhP4AozWqHPSfDaqb6DsKOwpLddNfbOOhAdlaFRw5hnLx6NUu2ZeiKf5kR2QM/fB0dcDVu/ZOlWBxfz6OPZRST6nYWEhorNJKa2WGjza8qEA/XH1yaoARDYEyW0dY+XtduY84Ufcl1diE8oid7gKEsLMpOvQslKfGBCOQJ/RXKITJEim//1B9A7DPCmhQ4YJseF2NJAmLeMkTr/cwiRBiPJy4zJ54PbX/L8vwALUkVycBPj9F7rZI6YYYuDC1YWh0Fsjucs0dg9CtzGnrWO9UlAK/rUDEvqD0hvhTM83dU6Y1LCTSUjRUdEcC12j2zsTA5rLkMPXRNXEHDOIRBxRKIbcn0Wc6TCrDa5/STBedPlVMkWt0/chU=";
//        String tNull = null;
//        Object b = null;
//        System.out.println("length=" + account.length() + " sub=" + account.substring(0, 1) +
//                " rad=" + ThreadLocalRandom.current().nextInt(18, 20) +
//                " vip=" + account.contains("vip") + " to null=" + tNull + " afSter=" + afStr +
//                "fmt" + String.format("hash:chargeBeans:%s gf:%s", "ds", null) + " b instanceof =" + (b instanceof Integer));

//        String account = " d s a   d  ";
//        String newa = account.trim().replace(" ", "");
//        System.out.println("newa="+newa);
//        testPhoneNum("44","**********");

//        String from = " الـرافـديـن";
//        String to = Base64.getEncoder().encodeToString(from.getBytes(StandardCharsets.UTF_8));
//        System.out.println("from="+from+"  to="+to);
//        testPhoneNum("86","***********");

//        List<Integer> DEVICE_EMULATOR_TYPE = Arrays.asList(204, 301, 302, 401, 402, 1100);
//        System.out.println("DEVICE_EMULATOR_TYPE=" + DEVICE_EMULATOR_TYPE + " [0]=" + DEVICE_EMULATOR_TYPE.get(0)
//                + " [3]=" + DEVICE_EMULATOR_TYPE.get(3));
//        dateUtils("1996-1-01");
//        ttStream();
//        System.out.println("星座是:" + getConstellation("2004-01-20"));
//        ttUrl("https://cloudcdn.qmovies.tv/user/0E5612B34AF4AFAC024B0E892B68F9C6.png?activityId=500");
//        checkArb();
    }
}

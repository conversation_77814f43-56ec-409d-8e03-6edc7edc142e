import java.text.SimpleDateFormat;
import java.util.TimeZone;

public class ThreadLocalDateFormat extends ThreadLocal<SimpleDateFormat> {
    private String formatString;
    private TimeZone timeZone;

    public ThreadLocalDateFormat(String formatString){
        this.formatString = formatString;
    }

    public ThreadLocalDateFormat(String formatString, String timeZone){
        this.formatString = formatString;
        this.timeZone = TimeZone.getTimeZone(timeZone);
    }

    public ThreadLocalDateFormat(String formatString, TimeZone timeZone){
        this.formatString = formatString;
        this.timeZone = timeZone;
    }

    @Override
    protected SimpleDateFormat initialValue() {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(formatString);
        if(this.timeZone != null){
            simpleDateFormat.setTimeZone(timeZone);
        }
        return simpleDateFormat;
    }
}

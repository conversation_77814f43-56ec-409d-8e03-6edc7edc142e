package com.quhong.data;

import com.alibaba.fastjson.annotation.JSONField;
import com.quhong.mongo.data.MongoRoomData;

public class PartyListData extends RoomListData {

    @JSONField(serialize = false)
    private double weight; // popular权重值，用于排序
    @JSONField(serialize = false)
    private int forYouWeight; // forYou权重值，用于排序
    private int realOnline; // 真实在线人数
    @JSONField(serialize = false)
    private String countryCode; // 国家代码
    @JSONField(serialize = false)
    private MongoRoomData roomData;
    @JSONField(serialize = false)
    private int activeRoomWeight; // activeRoom权重值，用于排序
    @JSONField(serialize = false)
    private int area; // 1：大户国家 2：非大户国家
    @JSONField(serialize = false)
    private long devote; // 最近1分钟房间贡献
    @JSONField(serialize = false)
    private long msgCount; // 最近1分钟房间消息数
    @JSONField(serialize = false)
    private int micCount; // 麦位置人数

    @JSONField(serialize = false)
    private int kickCount; // 最近5分钟内踢人人数
    @JSONField(serialize = false)
    private int inviteMicCount; // 最近10分钟内邀请新用户上麦的人数
    @JSONField(serialize = false)
    private int stayRoomCount; // 最近5分钟停留时长大于3分钟人数

    @JSONField(serialize = false)
    private double saWeight; // 沙特，伊拉克单独权重值，用于排序

    public double getWeight() {
        return weight;
    }

    public void setWeight(double weight) {
        this.weight = weight;
    }

    public int getForYouWeight() {
        return forYouWeight;
    }

    public void setForYouWeight(int forYouWeight) {
        this.forYouWeight = forYouWeight;
    }

    public int getRealOnline() {
        return realOnline;
    }

    public void setRealOnline(int realOnline) {
        this.realOnline = realOnline;
    }

    public int getArea() {
        return area;
    }

    public void setArea(int area) {
        this.area = area;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public MongoRoomData getRoomData() {
        return roomData;
    }

    public void setRoomData(MongoRoomData roomData) {
        this.roomData = roomData;
    }

    public int getActiveRoomWeight() {
        return activeRoomWeight;
    }

    public void setActiveRoomWeight(int activeRoomWeight) {
        this.activeRoomWeight = activeRoomWeight;
    }

    public long getDevote() {
        return devote;
    }

    public void setDevote(long devote) {
        this.devote = devote;
    }

    public long getMsgCount() {
        return msgCount;
    }

    public void setMsgCount(long msgCount) {
        this.msgCount = msgCount;
    }

    @Override
    public int getMicCount() {
        return micCount;
    }

    @Override
    public void setMicCount(int micCount) {
        this.micCount = micCount;
    }

    public int getKickCount() {
        return kickCount;
    }

    public void setKickCount(int kickCount) {
        this.kickCount = kickCount;
    }

    public int getInviteMicCount() {
        return inviteMicCount;
    }

    public void setInviteMicCount(int inviteMicCount) {
        this.inviteMicCount = inviteMicCount;
    }

    public int getStayRoomCount() {
        return stayRoomCount;
    }

    public void setStayRoomCount(int stayRoomCount) {
        this.stayRoomCount = stayRoomCount;
    }

    public double getSaWeight() {
        return saWeight;
    }

    public void setSaWeight(double saWeight) {
        this.saWeight = saWeight;
    }
}

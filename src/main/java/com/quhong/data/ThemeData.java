package com.quhong.data;

import com.quhong.core.config.ServerConfig;

public class ThemeData {

    private int id; // 麦位主题id
    private int tid; // 房间背景id （正式服）
    private int testTid; // 房间背景id（测试服）
    private String nameEn;
    private String nameAr;
    private String url;
    private String detailUrl;
    private String showTxtEn;
    private String showTxtAr;
    private int version;
    private int status;
    private int vip;
    private int roomStepLevel; // 主题所需要的房间等级
    private int micSize; // 主题所需要的房间等级
    private int themeType; // 主题类型  0: 默认主题类型, 使用该类主题要还原原来使用的背景  1: 该类主题使用指定的背景
    private int ctime; // 上新时间

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getTid() {
        return ServerConfig.isProduct() ? tid : testTid;
    }

    public void setTid(int tid) {
        this.tid = tid;
    }

    public void setTestTid(int testTid) {
        this.testTid = testTid;
    }

    public String getNameEn() {
        return nameEn;
    }

    public void setNameEn(String nameEn) {
        this.nameEn = nameEn;
    }

    public String getNameAr() {
        return nameAr;
    }

    public void setNameAr(String nameAr) {
        this.nameAr = nameAr;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getDetailUrl() {
        return detailUrl;
    }

    public void setDetailUrl(String detailUrl) {
        this.detailUrl = detailUrl;
    }

    public String getShowTxtEn() {
        return showTxtEn;
    }

    public void setShowTxtEn(String showTxtEn) {
        this.showTxtEn = showTxtEn;
    }

    public String getShowTxtAr() {
        return showTxtAr;
    }

    public void setShowTxtAr(String showTxtAr) {
        this.showTxtAr = showTxtAr;
    }

    public int getVersion() {
        return version;
    }

    public void setVersion(int version) {
        this.version = version;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getVip() {
        return vip;
    }

    public void setVip(int vip) {
        this.vip = vip;
    }

    public int getRoomStepLevel() {
        return roomStepLevel;
    }

    public void setRoomStepLevel(int roomStepLevel) {
        this.roomStepLevel = roomStepLevel;
    }

    public int getMicSize() {
        return micSize;
    }

    public void setMicSize(int micSize) {
        this.micSize = micSize;
    }

    public int getThemeType() {
        return themeType;
    }

    public void setThemeType(int themeType) {
        this.themeType = themeType;
    }

    public int getCtime() {
        return ctime;
    }

    public void setCtime(int ctime) {
        this.ctime = ctime;
    }
}

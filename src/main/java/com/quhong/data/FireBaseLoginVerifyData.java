package com.quhong.data;

import com.alibaba.fastjson.JSON;

public class FireBaseLoginVerifyData {
    /***
     *https://firebase.google.com/docs/auth/admin/verify-id-tokens#java
     fierbase mail
     {
     'iss': 'https://securetoken.google.com/findher-87c07',
     'aud': 'findher-87c07',
     'auth_time': **********,
     'user_id': 'FiXlfXpZsxP5JAhSZbgkoxgZxRr2',
     'sub': 'FiXlfXpZsxP5JAhSZbgkoxgZxRr2',
     'iat': **********,
     'exp': **********,
     'email': '<EMAIL>',
     'email_verified': True,
     'firebase': {'identities': {'email': ['<EMAIL>']}, 'sign_in_provider': 'password'},
     'uid': 'FiXlfXpZsxP5JAhSZbgkoxgZxRr2'
     }

     fierbase phone
     {
     'iss': 'https://securetoken.google.com/findher-87c07',
     'aud': 'findher-87c07',
     'auth_time': **********,
     'user_id': 'XbeOBtm3sRdRCYMlo2le6xqwmYq1',
     'sub': 'XbeOBtm3sRdRCYMlo2le6xqwmYq1',
     'iat': **********,
     'exp': **********,
     'phone_number': '+9647904234190',
     'firebase': {'identities': {'phone': ['+9647904234190']}, 'sign_in_provider': 'phone'},
     'uid': 'XbeOBtm3sRdRCYMlo2le6xqwmYq1'
     }
     */
    private String user_id;

    private String email;

    private String phone_number ;

    public String getUser_id() {
        return user_id;
    }

    public void setUser_id(String user_id) {
        this.user_id = user_id;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPhone_number() {
        return phone_number;
    }

    public void setPhone_number(String phone_number) {
        this.phone_number = phone_number;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}

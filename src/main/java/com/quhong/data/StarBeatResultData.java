package com.quhong.data;

import com.alibaba.fastjson.JSON;
import com.quhong.mongo.data.CarnivalGameData;

import java.util.List;
import java.util.Map;

public class StarBeatResultData {
    private StarBeatPoolRewardData starBeatPoolRewardData;
    private int nowStar; // 当前用户累积的幸运值
    private List<AwardBean> awardBeanList; //  中奖开出的列表
    private Map<Integer, Integer> lowSourceMap; //  低价值
    private Map<Integer, Integer> highSourceMap; //  高价值
    private Map<Integer, Integer> allSourceMap; //   全部
    private Map<Integer, CarnivalGameData.PrizeConfig> allPrizeConfig; //  全奖品映射
    private Map<Integer, List<CarnivalGameData.PrizeConfig>> allBoxListMap; // 全礼盒奖品映射
    private int addTicket; // 需要增加的游戏抽奖券
    private CarnivalGameData.PrizeConfig maxPrizeConfig; // 价值最大的奖品

    private Map<Integer, Integer> lowSourceMapA; //  1类用户中奖概率-低价值
    private Map<Integer, Integer> highSourceMapA; // 1类用户中奖概率-高价值
    private Map<Integer, Integer> allSourceMapA; //  1类用户中奖概率-全部

    private Map<Integer, Integer> lowSourceMapB; //  2类用户中奖概率-低价值
    private Map<Integer, Integer> highSourceMapB; // 2类用户中奖概率-高价值
    private Map<Integer, Integer> allSourceMapB; //  2类用户中奖概率-全部

    private Map<Integer, Integer> lowSourceMapC; //  3类用户中奖概率-低价值
    private Map<Integer, Integer> highSourceMapC; // 3类用户中奖概率-高价值
    private Map<Integer, Integer> allSourceMapC; //  3类用户中奖概率-全部

    public static class AwardBean {
        private int awardId;
        private int awardType; // 0 普通抽奖中出 1幸运一击

        public AwardBean() {
        }

        public AwardBean(int awardId, int awardType) {
            this.awardId = awardId;
            this.awardType = awardType;
        }

        public int getAwardId() {
            return awardId;
        }

        public void setAwardId(int awardId) {
            this.awardId = awardId;
        }

        public int getAwardType() {
            return awardType;
        }

        public void setAwardType(int awardType) {
            this.awardType = awardType;
        }

        @Override
        public String toString() {
            return JSON.toJSONString(this);
        }
    }

    public StarBeatResultData() {
    }

    public StarBeatPoolRewardData getStarBeatPoolRewardData() {
        return starBeatPoolRewardData;
    }

    public void setStarBeatPoolRewardData(StarBeatPoolRewardData starBeatPoolRewardData) {
        this.starBeatPoolRewardData = starBeatPoolRewardData;
    }

    public int getNowStar() {
        return nowStar;
    }

    public void setNowStar(int nowStar) {
        this.nowStar = nowStar;
    }

    public List<AwardBean> getAwardBeanList() {
        return awardBeanList;
    }

    public void setAwardBeanList(List<AwardBean> awardBeanList) {
        this.awardBeanList = awardBeanList;
    }

    public Map<Integer, Integer> getLowSourceMap() {
        return lowSourceMap;
    }

    public void setLowSourceMap(Map<Integer, Integer> lowSourceMap) {
        this.lowSourceMap = lowSourceMap;
    }

    public Map<Integer, Integer> getHighSourceMap() {
        return highSourceMap;
    }

    public void setHighSourceMap(Map<Integer, Integer> highSourceMap) {
        this.highSourceMap = highSourceMap;
    }

    public Map<Integer, Integer> getAllSourceMap() {
        return allSourceMap;
    }

    public void setAllSourceMap(Map<Integer, Integer> allSourceMap) {
        this.allSourceMap = allSourceMap;
    }

    public Map<Integer, Integer> getLowSourceMapA() {
        return lowSourceMapA;
    }

    public void setLowSourceMapA(Map<Integer, Integer> lowSourceMapA) {
        this.lowSourceMapA = lowSourceMapA;
    }

    public Map<Integer, Integer> getHighSourceMapA() {
        return highSourceMapA;
    }

    public void setHighSourceMapA(Map<Integer, Integer> highSourceMapA) {
        this.highSourceMapA = highSourceMapA;
    }

    public Map<Integer, Integer> getAllSourceMapA() {
        return allSourceMapA;
    }

    public void setAllSourceMapA(Map<Integer, Integer> allSourceMapA) {
        this.allSourceMapA = allSourceMapA;
    }

    public Map<Integer, Integer> getLowSourceMapB() {
        return lowSourceMapB;
    }

    public void setLowSourceMapB(Map<Integer, Integer> lowSourceMapB) {
        this.lowSourceMapB = lowSourceMapB;
    }

    public Map<Integer, Integer> getHighSourceMapB() {
        return highSourceMapB;
    }

    public void setHighSourceMapB(Map<Integer, Integer> highSourceMapB) {
        this.highSourceMapB = highSourceMapB;
    }

    public Map<Integer, Integer> getAllSourceMapB() {
        return allSourceMapB;
    }

    public void setAllSourceMapB(Map<Integer, Integer> allSourceMapB) {
        this.allSourceMapB = allSourceMapB;
    }

    public Map<Integer, Integer> getLowSourceMapC() {
        return lowSourceMapC;
    }

    public void setLowSourceMapC(Map<Integer, Integer> lowSourceMapC) {
        this.lowSourceMapC = lowSourceMapC;
    }

    public Map<Integer, Integer> getHighSourceMapC() {
        return highSourceMapC;
    }

    public void setHighSourceMapC(Map<Integer, Integer> highSourceMapC) {
        this.highSourceMapC = highSourceMapC;
    }

    public Map<Integer, Integer> getAllSourceMapC() {
        return allSourceMapC;
    }

    public void setAllSourceMapC(Map<Integer, Integer> allSourceMapC) {
        this.allSourceMapC = allSourceMapC;
    }

    public Map<Integer, CarnivalGameData.PrizeConfig> getAllPrizeConfig() {
        return allPrizeConfig;
    }

    public void setAllPrizeConfig(Map<Integer, CarnivalGameData.PrizeConfig> allPrizeConfig) {
        this.allPrizeConfig = allPrizeConfig;
    }

    public Map<Integer, List<CarnivalGameData.PrizeConfig>> getAllBoxListMap() {
        return allBoxListMap;
    }

    public void setAllBoxListMap(Map<Integer, List<CarnivalGameData.PrizeConfig>> allBoxListMap) {
        this.allBoxListMap = allBoxListMap;
    }

    public int getAddTicket() {
        return addTicket;
    }

    public void setAddTicket(int addTicket) {
        this.addTicket = addTicket;
    }

    public CarnivalGameData.PrizeConfig getMaxPrizeConfig() {
        return maxPrizeConfig;
    }

    public void setMaxPrizeConfig(CarnivalGameData.PrizeConfig maxPrizeConfig) {
        this.maxPrizeConfig = maxPrizeConfig;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}

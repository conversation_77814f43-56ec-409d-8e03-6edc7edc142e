package com.quhong.data.vo;

public class TruthOrDareV2VO {

    private String gameId;
    private String createUid;
    private String roomId;
    private int status;
    private int gameMode;   // 游戏模式 0: 随机话题模式  1: 自定义话题模式
    private int roundNum;   // 当前抽奖轮次
    private Integer selectIndex;
    private String selectedUid;
    private String selectedHead;
    private String selectedUserName;
    private String topicNameEn;
    private String topicNameAr;
    private Integer topicType;

    public String getGameId() {
        return gameId;
    }

    public void setGameId(String gameId) {
        this.gameId = gameId;
    }

    public String getRoomId() {
        return roomId;
    }

    public void setRoomId(String roomId) {
        this.roomId = roomId;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getGameMode() {
        return gameMode;
    }

    public void setGameMode(int gameMode) {
        this.gameMode = gameMode;
    }

    public String getCreateUid() {
        return createUid;
    }

    public void setCreateUid(String createUid) {
        this.createUid = createUid;
    }

    public int getRoundNum() {
        return roundNum;
    }

    public void setRoundNum(int roundNum) {
        this.roundNum = roundNum;
    }

    public Integer getSelectIndex() {
        return selectIndex;
    }

    public void setSelectIndex(Integer selectIndex) {
        this.selectIndex = selectIndex;
    }

    public String getSelectedUid() {
        return selectedUid;
    }

    public void setSelectedUid(String selectedUid) {
        this.selectedUid = selectedUid;
    }

    public String getSelectedHead() {
        return selectedHead;
    }

    public void setSelectedHead(String selectedHead) {
        this.selectedHead = selectedHead;
    }

    public String getSelectedUserName() {
        return selectedUserName;
    }

    public void setSelectedUserName(String selectedUserName) {
        this.selectedUserName = selectedUserName;
    }

    public String getTopicNameEn() {
        return topicNameEn;
    }

    public void setTopicNameEn(String topicNameEn) {
        this.topicNameEn = topicNameEn;
    }

    public String getTopicNameAr() {
        return topicNameAr;
    }

    public void setTopicNameAr(String topicNameAr) {
        this.topicNameAr = topicNameAr;
    }

    public Integer getTopicType() {
        return topicType;
    }

    public void setTopicType(Integer topicType) {
        this.topicType = topicType;
    }
}

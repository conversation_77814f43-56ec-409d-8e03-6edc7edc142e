package com.quhong.data.vo;


import com.quhong.mongo.data.ResourceKeyConfigData;

import java.util.List;

public class CrashExchangeShopVO extends OtherRankConfigVO {

    private List<OtherRankingListVO> gameRankingList; // 下注钻石列表
    private Integer nowPoint;// 当前积分
    private  List<RollRecordVO> rollRecordList; // 最近5条抽奖记录
    private OtherRankingListVO myRank; // 我的排名
    private String gameUrl; // 游戏地址
    private Integer isAutoDraw; // 是否开启自动抽奖 0否 1是

    public static class ResourceMetaTmp extends ResourceKeyConfigData.ResourceMeta {
        private int awardNum; // 弹窗用，资源抽中个数

        private int awardType; // 历史记录用，获得的类型，1 盲盒 2 商店兑换
        private int ctime; // 历史记录用，抽奖时间

        private int costPoint; // 消耗的积分

        public int getAwardNum() {
            return awardNum;
        }

        public void setAwardNum(int awardNum) {
            this.awardNum = awardNum;
        }

        public int getAwardType() {
            return awardType;
        }

        public void setAwardType(int awardType) {
            this.awardType = awardType;
        }

        public int getCtime() {
            return ctime;
        }

        public void setCtime(int ctime) {
            this.ctime = ctime;
        }

        public int getCostPoint() {
            return costPoint;
        }

        public void setCostPoint(int costPoint) {
            this.costPoint = costPoint;
        }
    }

    public static class CrashExchangeHistoryVO {
        private int nextPage;
        private List<ResourceMetaTmp> myHistoryList;

        public int getNextPage() {
            return nextPage;
        }

        public void setNextPage(int nextPage) {
            this.nextPage = nextPage;
        }

        public List<ResourceMetaTmp> getMyHistoryList() {
            return myHistoryList;
        }

        public void setMyHistoryList(List<ResourceMetaTmp> myHistoryList) {
            this.myHistoryList = myHistoryList;
        }
    }

    public static class CrashExchangeDrawVO {
        private int drawNum;
        private List<ResourceMetaTmp> myDrawyList;

        public int getDrawNum() {
            return drawNum;
        }

        public void setDrawNum(int drawNum) {
            this.drawNum = drawNum;
        }

        public List<ResourceMetaTmp> getMyDrawyList() {
            return myDrawyList;
        }

        public void setMyDrawyList(List<ResourceMetaTmp> myDrawyList) {
            this.myDrawyList = myDrawyList;
        }
    }

    public static class RollRecordVO {
        private String uid; // 用户id
        private String name; // 用户名
        private String resourceIcon; // 国家名
        private Integer ctime; //
        private Integer costValue; // 价值积分

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getUid() {
            return uid;
        }

        public void setUid(String uid) {
            this.uid = uid;
        }

        public String getResourceIcon() {
            return resourceIcon;
        }

        public void setResourceIcon(String resourceIcon) {
            this.resourceIcon = resourceIcon;
        }

        public Integer getCostValue() {
            return costValue;
        }

        public void setCostValue(Integer costValue) {
            this.costValue = costValue;
        }

        public Integer getCtime() {
            return ctime;
        }

        public void setCtime(Integer ctime) {
            this.ctime = ctime;
        }
    }

    public Integer getNowPoint() {
        return nowPoint;
    }

    public void setNowPoint(Integer nowPoint) {
        this.nowPoint = nowPoint;
    }

    public List<OtherRankingListVO> getGameRankingList() {
        return gameRankingList;
    }

    public void setGameRankingList(List<OtherRankingListVO> gameRankingList) {
        this.gameRankingList = gameRankingList;
    }

    public List<RollRecordVO> getRollRecordList() {
        return rollRecordList;
    }

    public void setRollRecordList(List<RollRecordVO> rollRecordList) {
        this.rollRecordList = rollRecordList;
    }

    public String getGameUrl() {
        return gameUrl;
    }

    public void setGameUrl(String gameUrl) {
        this.gameUrl = gameUrl;
    }

    public OtherRankingListVO getMyRank() {
        return myRank;
    }

    public void setMyRank(OtherRankingListVO myRank) {
        this.myRank = myRank;
    }

    public Integer getIsAutoDraw() {
        return isAutoDraw;
    }

    public void setIsAutoDraw(Integer isAutoDraw) {
        this.isAutoDraw = isAutoDraw;
    }
}

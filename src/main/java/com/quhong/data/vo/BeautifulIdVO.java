package com.quhong.data.vo;

import com.alibaba.fastjson.annotation.JSONField;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/26
 */
public class BeautifulIdVO {

    private List<List<IdData>> list;

    private Integer rid;

    @J<PERSON><PERSON>ield(name = "is_beautiful_id")
    private Integer isBeautifulId;

    @JSONField(name = "end_days")
    private String endDays;

    @JSONField(name = "bir_permanent_type")
    private Integer birPermanentType;

    @JSONField(name = "alpha_rid")
    private String alphaRid;

    @JSONField(name = "alpha_type")
    private Integer alphaType;

    public static class IdData {

        private String rid;
        private Integer beans;
        private Integer status;

        public String getRid() {
            return rid;
        }

        public void setRid(String rid) {
            this.rid = rid;
        }

        public Integer getBeans() {
            return beans;
        }

        public void setBeans(Integer beans) {
            this.beans = beans;
        }

        public Integer getStatus() {
            return status;
        }

        public void setStatus(Integer status) {
            this.status = status;
        }
    }

    public List<List<IdData>> getList() {
        return list;
    }

    public void setList(List<List<IdData>> list) {
        this.list = list;
    }

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public Integer getIsBeautifulId() {
        return isBeautifulId;
    }

    public void setIsBeautifulId(Integer isBeautifulId) {
        this.isBeautifulId = isBeautifulId;
    }

    public String getEndDays() {
        return endDays;
    }

    public void setEndDays(String endDays) {
        this.endDays = endDays;
    }

    public Integer getBirPermanentType() {
        return birPermanentType;
    }

    public void setBirPermanentType(Integer birPermanentType) {
        this.birPermanentType = birPermanentType;
    }

    public String getAlphaRid() {
        return alphaRid;
    }

    public void setAlphaRid(String alphaRid) {
        this.alphaRid = alphaRid;
    }

    public Integer getAlphaType() {
        return alphaType;
    }

    public void setAlphaType(Integer alphaType) {
        this.alphaType = alphaType;
    }
}

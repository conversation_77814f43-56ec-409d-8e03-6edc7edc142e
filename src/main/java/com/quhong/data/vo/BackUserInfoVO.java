package com.quhong.data.vo;

import com.quhong.data.CoreRewardConfigData;
import com.quhong.mongo.data.ResourceKeyConfigData;

import java.util.List;

public class BackUserInfoVO {
    private Integer lossDays; // 流失天数
    private String name;
    private String head ;
    private Integer regTime; // 注册时间戳
    private Integer aliveTime; //流失之前在youstar玩的时间 (时间戳)  logoutTime-regTime
    private Integer friendCount; //朋友数量
    private Integer endTime; //回归活动的结束时间戳
    private List<BackUserItemBag> chargeBagList; //充值礼包
    private List<BackUserItemBag> dauBagList;// 7天内日活礼包
    private Integer isShowPop; //  是否首次进来

    public static class BackUserItemBag {
        private String itemName;
        private Integer itemState; // 0 待完成 1 待领取 2 已完成
        private List<CoreRewardConfigData> configDataList;
        // 新版回归活动配置
        private List<ResourceKeyConfigData.ResourceMeta> configDataList2;

        public String getItemName() {
            return itemName;
        }

        public void setItemName(String itemName) {
            this.itemName = itemName;
        }

        public Integer getItemState() {
            return itemState;
        }

        public void setItemState(Integer itemState) {
            this.itemState = itemState;
        }

        public List<CoreRewardConfigData> getConfigDataList() {
            return configDataList;
        }

        public void setConfigDataList(List<CoreRewardConfigData> configDataList) {
            this.configDataList = configDataList;
        }

        public List<ResourceKeyConfigData.ResourceMeta> getConfigDataList2() {
            return configDataList2;
        }

        public void setConfigDataList2(List<ResourceKeyConfigData.ResourceMeta> configDataList2) {
            this.configDataList2 = configDataList2;
        }
    }

    public Integer getLossDays() {
        return lossDays;
    }

    public void setLossDays(Integer lossDays) {
        this.lossDays = lossDays;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getRegTime() {
        return regTime;
    }

    public void setRegTime(Integer regTime) {
        this.regTime = regTime;
    }

    public Integer getAliveTime() {
        return aliveTime;
    }

    public void setAliveTime(Integer aliveTime) {
        this.aliveTime = aliveTime;
    }

    public Integer getFriendCount() {
        return friendCount;
    }

    public void setFriendCount(Integer friendCount) {
        this.friendCount = friendCount;
    }

    public Integer getEndTime() {
        return endTime;
    }

    public void setEndTime(Integer endTime) {
        this.endTime = endTime;
    }

    public List<BackUserItemBag> getChargeBagList() {
        return chargeBagList;
    }

    public void setChargeBagList(List<BackUserItemBag> chargeBagList) {
        this.chargeBagList = chargeBagList;
    }

    public List<BackUserItemBag> getDauBagList() {
        return dauBagList;
    }

    public void setDauBagList(List<BackUserItemBag> dauBagList) {
        this.dauBagList = dauBagList;
    }

    public String getHead() {
        return head;
    }

    public void setHead(String head) {
        this.head = head;
    }

    public Integer getIsShowPop() {
        return isShowPop;
    }

    public void setIsShowPop(Integer isShowPop) {
        this.isShowPop = isShowPop;
    }
}

package com.quhong.data.vo;


import java.util.List;

public class ShootVO extends OtherRankConfigVO {
    private Integer before;              // 前锋卡片数量
    private Integer center;              // 中卫卡片数量
    private Integer after;               // 后卫卡片数量
    private Integer guard;               // 守卫卡片数量
    private Integer firstEntry;          // 是否首次进入

    // 抽卡片返回
    private PrizeConfigVO drawCardVO;

    // 射门抽奖记录
    private List<PrizeConfigVO> drawRecordList;
    private List<PrizeConfigVO> friendList;
    private Integer nextUrl;


    public Integer getBefore() {
        return before;
    }

    public void setBefore(Integer before) {
        this.before = before;
    }

    public Integer getCenter() {
        return center;
    }

    public void setCenter(Integer center) {
        this.center = center;
    }

    public Integer getAfter() {
        return after;
    }

    public void setAfter(Integer after) {
        this.after = after;
    }

    public Integer getGuard() {
        return guard;
    }

    public void setGuard(Integer guard) {
        this.guard = guard;
    }

    public Integer getFirstEntry() {
        return firstEntry;
    }

    public void setFirstEntry(Integer firstEntry) {
        this.firstEntry = firstEntry;
    }

    public PrizeConfigVO getDrawCardVO() {
        return drawCardVO;
    }

    public void setDrawCardVO(PrizeConfigVO drawCardVO) {
        this.drawCardVO = drawCardVO;
    }

    public List<PrizeConfigVO> getDrawRecordList() {
        return drawRecordList;
    }

    public void setDrawRecordList(List<PrizeConfigVO> drawRecordList) {
        this.drawRecordList = drawRecordList;
    }

    public List<PrizeConfigVO> getFriendList() {
        return friendList;
    }

    public void setFriendList(List<PrizeConfigVO> friendList) {
        this.friendList = friendList;
    }

    public Integer getNextUrl() {
        return nextUrl;
    }

    public void setNextUrl(Integer nextUrl) {
        this.nextUrl = nextUrl;
    }
}

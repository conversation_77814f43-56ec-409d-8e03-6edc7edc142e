package com.quhong.data.vo;

import com.alibaba.fastjson.annotation.JSONField;

/**
 * <AUTHOR>
 * @date 2022/8/26
 */
public class StoreInfoVO {

    private Integer price;
    private Integer status;
    @JSONField(name = "left_days")
    private Integer leftDays;
    private Integer viplevel;

    public Integer getPrice() {
        return price;
    }

    public void setPrice(Integer price) {
        this.price = price;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getLeftDays() {
        return leftDays;
    }

    public void setLeftDays(Integer leftDays) {
        this.leftDays = leftDays;
    }

    public Integer getViplevel() {
        return viplevel;
    }

    public void setViplevel(Integer viplevel) {
        this.viplevel = viplevel;
    }
}

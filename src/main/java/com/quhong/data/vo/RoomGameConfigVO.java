package com.quhong.data.vo;


import java.util.List;

public class RoomGameConfigVO {

    private List<RoomGameConfigVO> gameInfoList;
    private List<GameItemInfoVO> moreGameList;
    /**
     * 游戏类型
     */
    private Integer gameType;
    private String gameId;
    private Integer currencyType; //  入场费货币类型
    private Integer currencyValue; // 入场费
    /**
     * 游戏名
     */
    private String name;
    /**
     * 阿语游戏名
     */
    private String nameAr;
    /**
     * 选择游戏图标地址
     */
    private String iconUrl;
    private List<Integer> coinFeeList; // 金币费用列表。从小到大
    private List<Integer> diamondFeeList; // 钻石列表
    /**
     * h5游戏的宽
     */
    private Integer width;
    /**
     * h5游戏的高
     */
    private Integer height;

    /**
     * 背景
     */
    private String background;

    /**
     * 创建游戏的状态 1 成功 2 失败 该房间有正在进行的其他游戏
     */
    private Integer createGameStatus;

    private String gameUrl;

    private Integer leftCoins;

    /**
     * 进房右上角小图标
     */
    private String smallIconUrl;

    /**
     * keep出房间后的小图标
     */
    private String keepIconUrl;

    public Integer getGameType() {
        return gameType;
    }

    public void setGameType(Integer gameType) {
        this.gameType = gameType;
    }

    public Integer getCurrencyType() {
        return currencyType;
    }

    public void setCurrencyType(Integer currencyType) {
        this.currencyType = currencyType;
    }

    public Integer getCurrencyValue() {
        return currencyValue;
    }

    public void setCurrencyValue(Integer currencyValue) {
        this.currencyValue = currencyValue;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getNameAr() {
        return nameAr;
    }

    public void setNameAr(String nameAr) {
        this.nameAr = nameAr;
    }

    public String getIconUrl() {
        return iconUrl;
    }

    public void setIconUrl(String iconUrl) {
        this.iconUrl = iconUrl;
    }

    public Integer getWidth() {
        return width;
    }

    public void setWidth(Integer width) {
        this.width = width;
    }

    public Integer getHeight() {
        return height;
    }

    public void setHeight(Integer height) {
        this.height = height;
    }

    public List<Integer> getCoinFeeList() {
        return coinFeeList;
    }

    public void setCoinFeeList(List<Integer> coinFeeList) {
        this.coinFeeList = coinFeeList;
    }

    public List<Integer> getDiamondFeeList() {
        return diamondFeeList;
    }

    public void setDiamondFeeList(List<Integer> diamondFeeList) {
        this.diamondFeeList = diamondFeeList;
    }

    public List<RoomGameConfigVO> getGameInfoList() {
        return gameInfoList;
    }

    public void setGameInfoList(List<RoomGameConfigVO> gameInfoList) {
        this.gameInfoList = gameInfoList;
    }

    public List<GameItemInfoVO> getMoreGameList() {
        return moreGameList;
    }

    public void setMoreGameList(List<GameItemInfoVO> moreGameList) {
        this.moreGameList = moreGameList;
    }

    public Integer getCreateGameStatus() {
        return createGameStatus;
    }

    public void setCreateGameStatus(Integer createGameStatus) {
        this.createGameStatus = createGameStatus;
    }

    public String getGameId() {
        return gameId;
    }

    public void setGameId(String gameId) {
        this.gameId = gameId;
    }

    public String getGameUrl() {
        return gameUrl;
    }

    public void setGameUrl(String gameUrl) {
        this.gameUrl = gameUrl;
    }

    public Integer getLeftCoins() {
        return leftCoins;
    }

    public void setLeftCoins(Integer leftCoins) {
        this.leftCoins = leftCoins;
    }

    public String getBackground() {
        return background;
    }

    public void setBackground(String background) {
        this.background = background;
    }

    public String getSmallIconUrl() {
        return smallIconUrl;
    }

    public void setSmallIconUrl(String smallIconUrl) {
        this.smallIconUrl = smallIconUrl;
    }

    public String getKeepIconUrl() {
        return keepIconUrl;
    }

    public void setKeepIconUrl(String keepIconUrl) {
        this.keepIconUrl = keepIconUrl;
    }
}

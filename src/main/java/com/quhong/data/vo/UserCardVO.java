package com.quhong.data.vo;

import com.quhong.data.RidData;
import com.quhong.data.UserCardFrameData;
import com.quhong.data.dto.BadgeListDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/26
 */
public class UserCardVO {

    private String name;
    private String nameV2;
    private String head;
    private String desc;
    private int gender;
    private int age;
    private String country;
    private int rid;
    private RidData ridData;
    private int os;
    private int valid;
    private int rideOption;
    private String aid;
    private String alphaRid;
    private int alphaType;
    private int ulvl;
    private List<String> badge;
    private int badgeCount; // 勋章数量
    private List<BadgeListDTO> badgeModelList; // 勋章列表（新版本）
    private int joinCartonId;
    private String rippleUrl;
    private String cartonTimes;
    private String smallIcon;
    private int sourceType;
    private int joinCartonType;
    private int isBeautifulRid;  // 是否是靓号
    private String devote;
    private String micFrame;
    private int identify;
    private int isMember;
    private int role;
    private int roleData;
    private int viceHost;
    private int forbidEnTime;
    private int micForbidEnTime;
    private int isFollowed;
    private int friends;
    private boolean block;
    private int vipLevelV2;
    private String vipMedal;
    private int showRookie;
    private int importantUser;
    private int slang;
    private UserGameInfo userGameInfo;
    private int isNewUser;
    private List<GuardRankUserVO> guardianTopThree; // 守护者前三名
    private UserCardFrameData userCardFrame;

    public static class UserGameInfo {
        private long giftBeans;
        private long winNum; //
        private long joinNum; //
        private String winRate;//

        public UserGameInfo() {
        }

        public UserGameInfo(Long giftBeans, Long winNum, Long joinNum, String winRate) {
            this.giftBeans = giftBeans;
            this.winNum = winNum;
            this.joinNum = joinNum;
            this.winRate = winRate;
        }

        public Long getGiftBeans() {
            return giftBeans;
        }

        public void setGiftBeans(Long giftBeans) {
            this.giftBeans = giftBeans;
        }

        public Long getWinNum() {
            return winNum;
        }

        public void setWinNum(Long winNum) {
            this.winNum = winNum;
        }

        public Long getJoinNum() {
            return joinNum;
        }

        public void setJoinNum(Long joinNum) {
            this.joinNum = joinNum;
        }

        public String getWinRate() {
            return winRate;
        }

        public void setWinRate(String winRate) {
            this.winRate = winRate;
        }
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getHead() {
        return head;
    }

    public void setHead(String head) {
        this.head = head;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public int getGender() {
        return gender;
    }

    public void setGender(int gender) {
        this.gender = gender;
    }

    public int getAge() {
        return age;
    }

    public void setAge(int age) {
        this.age = age;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public int getRid() {
        return rid;
    }

    public void setRid(int rid) {
        this.rid = rid;
    }

    public int getOs() {
        return os;
    }

    public void setOs(int os) {
        this.os = os;
    }

    public int getValid() {
        return valid;
    }

    public void setValid(int valid) {
        this.valid = valid;
    }

    public String getAid() {
        return aid;
    }

    public void setAid(String aid) {
        this.aid = aid;
    }

    public String getAlphaRid() {
        return alphaRid;
    }

    public void setAlphaRid(String alphaRid) {
        this.alphaRid = alphaRid;
    }

    public int getAlphaType() {
        return alphaType;
    }

    public void setAlphaType(int alphaType) {
        this.alphaType = alphaType;
    }

    public int getRideOption() {
        return rideOption;
    }

    public void setRideOption(int rideOption) {
        this.rideOption = rideOption;
    }

    public int getUlvl() {
        return ulvl;
    }

    public void setUlvl(int ulvl) {
        this.ulvl = ulvl;
    }

    public List<String> getBadge() {
        return badge;
    }

    public void setBadge(List<String> badge) {
        this.badge = badge;
    }

    public int getJoinCartonId() {
        return joinCartonId;
    }

    public void setJoinCartonId(int joinCartonId) {
        this.joinCartonId = joinCartonId;
    }

    public String getRippleUrl() {
        return rippleUrl;
    }

    public void setRippleUrl(String rippleUrl) {
        this.rippleUrl = rippleUrl;
    }

    public String getCartonTimes() {
        return cartonTimes;
    }

    public void setCartonTimes(String cartonTimes) {
        this.cartonTimes = cartonTimes;
    }

    public String getSmallIcon() {
        return smallIcon;
    }

    public void setSmallIcon(String smallIcon) {
        this.smallIcon = smallIcon;
    }

    public int getSourceType() {
        return sourceType;
    }

    public void setSourceType(int sourceType) {
        this.sourceType = sourceType;
    }

    public int getJoinCartonType() {
        return joinCartonType;
    }

    public void setJoinCartonType(int joinCartonType) {
        this.joinCartonType = joinCartonType;
    }

    public int getIsBeautifulRid() {
        return isBeautifulRid;
    }

    public void setIsBeautifulRid(int isBeautifulRid) {
        this.isBeautifulRid = isBeautifulRid;
    }

    public String getDevote() {
        return devote;
    }

    public void setDevote(String devote) {
        this.devote = devote;
    }

    public String getMicFrame() {
        return micFrame;
    }

    public void setMicFrame(String micFrame) {
        this.micFrame = micFrame;
    }

    public int getIdentify() {
        return identify;
    }

    public void setIdentify(int identify) {
        this.identify = identify;
    }

    public int getIsMember() {
        return isMember;
    }

    public void setIsMember(int isMember) {
        this.isMember = isMember;
    }

    public int getRole() {
        return role;
    }

    public void setRole(int role) {
        this.role = role;
    }

    public int getRoleData() {
        return roleData;
    }

    public void setRoleData(int roleData) {
        this.roleData = roleData;
    }

    public int getViceHost() {
        return viceHost;
    }

    public void setViceHost(int viceHost) {
        this.viceHost = viceHost;
    }

    public int getForbidEnTime() {
        return forbidEnTime;
    }

    public void setForbidEnTime(int forbidEnTime) {
        this.forbidEnTime = forbidEnTime;
    }

    public int getMicForbidEnTime() {
        return micForbidEnTime;
    }

    public void setMicForbidEnTime(int micForbidEnTime) {
        this.micForbidEnTime = micForbidEnTime;
    }

    public int getIsFollowed() {
        return isFollowed;
    }

    public void setIsFollowed(int isFollowed) {
        this.isFollowed = isFollowed;
    }

    public int getFriends() {
        return friends;
    }

    public void setFriends(int friends) {
        this.friends = friends;
    }

    public boolean getBlock() {
        return block;
    }

    public void setBlock(boolean block) {
        this.block = block;
    }

    public int getShowRookie() {
        return showRookie;
    }

    public void setShowRookie(int showRookie) {
        this.showRookie = showRookie;
    }

    public int getImportantUser() {
        return importantUser;
    }

    public void setImportantUser(int importantUser) {
        this.importantUser = importantUser;
    }

    public int getSlang() {
        return slang;
    }

    public void setSlang(int slang) {
        this.slang = slang;
    }

    public String getNameV2() {
        return nameV2;
    }

    public void setNameV2(String nameV2) {
        this.nameV2 = nameV2;
    }

    public boolean isBlock() {
        return block;
    }

    public int getVipLevelV2() {
        return vipLevelV2;
    }

    public void setVipLevelV2(int vipLevelV2) {
        this.vipLevelV2 = vipLevelV2;
    }

    public String getVipMedal() {
        return vipMedal;
    }

    public void setVipMedal(String vipMedal) {
        this.vipMedal = vipMedal;
    }

    public RidData getRidData() {
        return ridData;
    }

    public void setRidData(RidData ridData) {
        this.ridData = ridData;
    }

    public UserGameInfo getUserGameInfo() {
        return userGameInfo;
    }

    public void setUserGameInfo(UserGameInfo userGameInfo) {
        this.userGameInfo = userGameInfo;
    }

    public int getIsNewUser() {
        return isNewUser;
    }

    public void setIsNewUser(int isNewUser) {
        this.isNewUser = isNewUser;
    }

    public List<GuardRankUserVO> getGuardianTopThree() {
        return guardianTopThree;
    }

    public void setGuardianTopThree(List<GuardRankUserVO> guardianTopThree) {
        this.guardianTopThree = guardianTopThree;
    }

    public UserCardFrameData getUserCardFrame() {
        return userCardFrame;
    }

    public void setUserCardFrame(UserCardFrameData userCardFrame) {
        this.userCardFrame = userCardFrame;
    }

    public int getBadgeCount() {
        return badgeCount;
    }

    public void setBadgeCount(int badgeCount) {
        this.badgeCount = badgeCount;
    }

    public List<BadgeListDTO> getBadgeModelList() {
        return badgeModelList;
    }

    public void setBadgeModelList(List<BadgeListDTO> badgeModelList) {
        this.badgeModelList = badgeModelList;
    }
}

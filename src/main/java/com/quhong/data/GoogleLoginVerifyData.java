package com.quhong.data;

import com.alibaba.fastjson.JSON;

public class GoogleLoginVerifyData {
    /***
     * https://developers.google.com/identity/protocols/oauth2/openid-connect
     {
     "iss": "https://accounts.google.com",
     "azp": "***********-dpv474al4rhkf7i54qb8vilhd3jmo1r8.apps.googleusercontent.com",
     "aud": "***********-pcmpj12fq2jle5ffr0kmu73h0fpa9moq.apps.googleusercontent.com",
     "sub": "111291668344166007346",
     "name": "حسن علي سالم",
     "picture": "https://lh3.googleusercontent.com/a/AItbvmlqnBCyxUsqOh5dSp7v1FpCtK43SxPg41KBRclO=s96-c",
     "given_name": "حسن علي سالم",
     "locale": "ar",
     "iat": "**********",
     "exp": "**********",
     "alg": "RS256",
     "kid": "e847d9948e8545948fa8157b73e915c567302d4e",
     "typ": "JWT"
     }
     */
    private String iss;

    private String azp;

    private String aud;

    private String sub;

    private String name;

    private String picture;

    private String locale;

    private int iat;

    private int exp;

    private String kid;

    private int issued_at;

    private String alg;

    private String given_name;

    private String typ;

    public String getIss() {
        return iss;
    }

    public void setIss(String iss) {
        this.iss = iss;
    }

    public String getAzp() {
        return azp;
    }

    public void setAzp(String azp) {
        this.azp = azp;
    }

    public String getAud() {
        return aud;
    }

    public void setAud(String aud) {
        this.aud = aud;
    }

    public String getSub() {
        return sub;
    }

    public void setSub(String sub) {
        this.sub = sub;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPicture() {
        return picture;
    }

    public void setPicture(String picture) {
        this.picture = picture;
    }

    public String getLocale() {
        return locale;
    }

    public void setLocale(String locale) {
        this.locale = locale;
    }

    public int getIat() {
        return iat;
    }

    public void setIat(int iat) {
        this.iat = iat;
    }

    public int getExp() {
        return exp;
    }

    public void setExp(int exp) {
        this.exp = exp;
    }

    public String getKid() {
        return kid;
    }

    public void setKid(String kid) {
        this.kid = kid;
    }

    public int getIssued_at() {
        return issued_at;
    }

    public void setIssued_at(int issued_at) {
        this.issued_at = issued_at;
    }

    public String getAlg() {
        return alg;
    }

    public void setAlg(String alg) {
        this.alg = alg;
    }

    public String getGiven_name() {
        return given_name;
    }

    public void setGiven_name(String given_name) {
        this.given_name = given_name;
    }

    public String getTyp() {
        return typ;
    }

    public void setTyp(String typ) {
        this.typ = typ;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}

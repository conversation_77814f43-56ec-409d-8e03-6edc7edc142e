package com.quhong.data.dto;

import com.quhong.handler.HttpEnvData;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/7/13
 */
public class RoomAdminDTO extends HttpEnvData {

    private String aid;
    /**
     * 0操作管理员 1操作副房主
     */
    private int is_vice;

    private int roomRelateSource; // 数数埋点场景字段 1为房间资料页，2为房间顶部，3为公屏消息引导

    public String getAid() {
        return aid;
    }

    public void setAid(String aid) {
        this.aid = aid;
    }

    public int getIs_vice() {
        return is_vice;
    }

    public void setIs_vice(int is_vice) {
        this.is_vice = is_vice;
    }

    public int getRoomRelateSource() {
        return roomRelateSource;
    }

    public void setRoomRelateSource(int roomRelateSource) {
        this.roomRelateSource = roomRelateSource;
    }
}

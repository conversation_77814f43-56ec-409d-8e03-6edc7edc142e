package com.quhong.data.dto;

import com.quhong.handler.HttpEnvData;

/**
 * <AUTHOR>
 * @date 2023/10/31
 */
public class UniqueIdDTO extends HttpEnvData {

    /**
     * 被操作用户的uid
     */
    private String aid;

    /**
     * 靓号
     */
    private String uniqueId;

    /**
     * 有效天数 -1 永久 0 当前靓号剩余天数 -2 输入天数或者配置的天数值取customDays
     */
    private int validDays;

    /**
     * 样式
     */
    private int level;
    /**
     * 配置天数
     */
    private int customDays;

    public String getAid() {
        return aid;
    }

    public void setAid(String aid) {
        this.aid = aid;
    }

    public String getUniqueId() {
        return uniqueId;
    }

    public void setUniqueId(String uniqueId) {
        this.uniqueId = uniqueId;
    }

    public int getValidDays() {
        return validDays;
    }

    public void setValidDays(int validDays) {
        this.validDays = validDays;
    }

    public int getLevel() {
        return level;
    }

    public void setLevel(int level) {
        this.level = level;
    }

    public int getCustomDays() {
        return customDays;
    }

    public void setCustomDays(int customDays) {
        this.customDays = customDays;
    }
}

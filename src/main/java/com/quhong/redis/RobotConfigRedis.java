package com.quhong.redis;

import com.alibaba.fastjson.JSONObject;
import com.quhong.data.RobotConfigData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;

@Component
public class RobotConfigRedis {

    private static Logger logger = LoggerFactory.getLogger(RobotConfigRedis.class);

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate redisTemplate;

    public RobotConfigData getRobotConfigData() {
        String strData = redisTemplate.opsForValue().get(getKey());
        if (StringUtils.isEmpty(strData)) {
            return new RobotConfigData();
        }
        return JSONObject.parseObject(strData, RobotConfigData.class);
    }


    public void setRobotConfigData(RobotConfigData data) {
        redisTemplate.opsForValue().set(getKey(), JSONObject.toJSONString(data));
    }


    private String getKey() {
        return "str:gen_robot_config";
    }
}

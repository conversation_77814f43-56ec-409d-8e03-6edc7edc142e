package com.quhong.redis;

import com.quhong.constant.DetectConstant;
import com.quhong.core.date.DateSupport;
import com.quhong.core.utils.DateHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

@Lazy
@Component
public class DetectRedis {
    private static final Logger logger = LoggerFactory.getLogger(DetectRedis.class);
    private static final int COMMON_EXPIRE_DAYS = 6;

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterTemplate;

    /**
     * 获取token key
     */
    private String getAccessTokenKey() {
        return "str:aestron_access_token";
    }

    /**
     * 获取token
     */
    public String getAccessToken() {
        try {
            return clusterTemplate.opsForValue().get(getAccessTokenKey());
        } catch (Exception e) {
            logger.info("getAccessToken error e={}", e.getMessage());
            return "";
        }
    }

    /**
     * 设置token
     */
    public void setAccessToken(String accessToken) {
        try {
            clusterTemplate.opsForValue().set(getAccessTokenKey(), accessToken, COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.info("setAccessToken error e={}", e.getMessage());
        }
    }


    /**
     * 获取文本检测按月统计数量key
     */
    private String getTextMonthKey(String type) {
        if (StringUtils.isEmpty(type)) {
            return "str:bigo_text_month_" + DateHelper.BEIJING.getTableSuffix();
        } else {
            return "str:" + type + "_text_month_" + DateHelper.BEIJING.getTableSuffix();
        }

    }

    /**
     * 获取月统计数量
     */
    public int getTextMonthNum(String type) {
        try {
            String monthNum = clusterTemplate.opsForValue().get(getTextMonthKey(type));
            if (StringUtils.isEmpty(monthNum)) {
                return 0;
            }
            return Integer.parseInt(monthNum);
        } catch (Exception e) {
            logger.info("getTextMonthNum error e={}", e.getMessage());
            return 0;
        }
    }

    /**
     * 增加按月统计数量
     */

    public void incTextMonthNum(String type) {
        try {
            clusterTemplate.opsForValue().increment(getTextMonthKey(type));
            clusterTemplate.expire(getTextMonthKey(type), 90, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.info("incTextMonthNum error e={}", e.getMessage());
        }
    }


    /**
     * 获取文本检测按日统计数量key
     */
    private String getTextDailyKey(String type) {
        if (StringUtils.isEmpty(type)) {
            return "str:bigo_text_daily_" + DateSupport.BEIJING.yyyyMMdd();
        } else {
            return "str:" + type + "_text_daily_" + DateSupport.BEIJING.yyyyMMdd();
        }


    }

    /**
     * 获日统计数量
     */
    public int getTextDailyNum(String type) {
        try {
            String dailyNum = clusterTemplate.opsForValue().get(getTextDailyKey(type));
            if (StringUtils.isEmpty(dailyNum)) {
                return 0;
            }
            return Integer.parseInt(dailyNum);
        } catch (Exception e) {
            logger.info("getTextDailyNum error e={}", e.getMessage());
            return 0;
        }
    }

    /**
     * 增加按日统计数量
     */

    public int incTextDailyNum(String type,int curNum) {
        try {
            Long afterNum = clusterTemplate.opsForValue().increment(getTextDailyKey(type));
            clusterTemplate.expire(getTextDailyKey(type), 90, TimeUnit.DAYS);
            return afterNum == null ? curNum : afterNum.intValue();
        } catch (Exception e) {
            logger.info("incTextDailyNum error e={}", e.getMessage());
        }
        return curNum;
    }


    /**
     * 获取图像检测按月统计数量key
     */
    private String getImageMonthKey(String type) {
        if (StringUtils.isEmpty(type)) {
            return "str:bigo_image_month_" + DateHelper.BEIJING.getTableSuffix();
        } else {
            return "str:" + type + "_image_month_" + DateHelper.BEIJING.getTableSuffix();
        }

    }

    /**
     * 获取月统计数量
     */
    public int getImageMonthNum(String type) {
        try {
            String monthNum = clusterTemplate.opsForValue().get(getImageMonthKey(type));
            if (StringUtils.isEmpty(monthNum)) {
                return 0;
            }
            return Integer.parseInt(monthNum);
        } catch (Exception e) {
            logger.info("getImageMonthNum error e={}", e.getMessage());
            return 0;
        }
    }

    /**
     * 增加按月统计数量
     */

    public void incImageMonthNum(String type) {
        try {
            clusterTemplate.opsForValue().increment(getImageMonthKey(type));
            clusterTemplate.expire(getImageMonthKey(type), 90, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.info("incImageMonthNum error e={}", e.getMessage());
        }
    }


    /**
     * 获取图像检测按日统计数量key
     */
    private String getImageDailyKey(String type) {
        if (StringUtils.isEmpty(type)) {
            return "str:bigo_image_daily_" + DateSupport.BEIJING.yyyyMMdd();
        } else {
            return "str:" + type + "_image_daily_" + DateSupport.BEIJING.yyyyMMdd();
        }

    }

    /**
     * 获日统计数量
     */
    public int getImageDailyNum(String type) {
        try {
            String dailyNum = clusterTemplate.opsForValue().get(getImageDailyKey(type));
            if (StringUtils.isEmpty(dailyNum)) {
                return 0;
            }
            return Integer.parseInt(dailyNum);
        } catch (Exception e) {
            logger.info("getImageDailyNum error e={}", e.getMessage());
            return 0;
        }
    }

    /**
     * 增加按日统计数量
     */

    public int incImageDailyNum(String type,int curNum) {
        try {
            Long afterNum = clusterTemplate.opsForValue().increment(getImageDailyKey(type));
            clusterTemplate.expire(getImageDailyKey(type), 90, TimeUnit.DAYS);
            return afterNum == null ? curNum : afterNum.intValue();
        } catch (Exception e) {
            logger.info("incImageDailyNum error e={}", e.getMessage());
        }
        return curNum;
    }


}

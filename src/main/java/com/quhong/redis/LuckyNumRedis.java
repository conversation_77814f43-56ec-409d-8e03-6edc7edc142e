package com.quhong.redis;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;


@Component
@Lazy
public class LuckyNumRedis {

    private static final Logger logger = LoggerFactory.getLogger(LuckyNumRedis.class);

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    protected StringRedisTemplate redisTemplate;
    private static final int COMMON_EXPIRE_DAYS = 10;


    private String getLuckyNumKey(String roomId) {
        return "list:luckyNum:" + roomId;
    }


    public void initLuckyNumSize(String roomId, List<String> pushList) {
        try {

            redisTemplate.opsForList().rightPushAll(getLuckyNum<PERSON>ey(roomId), pushList);
            redisTemplate.expire(getLuckyNumKey(roomId), COMMON_EXPIRE_DAYS, TimeUnit.DAYS);

        } catch (Exception e) {
            logger.info("initPoolSize error roomId={}  e={}", roomId, e);
        }
    }

    public int getLuckyNumSize(String roomId) {
        try {
            Long poolSize = redisTemplate.opsForList().size(getLuckyNumKey(roomId));
            return poolSize != null ? poolSize.intValue() : 0;
        } catch (Exception e) {
            logger.info("getLuckyNumSize error roomId={}  score={}", roomId, e);
            return 0;
        }
    }

    public void deleteLuckyNumSize(String roomId) {
        try {
            redisTemplate.delete(getLuckyNumKey(roomId));
        } catch (Exception e) {
            logger.info("deleteLuckyNumSize error roomId={}  score={}", roomId, e);
        }
    }

    public String drawLuckyNumKey(String roomId) {
        try {
            String drawLuckyKey = redisTemplate.opsForList().leftPop(getLuckyNumKey(roomId));
            return drawLuckyKey != null ? drawLuckyKey : "0";
        } catch (Exception e) {
            logger.info("drawLuckyNumKey error activityId={}  score={}", roomId, e);
            return "0";
        }
    }


}

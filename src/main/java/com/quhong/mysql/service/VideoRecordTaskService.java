package com.quhong.mysql.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.exception.CommonH5Exception;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mysql.data.VideoRecordReviewData;
import com.quhong.mysql.data.VideoRecordTaskData;
import com.quhong.mysql.mapper.ustar_log.VideoRecordReviewMapper;
import com.quhong.mysql.mapper.ustar_log.VideoRecordTaskMapper;
import com.quhong.operation.clients.tencent.VO.TaskReviewVO;
import com.quhong.operation.constant.CarConstant;
import com.quhong.operation.dao.ManagerDao;
import com.quhong.operation.share.mongobean.Manager;
import com.quhong.operation.share.vo.PageVo;
import com.quhong.utils.RoomUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;

@Service
public class VideoRecordTaskService extends ServiceImpl<VideoRecordTaskMapper, VideoRecordTaskData> {
    private static final Logger logger = LoggerFactory.getLogger(VideoRecordTaskService.class);

    @Resource
    private ActorDao actorDao;
    @Resource
    private VideoRecordReviewMapper videoRecordReviewMapper;
    @Resource
    private ManagerDao managerDao;

    public PageVo<VideoRecordTaskData> getPageRecord(int page) {
        QueryWrapper<VideoRecordTaskData> queryWrapper = new QueryWrapper<>();
        queryWrapper.orderByDesc("ctime");
        PageVo<VideoRecordTaskData> vo = new PageVo<>();
        IPage<VideoRecordTaskData> recordPage = new Page<>(page == 0 ? 1 : page, page == 0 ? 10000 : 10);
        recordPage = baseMapper.selectPage(recordPage, queryWrapper);
        vo.setList(recordPage.getRecords());
        vo.setPages(recordPage.getPages());
        vo.setTotal(recordPage.getTotal());
        return vo;
    }

    public List<VideoRecordTaskData> getTaskListByStatus(int status) {
        return lambdaQuery().eq(VideoRecordTaskData::getStatus, status).list();
    }

    public List<VideoRecordTaskData> getTaskListToStart() {
        return lambdaQuery().eq(VideoRecordTaskData::getStatus, CarConstant.INIT_RECORD).le(VideoRecordTaskData::getStartTime, DateHelper.getNowSeconds()).list();
    }

    public List<VideoRecordTaskData> getTaskListToEnd() {
        return lambdaQuery().eq(VideoRecordTaskData::getStatus, CarConstant.START_RECORD).le(VideoRecordTaskData::getEndTime, DateHelper.getNowSeconds()).list();
    }

    /**
     * 检查时间是否重叠
     *
     * @param roomId    房间ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param excludeId 排除的任务ID（用于更新时排除自身）
     */
    private void timeRangeCheck(String roomId, int startTime, int endTime, String excludeId) {
        List<VideoRecordTaskData> tasks = lambdaQuery().eq(VideoRecordTaskData::getRoomId, roomId).list();
        for (VideoRecordTaskData task : tasks) {
            // 排除自身
            if (task.getId().equals(excludeId)) {
                continue;
            }
            // 检查时间重叠
            // 两个时间段重叠的条件：一个的开始时间小于另一个的结束时间，且一个的结束时间大于另一个的开始时间
            if ((startTime >= task.getStartTime() && startTime <= task.getEndTime())
                    || (endTime >= task.getStartTime() && endTime <= task.getEndTime())) {
                logger.info("time overlap roomId:{} startTime:{} endTime:{}", roomId, startTime, endTime);
                throw new CommonH5Exception(1, "该房间在指定时间段内已有录制任务");
            }
        }
    }

    public void createTask(int rid, int startTime, int endTime) {
        ActorData actorData = actorDao.getActorByRid(rid);
        if (null == actorData) {
            throw new CommonH5Exception(1, "用户不存在");
        }
        String roomId = RoomUtils.formatRoomId(actorData.getUid());
        timeRangeCheck(roomId, startTime, endTime, null);
        int nowSeconds = DateHelper.getNowSeconds();
        VideoRecordTaskData videoRecordTaskData = new VideoRecordTaskData();
        videoRecordTaskData.setId(new ObjectId().toString());
        videoRecordTaskData.setRid(String.valueOf(rid));
        videoRecordTaskData.setRoomId(roomId);
        videoRecordTaskData.setStatus(CarConstant.INIT_RECORD);
        videoRecordTaskData.setAid("");
        videoRecordTaskData.setFilePath("");
        videoRecordTaskData.setDuration("");
        videoRecordTaskData.setStartTime(startTime);
        videoRecordTaskData.setEndTime(endTime);
        videoRecordTaskData.setCtime(nowSeconds);
        videoRecordTaskData.setMtime(nowSeconds);
        baseMapper.insert(videoRecordTaskData);
    }

    public void updateTask(String id, int rid, int startTime, int endTime) {
        VideoRecordTaskData existingTask = getById(id);
        if (existingTask == null) {
            throw new CommonH5Exception(1, "任务不存在");
        }
        if (existingTask.getStatus() != CarConstant.INIT_RECORD) {
            throw new CommonH5Exception(1, "任务已开始，无法修改");
        }
        ActorData actorData = actorDao.getActorByRid(rid);
        if (null == actorData) {
            throw new CommonH5Exception(1, "用户不存在");
        }
        String roomId = RoomUtils.formatRoomId(actorData.getUid());
        timeRangeCheck(roomId, startTime, endTime, id);
        VideoRecordTaskData taskData = new VideoRecordTaskData();
        taskData.setId(id);
        taskData.setRoomId(roomId);
        taskData.setStartTime(startTime);
        taskData.setEndTime(endTime);
        lambdaUpdate().eq(VideoRecordTaskData::getId, id).update(taskData);
    }

    public void updateTaskAid(String id, String aid) {
        VideoRecordTaskData taskData = new VideoRecordTaskData();
        taskData.setAid(aid);
        taskData.setMtime(DateHelper.getNowSeconds());
        lambdaUpdate().eq(VideoRecordTaskData::getId, id).update(taskData);
    }

    public void removeTask(String id) {
        VideoRecordTaskData existingTask = getById(id);
        if (existingTask == null) {
            throw new CommonH5Exception(1, "任务不存在");
        }
        if (existingTask.getStatus() != CarConstant.START_RECORD) {
            throw new CommonH5Exception(1, "任务已开始，无法删除");
        }
        removeById(id);
    }

    public void startRecord(String id) {
        VideoRecordTaskData record = lambdaQuery().eq(VideoRecordTaskData::getId, id).one();
        if (null == record) {
            return;
        }
        if (record.getStatus() != CarConstant.INIT_RECORD) {
            return;
        }
        VideoRecordTaskData videoRecordTaskData = new VideoRecordTaskData();
        videoRecordTaskData.setId(id);
        videoRecordTaskData.setStatus(CarConstant.START_RECORD);
        videoRecordTaskData.setMtime(DateHelper.getNowSeconds());
        lambdaUpdate().eq(VideoRecordTaskData::getId, id).update(videoRecordTaskData);
    }

    public void endRecord(String id) {
        VideoRecordTaskData record = lambdaQuery().eq(VideoRecordTaskData::getId, id).one();
        if (null == record) {
            return;
        }
        if (record.getStatus() == CarConstant.FINISH_RECORD) {
            return;
        }
        VideoRecordTaskData taskData = new VideoRecordTaskData();
        taskData.setStatus(CarConstant.END_RECORD);
        int nowSeconds = DateHelper.getNowSeconds();
        taskData.setMtime(nowSeconds);
        taskData.setEndTime(nowSeconds);
        lambdaUpdate().eq(VideoRecordTaskData::getId, id).update(taskData);
    }

    public void finishRecord(String id, String filePath, String duration) {
        VideoRecordTaskData record = lambdaQuery().eq(VideoRecordTaskData::getId, id).one();
        if (null == record) {
            return;
        }
        VideoRecordTaskData taskData = new VideoRecordTaskData();
        taskData.setFilePath(createCDNUrl(filePath));
        taskData.setDuration(duration);
        taskData.setStatus(CarConstant.FINISH_RECORD);
        taskData.setMtime(DateHelper.getNowSeconds());
        if (null == record.getEndTime() || 0 == record.getEndTime()) {
            taskData.setEndTime(DateHelper.getNowSeconds());
        }
        lambdaUpdate().eq(VideoRecordTaskData::getId, id).update(taskData);
    }

    private static String createCDNUrl(String url) {
        if (StringUtils.isEmpty(url)) {
            return url;
        }
        url = url.replaceAll("http://video-1328627554.cos.ap-singapore.myqcloud.com/", "https://videocos.youstar.live/");
        return url;
    }

    public void taskReview(String uid, String taskId, int duration) {
        Manager manager = managerDao.getDataByUid(uid);
        if (null == manager) {
            return;
        }
        VideoRecordTaskData record = lambdaQuery().eq(VideoRecordTaskData::getId, taskId).one();
        if (null == record) {
            return;
        }
        VideoRecordReviewData reviewData = new VideoRecordReviewData();
        reviewData.setUsername(manager.getAccount());
        reviewData.setTaskId(taskId);
        reviewData.setDuration(duration);
        reviewData.setCtime(DateHelper.getNowSeconds());
        videoRecordReviewMapper.insert(reviewData);
    }

    public List<TaskReviewVO> getUserStatistics() {
        return videoRecordReviewMapper.userStatistics();
    }
}

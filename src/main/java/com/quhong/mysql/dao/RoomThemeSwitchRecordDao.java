package com.quhong.mysql.dao;

import com.quhong.mysql.data.RoomThemeSwitchRecordData;
import com.quhong.mysql.mapper.ustar.RoomThemeSwitchRecordMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2022/6/25
 */
@Component
@Lazy
public class RoomThemeSwitchRecordDao {

    private static final Logger logger = LoggerFactory.getLogger(RoomThemeSwitchRecordDao.class);

    @Resource
    private RoomThemeSwitchRecordMapper recordMapper;

    public RoomThemeSwitchRecordData findLastData(String roomId) {
        return recordMapper.selectLastOneRecord(roomId);
    }

    public void insert(RoomThemeSwitchRecordData data) {
        recordMapper.insert(data);
    }
}

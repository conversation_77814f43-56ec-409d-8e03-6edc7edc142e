package com.quhong.mysql.dao;

import com.alibaba.fastjson.JSON;
import com.quhong.cache.CacheMap;
import com.quhong.constant.ExpireTimeConstant;
import com.quhong.mysql.data.UserFriendExpDetailData;
import com.quhong.redis.DataRedisBean;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;


@Component
public class UserFriendExpDetailDao {
    private static final Logger logger = LoggerFactory.getLogger(UserFriendExpDetailDao.class);
    /**
     * 缓存30分钟
     */
    private static final long CACHE_TIME_MILLIS = 30 * 60 * 1000L;
    private final CacheMap<String, UserFriendExpDetailData> cacheMap;

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate mainRedisTemplate;

    @PostConstruct
    public void postInit() {
        cacheMap.start();
    }

    public UserFriendExpDetailDao() {
        this.cacheMap = new CacheMap<>(CACHE_TIME_MILLIS);
    }

    public UserFriendExpDetailData getUserFriendExpDetail(String uid, String dateStr) {
        if (StringUtils.isEmpty(uid) || StringUtils.isEmpty(dateStr)) {
            return null;
        }
        UserFriendExpDetailData userFriendExpDetailData = cacheMap.getData(getUserFriendExpDetailKey(uid, dateStr), true);
        if (null != userFriendExpDetailData) {
            return userFriendExpDetailData;
        }
        userFriendExpDetailData = getUserFriendExpDetailFromRedis(uid, dateStr);
        if (null != userFriendExpDetailData) {
            cacheMap.cacheData(getUserFriendExpDetailKey(uid, dateStr), userFriendExpDetailData);
            return userFriendExpDetailData;
        } else {
            return null;
        }
    }

    private UserFriendExpDetailData getUserFriendExpDetailFromRedis(String roomId, String dateStr) {
        try {
            String expDetail = mainRedisTemplate.opsForValue().get(getUserFriendExpDetailKey(roomId, dateStr));
            if (StringUtils.isEmpty(expDetail)) {
                return null;
            }
            return JSON.parseObject(expDetail, UserFriendExpDetailData.class);
        } catch (Exception e) {
            logger.error("get UserFriend exp detail from redis error. roomId={} dateStr={}", roomId, dateStr, e);
        }
        return null;
    }

    private void cacheUserFriendExpDetail(UserFriendExpDetailData userFriendExpDetailData, String dateStr) {
        try {
            String key = getUserFriendExpDetailKey(userFriendExpDetailData.getUid(), dateStr);
            String json = JSON.toJSONString(userFriendExpDetailData);
            mainRedisTemplate.opsForValue().set(key, json, ExpireTimeConstant.EXPIRE_DAYS_THREE, TimeUnit.DAYS);
//            cacheMap.cacheData(getUserFriendExpDetailKey(userFriendExpDetailData.getUid(), dateStr), userFriendExpDetailData);
        } catch (Exception e) {
            logger.error("cache UserFriend exp detail error. {}", e.getMessage(), e);
        }
    }

    private String getUserFriendExpDetailKey(String uid, String dateStr) {
        return "str:user_friend_exp_detail:" + dateStr + ":" + uid;
    }

    public void saveOrUpdate(UserFriendExpDetailData detail) {
        cacheUserFriendExpDetail(detail, detail.getDateStr());
    }


}

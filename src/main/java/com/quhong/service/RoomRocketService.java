package com.quhong.service;

import com.alibaba.fastjson.JSONObject;
import com.quhong.config.CaffeineCacheConfig;
import com.quhong.constant.MatchConstant;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.dto.ResourcesDTO;
import com.quhong.data.dto.RoomRocketDTO;
import com.quhong.data.vo.RocketInfoVO;
import com.quhong.data.vo.RocketRewardVO;
import com.quhong.data.vo.RocketVO;
import com.quhong.data.vo.RoomRocketVO;
import com.quhong.data.dto.MoneyDetailReq;
import com.quhong.enums.*;
import com.quhong.exception.CommonException;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.RocketRewardConfigDao;
import com.quhong.mongo.dao.SysConfigDao;
import com.quhong.mongo.data.RocketRewardConfigData;
import com.quhong.mq.MqSenderService;
import com.quhong.msg.room.GetRoomRocketRewardMsg;
import com.quhong.mysql.dao.HeartRecordDao;
import com.quhong.mysql.dao.RocketGainRewardRecordDao;
import com.quhong.mysql.dao.VipInfoDao;
import com.quhong.mysql.data.RocketGainRewardRecordData;
import com.quhong.redis.RoomRocketRedis;
import com.quhong.room.RoomWebSender;
import com.quhong.utils.RoomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/12/17
 */
@Service
public class RoomRocketService {

    private static final Logger logger = LoggerFactory.getLogger(RoomRocketService.class);

    private static final String COIN = "coin";
    private static final String DIAMOND = "diamond";
    private static final String GIFT = "gift";

    private static final int A_TYPE = 930;
    private static final String TITLE = "Room Rocket Reward";
    private static final String DESC = "Room Rocket Reward";

    private static final String GET_COINS_REWARD_DESC = "Congrats to\u202B %s \u202Cfor getting %s gold coins";
    private static final String GET_COINS_REWARD_DESC_AR = "تهانينا لـ %s للحصول على %s كوينزات";

    private static final String GET_DIAMONDS_REWARD_DESC = "Congrats to\u202B %s \u202Cfor getting %s diamonds";
    private static final String GET_DIAMONDS_REWARD_DESC_AR = "تهانينا لـ %s للحصول على %s ماسة";

    private static final String GET_GIFTS_REWARD_DESC = "Congrats to\u202B %s \u202Cfor getting %s gifts";
    private static final String GET_GIFTS_REWARD_DESC_AR = "تهانينا لـ %s للحصول على هدايا %s";

    private static final String GET_PROPS_REWARD_DESC = "Congrats to\u202B %s \u202Cfor getting %s %s days";
    private static final String GET_PROPS_REWARD_DESC_AR = "تهانينا لـ %s للحصول على %s %s يومًا";

    @Resource
    private ActorDao actorDao;
    @Resource
    private RoomRocketRedis roomRocketRedis;
    @Resource
    private RocketGainRewardRecordDao rocketGainRewardRecordDao;
    @Resource
    private RocketRewardConfigDao rocketRewardConfigDao;
    @Resource
    private MqSenderService mqSenderService;
    @Resource
    private HeartRecordDao heartRecordDao;
    @Resource
    private RoomWebSender roomWebSender;
    @Resource
    private SysConfigDao sysConfigDao;
    @Resource
    private VipInfoDao vipInfoDao;
    @Resource
    private RoomRocketService roomRocketService;

    /**
     * 获取房间火箭信息
     */
    public RoomRocketVO getRocketInfo(RoomRocketDTO req) {
        RoomRocketVO vo = new RoomRocketVO();
        String roomId = req.getRoomId();
        int rocketLevel = roomRocketRedis.getRoomRocketLevel(roomId);
        int showRocketLevel = roomRocketRedis.getShowRocketLevel(rocketLevel);
        vo.setRocketLevel(showRocketLevel);
        List<RocketRewardConfigData> configList = rocketRewardConfigDao.findAllFromCache();
        if (CollectionUtils.isEmpty(configList)) {
            configList = new ArrayList<>();
        }
        boolean isSecondRound = getRocketConfigLevel(rocketLevel) > 3;
        Map<Integer, RocketRewardConfigData> configMap = configList.stream().collect(Collectors.toMap(RocketRewardConfigData::getRocketLevel, Function.identity()));
        vo.setRocketLv1(getRocketLvInfo(roomId, 1, rocketLevel, showRocketLevel, configMap.get(isSecondRound ? 4 : 1)));
        vo.setRocketLv2(getRocketLvInfo(roomId, 2, rocketLevel, showRocketLevel, configMap.get(isSecondRound ? 5 : 2)));
        vo.setRocketLv3(getRocketLvInfo(roomId, 3, rocketLevel, showRocketLevel, configMap.get(isSecondRound ? 6 : 3)));
        vo.setRewardRecordList(roomRocketService.getGainRewardRecordList(req.getSlang()));
        return vo;
    }

    /**
     * 获取房间火箭奖励
     */
    public RocketRewardVO getRocketReward(RoomRocketDTO req) {
        ActorData actorData = actorDao.getActorDataFromCache(req.getUid());
        if (actorData == null) {
            logger.error("can not find actor. uid={}", req.getUid());
            throw new CommonException(RoomHttpCode.PARAM_ERROR);
        }
        int rocketLevel = req.getBoxId();
        int nowTime = DateHelper.getNowSeconds();
        if (roomRocketRedis.hasGainedReward(req.getRoomId(), rocketLevel, req.getUid())) {
            logger.info("You have already been rewarded and cannot be re-acquired. uid={} roomId={} boxId={}", req.getUid(), req.getRoomId(), req.getBoxId());
            throw new CommonException(RoomHttpCode.HAVE_ALREADY_BEEN_REWARDED);
        }
        Map<Integer, Integer> allRewardEndTimeMap = roomRocketRedis.getAllRewardEndTime(req.getRoomId());
        Integer endTime = allRewardEndTimeMap.get(rocketLevel);
        if (endTime == null) {
            logger.info("no rewards available. uid={} roomId={} boxId={}", req.getUid(), req.getRoomId(), req.getBoxId());
            throw new CommonException(RoomHttpCode.NO_REWARDS_AVAILABLE);
        }
        if (nowTime > endTime) {
            logger.info("Timed out and cannot be claimed. uid={} roomId={} boxId={}", req.getUid(), req.getRoomId(), req.getBoxId());
            throw new CommonException(RoomHttpCode.TIMED_OUT_AND_CANNOT_BE_CLAIMED);
        }
        roomRocketRedis.addRocketRewardRecord(req.getRoomId(), rocketLevel, req.getUid());
        RocketRewardConfigData rewardConfigData = rocketRewardConfigDao.findDataFromCache(getRocketConfigLevel(rocketLevel));
        RocketRewardVO vo = new RocketRewardVO();
        int costBeans = roomRocketRedis.getRocketRankingScore(req.getRoomId(), rocketLevel, req.getUid());
        if (rewardConfigData != null && !CollectionUtils.isEmpty(rewardConfigData.getDetailList())) {
            List<RocketRewardConfigData.RewardConfigDetail> detailList = rewardConfigData.getDetailList();
            int count = 0;
            for (RocketRewardConfigData.RewardConfigDetail detail : detailList) {
                count++;
                if (meetTheAwardConditions(costBeans, detail)) {
                    if (detail.getRate() < 100) {
                        // 获奖概率非100%的奖励，有概率没法获得
                        long random = ThreadLocalRandom.current().nextLong(0, 100);
                        if (random >= detail.getRate()) {
                            vo.setHasReward(0);
                            return vo;
                        }
                    }
                    if (DIAMOND.equals(detail.getRewardType())) {
                        sendDiamondsReward(req.getUid(), A_TYPE, detail.getRewardNum(), TITLE, "");
                    } else if (COIN.equals(detail.getRewardType())) {
                        sendCoinReward(req.getUid(), detail.getRewardNum(), TITLE, "");
                    } else {
                        sendResourceReward(req.getUid(), detail.getSourceId(), RewardTypeEnum.getEnumByName(detail.getRewardType()), detail.getRewardTime(), detail.getRewardNum(), DESC);
                    }
                    RocketGainRewardRecordData recordData = saveGainRewardRecord(req.getUid(), req.getRoomId(), count, detail);
                    if (count <= 5) {
                        // 获取前5奖励会有房间广播
                        sendGetRoomRocketRewardMsg(req.getRoomId(), actorData, recordData);
                    }
                    vo.setHasReward(1);
                    vo.setName(req.getSlang() == SLangType.ARABIC ? detail.getRewardNameAr() : detail.getRewardNameEn());
                    vo.setIcon(detail.getRewardIcon());
                    vo.setNums(detail.getRewardNum());
                    vo.setDays(detail.getRewardTime());
                    vo.setShowDayOrNum(getShowDayOrNum(detail.getRewardType()));
                    return vo;
                }
            }
        }
        vo.setHasReward(0);
        return vo;
    }

    /**
     * 获取房间火箭和宝箱信息
     */
    public RocketInfoVO getRocketAndBoxInfo(String roomId, String uid) {
        int rocketSwitch = sysConfigDao.getIntValue(SysConfigDao.ROCKET_CONFIG, SysConfigDao.ROOM_ROCKET_SWITCH_KEY);
        if (rocketSwitch == 0) {
            return null;
        }
        int nowTime = DateHelper.getNowSeconds();
//        if (nowTime > MatchConstant.ROOM_ALLOWANCE_START) {
//            return null;
//        }
        int roomRocketLevel = roomRocketRedis.getRoomRocketLevel(roomId);
        int showRocketLevel = roomRocketRedis.getShowRocketLevel(roomRocketLevel);
        RocketInfoVO vo = new RocketInfoVO();
        vo.setRocketLevel(showRocketLevel);
        BigDecimal rate = new BigDecimal("0.00");
        int hasRewardBox = 0;
        int rocketEnergy = roomRocketRedis.getRocketEnergy(roomId, roomRocketLevel);
        RocketRewardConfigData configData = rocketRewardConfigDao.findDataFromCache(getRocketConfigLevel(roomRocketLevel));
        List<RocketInfoVO.RewardBox> boxList = new ArrayList<>();
        if (configData != null) {
            int energyLimit = configData.getRocketLaunchLimit();
            if (rocketEnergy >= energyLimit) {
                rate = new BigDecimal("1.00");
            } else {
                rate = new BigDecimal(rocketEnergy).divide(new BigDecimal(energyLimit), 2, BigDecimal.ROUND_DOWN);
            }
            Map<Integer, Integer> allRewardEndTime = roomRocketRedis.getAllRewardEndTime(roomId);
            if (!CollectionUtils.isEmpty(allRewardEndTime)) {
                for (Map.Entry<Integer, Integer> entry : allRewardEndTime.entrySet()) {
                    if (entry.getValue() > nowTime) {
                        if (roomRocketRedis.hasGainedReward(roomId, entry.getKey(), uid)) {
                            continue;
                        }
                        hasRewardBox = 1;
                        RocketInfoVO.RewardBox rewardBox = new RocketInfoVO.RewardBox();
                        rewardBox.setBoxId(entry.getKey());
                        rewardBox.setBoxLevel(roomRocketRedis.getShowRocketLevel(entry.getKey()));
                        rewardBox.setLeftTime(Math.min(60, entry.getValue() - nowTime));
                        boxList.add(rewardBox);
                    }
                }
            }
        }
        vo.setEnergyRate(rate);
        vo.setHasRewardBox(hasRewardBox);
        vo.setBoxList(boxList);
        return vo;
    }

    @Cacheable(value = "getGainRewardRecordList", key = "#p0", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public List<RoomRocketVO.GainRewardRecord> getGainRewardRecordList(int slang) {
        List<RoomRocketVO.GainRewardRecord> recordList = new ArrayList<>();
        List<RocketGainRewardRecordData> gainRewardRecordList = rocketGainRewardRecordDao.selectList();
        if (!CollectionUtils.isEmpty(gainRewardRecordList)) {
            for (RocketGainRewardRecordData recordData : gainRewardRecordList) {
                ActorData actorData = actorDao.getActorDataFromCache(recordData.getUid());
                if (actorData == null) {
                    logger.error("can not find actor. uid={}", recordData.getUid());
                    continue;
                }
                RoomRocketVO.GainRewardRecord rewardRecord = new RoomRocketVO.GainRewardRecord();
                rewardRecord.setUid(actorData.getUid());
                rewardRecord.setUserName(actorData.getName());
                rewardRecord.setUserHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
                rewardRecord.setDesc(getDesc(recordData, actorData.getName(), slang));
                recordList.add(rewardRecord);
            }
        }
        return recordList;
    }

    private int getRocketConfigLevel(int rocketLevel) {
        int level = rocketLevel % 6;
        return level == 0 ? 6 : level;
    }

    private void sendGetRoomRocketRewardMsg(String roomId, ActorData actorData, RocketGainRewardRecordData recordData) {
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                GetRoomRocketRewardMsg msg = new GetRoomRocketRewardMsg();
                msg.setUid(actorData.getUid());
                msg.setUserName(actorData.getName());
                msg.setUserHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead(), vipInfoDao.getIntVipLevelFromCache(actorData.getUid())));
                msg.setText(getDesc(recordData, actorData.getName(), SLangType.ENGLISH));
                msg.setTextAr(getDesc(recordData, actorData.getName(), SLangType.ARABIC));
                logger.info("msg={}", JSONObject.toJSONString(msg));
                roomWebSender.sendRoomWebMsg(roomId, "", msg, false);
            }
        });
    }

    private RocketGainRewardRecordData saveGainRewardRecord(String uid, String roomId, int rank, RocketRewardConfigData.RewardConfigDetail detail) {
        RocketGainRewardRecordData data = new RocketGainRewardRecordData();
        data.setUid(uid);
        data.setRoomId(roomId);
        data.setName(detail.getRewardNameEn());
        data.setNameAr(detail.getRewardNameAr());
        data.setIcon(detail.getRewardIcon());
        data.setDay(detail.getRewardTime());
        data.setNum(detail.getRewardNum());
        data.setType(getRewardType(detail.getRewardType()));
        data.setRank(rank);
        data.setCtime(DateHelper.getNowSeconds());
        rocketGainRewardRecordDao.insert(data);
        return data;
    }

    private int getRewardType(String rewardType) {
        // 0钻石 1金币 2道具 3礼物
        if (DIAMOND.equals(rewardType)) {
            return 0;
        } else if (COIN.equals(rewardType)) {
            return 1;
        } else if (GIFT.equals(rewardType)) {
            return 3;
        } else {
            return 2;
        }
    }

    /**
     * 是否满足获奖条件
     */
    private boolean meetTheAwardConditions(int costBeans, RocketRewardConfigData.RewardConfigDetail detail) {
        if (detail.getLowerLimit().equals(detail.getUpperLimit())) {
            return costBeans == detail.getLowerLimit();
        } else {
            if (costBeans >= detail.getLowerLimit()) {
                if (detail.getUpperLimit() == null) {
                    return true;
                }
                return costBeans < detail.getUpperLimit();
            }
        }
        return false;
    }

    private String getDesc(RocketGainRewardRecordData recordData, String userName, Integer slang) {
        // 0钻石 1金币 2道具 3礼物
        if (recordData.getType() == 0) {
            return slang == SLangType.ARABIC ? String.format(GET_DIAMONDS_REWARD_DESC_AR, userName, recordData.getNum()) : String.format(GET_DIAMONDS_REWARD_DESC, userName, recordData.getNum());
        } else if (recordData.getType() == 1) {
            return slang == SLangType.ARABIC ? String.format(GET_COINS_REWARD_DESC_AR, userName, recordData.getNum()) : String.format(GET_COINS_REWARD_DESC, userName, recordData.getNum());
        } else if (recordData.getType() == 2) {
            return slang == SLangType.ARABIC ? String.format(GET_PROPS_REWARD_DESC_AR, userName, recordData.getNameAr(), recordData.getDay()) : String.format(GET_PROPS_REWARD_DESC, userName, recordData.getName(), recordData.getDay());
        } else {
            return slang == SLangType.ARABIC ? String.format(GET_GIFTS_REWARD_DESC_AR, userName, recordData.getNum()) : String.format(GET_GIFTS_REWARD_DESC, userName, recordData.getNum());
        }
    }

    private RocketVO getRocketLvInfo(String roomId, int level, int currentLevel, int showRocketLevel, RocketRewardConfigData configData) {
        RocketVO vo = new RocketVO();
        List<RocketVO.RankUser> rankList = new ArrayList<>();
        List<RocketVO.Reward> rewards = new ArrayList<>();
        if (configData == null) {
            vo.setRank(rankList);
            vo.setRewards(rewards);
            vo.setEnergyRate(new BigDecimal("0"));
            logger.error("RocketRewardConfigData is null. currentLevel={}", currentLevel);
            return vo;
        }
        if (level < showRocketLevel) {
            List<String> rocketRankingList = roomRocketRedis.getRocketRankingList(roomId, (currentLevel - (showRocketLevel - level)));
            if (!CollectionUtils.isEmpty(rocketRankingList)) {
                int rank = 1;
                for (String rocketRankUid : rocketRankingList) {
                    ActorData actorData = actorDao.getActorDataFromCache(rocketRankUid);
                    if (actorData == null) {
                        logger.error("can not find actor. uid={}", rocketRankUid);
                        continue;
                    }
                    RocketVO.RankUser rankUser = new RocketVO.RankUser();
                    rankUser.setRank(rank);
                    rankUser.setUid(actorData.getUid());
                    rankUser.setRid(actorData.getRid());
                    rankUser.setName(actorData.getName());
                    rankUser.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead(), vipInfoDao.getIntVipLevelFromCache(actorData.getUid())));
                    rankList.add(rankUser);
                }
            }
            vo.setEnergyRate(new BigDecimal("1.00"));
        } else if (level == showRocketLevel) {
            BigDecimal rate;
            int rocketEnergy = roomRocketRedis.getRocketEnergy(roomId, currentLevel);
            int energyLimit = configData.getRocketLaunchLimit();
            if (rocketEnergy >= energyLimit) {
                rate = new BigDecimal("1.00");
            } else {
                rate = new BigDecimal(rocketEnergy).divide(new BigDecimal(energyLimit), 2, BigDecimal.ROUND_DOWN);
            }
            vo.setEnergyRate(rate);
        } else {
            vo.setEnergyRate(new BigDecimal("0"));
        }
        if (!CollectionUtils.isEmpty(configData.getDetailList())) {
            List<RocketRewardConfigData.RewardConfigDetail> detailList = new ArrayList<>(configData.getDetailList());
            detailList.sort(Comparator.comparing(RocketRewardConfigData.RewardConfigDetail::getLowerLimit).reversed());
            int count = 1;
            for (RocketRewardConfigData.RewardConfigDetail detail : detailList) {
                if (count > 5) {
                    continue;
                }
                RocketVO.Reward reward = new RocketVO.Reward();
                reward.setIcon(detail.getRewardIcon());
                reward.setDay(detail.getRewardTime());
                reward.setNum(detail.getRewardNum());
                reward.setShowDayOrNum(getShowDayOrNum(detail.getRewardType()));
                rewards.add(reward);
                count++;
            }
        }
        vo.setRank(rankList);
        vo.setRewards(rewards);
        return vo;
    }

    private int getShowDayOrNum(String rewardType) {
        if (DIAMOND.equals(rewardType)) {
            return 1;
        } else if (COIN.equals(rewardType)) {
            return 1;
        } else if (GIFT.equals(rewardType)) {
            return 1;
        }
        return 0;
    }

    /**
     * 下发资源奖励
     *
     * @param uid      获取礼物的用户uid
     * @param sourceId 资源id
     * @param typeEnum 资源类型
     * @param days     礼物有效时间 /天
     * @param num      礼物数量
     * @param desc     描述
     */
    public void sendResourceReward(String uid, Integer sourceId, RewardTypeEnum typeEnum, Integer days, Integer num, String desc) {
        if (typeEnum == null) {
            logger.error("reward type error. sourceId={}", sourceId);
            return;
        }
        logger.info("send gift to mq. toUid={}, sourceId={}, rewardType={} time={}", uid, sourceId, typeEnum.getName(), days);
        ResourcesDTO resourcesDTO = new ResourcesDTO();
        resourcesDTO.setUid(uid);
        if (typeEnum == RewardTypeEnum.BACKGROUND) {
            resourcesDTO.setRoomId(RoomUtils.formatRoomId(uid));
        }
        resourcesDTO.setResId(String.valueOf(sourceId));
        resourcesDTO.setResType(typeEnum.getCode());
        resourcesDTO.setDesc(desc);
        resourcesDTO.setItemsSourceDetail(desc);
        resourcesDTO.setActionType(BaseDataResourcesConstant.ACTION_GET);
        resourcesDTO.setmTime(DateHelper.getNowSeconds());
        resourcesDTO.setNum(num != 0 ? num : 1);
        resourcesDTO.setDays(days != 0 ? days : -1);
        mqSenderService.asyncHandleResources(resourcesDTO);
    }

    /**
     * 下发钻石奖励
     *
     * @param uid     获取礼物的用户uid
     * @param aType   aType
     * @param changed 钻石数量
     * @param title   标题
     * @param desc    描述
     */
    public void sendDiamondsReward(String uid, Integer aType, Integer changed, String title, String desc) {
        logger.info("send diamonds reward. uid={}, changed={}, title={}", uid, changed, title);
        MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
        moneyDetailReq.setRandomId();
        moneyDetailReq.setUid(uid);
        moneyDetailReq.setAtype(aType);
        moneyDetailReq.setChanged(changed);
        moneyDetailReq.setTitle(title);
        moneyDetailReq.setDesc(desc);
        moneyDetailReq.setMtime(DateHelper.getNowSeconds());
        mqSenderService.asyncChargeDiamonds(moneyDetailReq);
    }

    /**
     * 下发金币奖励
     *
     * @param uid     获取礼物的用户uid
     * @param changed 金币数量
     * @param title   标题
     * @param desc    描述
     */
    public void sendCoinReward(String uid, Integer changed, String title, String desc) {
        logger.info("send coin reward. uid={}, changed={}, title={}", uid, changed, title);
        heartRecordDao.changeHeart(uid, changed, title, desc);
    }
}

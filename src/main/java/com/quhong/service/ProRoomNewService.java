package com.quhong.service;

import com.alibaba.fastjson.JSONObject;
import com.quhong.config.CountryConfig;
import com.quhong.data.TurntableGameInfo;
import com.quhong.data.dto.ProNewRoomListDTO;
import com.quhong.data.vo.RoomNewVO;
import com.quhong.enums.RoomSwitchType;
import com.quhong.mongo.dao.MongoRoomDao;
import com.quhong.mongo.data.MongoRoomData;
import com.quhong.redis.DataRedisBean;
import com.quhong.redis.TurntableGameRedis;
import com.quhong.room.redis.RoomPlayerRedis;
import com.quhong.utils.PageUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;

@Component
public class ProRoomNewService {

    private static final Logger logger = LoggerFactory.getLogger(ProRoomNewService.class);

    private static final int NEW_ROOM_DAY = -7;
    private static final int ROOM_LIST_PAGE_SIZE = 20;

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate redisTemplate;
    @Autowired
    private RoomPlayerRedis roomPlayerRedis;

    @Autowired
    private MongoRoomDao roomDao;

    @Autowired
    private CountryConfig config;

    @Autowired
    private TurntableGameRedis turntableGameRedis;

    public JSONObject listNewRoom(ProNewRoomListDTO listDTO) {
        List<MongoRoomData> newRoomList = roomDao.listNewRoom(NEW_ROOM_DAY);
        if (CollectionUtils.isEmpty(newRoomList)) {
            logger.info("new_create room is empty. uid={}", listDTO.getUid());
            return null;
        }
        List<RoomNewVO> roomNewVOList = new ArrayList<>();
        Map<String, MongoRoomData> roomMap = new HashMap<>();
        for (MongoRoomData roomData : newRoomList) {
            int personCount = roomPlayerRedis.getRoomActorsCount(roomData.getRid());
            if (personCount > 0) {
                RoomNewVO roomNewVO = new RoomNewVO();
                roomNewVO.setRoomId(roomData.getRid());
                roomNewVO.setRoomName(roomData.getName());
                roomNewVO.setOnlinePeople(personCount);
                roomNewVO.setIcon(roomData.getHead());
                roomNewVO.setCountryName(roomData.getCountry());
                try {
                    roomNewVO.setCountryIcon(config.getCountryFlagMap()
                            .get(roomData.getCountry().split("_")[0].toLowerCase()));
                } catch (Exception e) {
                    logger.info("cannot parse country orig country={}", roomData.getCountry());
                    // 默认阿拉伯联合酋长国
                    roomNewVO.setCountryIcon(config.getCountryFlagMap().get("ae"));
                }
                roomNewVO.setOffice(roomData.getAnnounce());
                roomNewVOList.add(roomNewVO);
                roomMap.put(roomData.getRid(), roomData);
            }
        }
        Comparator<RoomNewVO> personCountDesc = Comparator.comparing(RoomNewVO::getOnlinePeople).reversed();
        roomNewVOList.sort(personCountDesc);
        PageUtils.PageData<RoomNewVO> pageData = PageUtils.getPageData(roomNewVOList, listDTO.getPage(), ROOM_LIST_PAGE_SIZE);
        for (RoomNewVO roomNewVO : pageData.list) {
            MongoRoomData roomData = roomMap.get(roomNewVO.getRoomId());
            roomNewVO.setType(getRoomType(roomData));
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("list", pageData.list);
        jsonObject.put("nextUrl", pageData.nextPage == 0 ? "" : pageData.nextPage + "");
        return jsonObject;
    }

    private String getRoomType(MongoRoomData roomData) {
        int video_switch = roomData.getVideo_switch();
        int ludo_switch = getRoomLudoSwitch(roomData.getRid());
        int turntable_switch = getRoomTurntableSwitch(roomData.getRid());
        if (video_switch == RoomSwitchType.open) {
            return "Watch video";
        }
        if (getGameSwitch(ludo_switch, turntable_switch)) {
            return "Game";
        }
        return null;
    }

    private boolean getGameSwitch(int ludo_switch, int turntable_switch) {
        return ludo_switch == RoomSwitchType.open || turntable_switch == RoomSwitchType.open;
    }

    private int getRoomLudoSwitch(String roomId) {
        String ludoGameId = redisTemplate.opsForValue().get(getRoomLudoKey(roomId));
        if (StringUtils.isEmpty(ludoGameId)) {
            return RoomSwitchType.close;
        }
        return RoomSwitchType.open;
    }

    private int getRoomTurntableSwitch(String roomId) {
        TurntableGameInfo gameData = turntableGameRedis.getGameInfoByRoomId(roomId);
        if (gameData == null) {
            return RoomSwitchType.close;
        }
        return RoomSwitchType.open;
    }

    private String getRoomLudoKey(String roomId) {
        return "room_ludo_" + roomId;
    }

}

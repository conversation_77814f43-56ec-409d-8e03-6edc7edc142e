package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.analysis.ActivitySpecialItemsChangeEvent;
import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.CommonMqTopicData;
import com.quhong.data.SendGiftData;
import com.quhong.data.vo.CrashExchangeShopVO;
import com.quhong.data.vo.HappySaudiVO;
import com.quhong.data.vo.OtherRankingListVO;
import com.quhong.enums.BaseDataResourcesConstant;
import com.quhong.exception.CommonH5Exception;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.mongo.data.ResourceKeyConfigData;
import com.quhong.mysql.dao.GiftDao;
import com.quhong.mysql.dao.RechargeDailyInfoDao;
import com.quhong.mysql.dao.WhiteTestDao;
import com.quhong.utils.ActorUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;

/**
 * 沙特国庆活动
 */
@Service
public class HappySaudiNationalService extends OtherActivityService {


    private static final Logger logger = LoggerFactory.getLogger(HappySaudiNationalService.class);
    private static final String ACTIVITY_TITLE_EN = "Happy Saudi National Day";
    public static String ACTIVITY_ID = "kkk";
    private static String ACTIVITY_URL = String.format("https://static.youstar.live/happy_saudi2025/?activityId=%s", ACTIVITY_ID);

    // 机会兑换比例：每xxx钻石=1机会
    private static final int DIAMONDS_PER_POINT = 190000;

    //    private static final String CRASH_SHOP_EXCHANGE_KEY = "CrashShopExchangeKey";
    private static final String SA_NATIONAL_DRAW_KEY = "2025SANationalDayDraw";

    // 榜单前10名奖励
    private static final List<String> CRASH_SHOP_TOP_KEY_LIST = Arrays.asList("CrashExchangeShopTop1", "CrashExchangeShopTop2", "CrashExchangeShopTop3", "CrashExchangeShopTop4", "CrashExchangeShopTop5"
            , "CrashExchangeShopTop6-10", "CrashExchangeShopTop6-10", "CrashExchangeShopTop6-10", "CrashExchangeShopTop6-10", "CrashExchangeShopTop6-10");

    private static final Interner<String> stringPool = Interners.newWeakInterner();

    private static final String eventDrawTitle = "2025SANationalDay-draw";
    private static final String eventRankingTitle = "2025SANationalDay-ranking";

    private static final int HISTORY_USER_MAX_SIZE = 1000;
    private static final int PAGE_SIZE = 20;

    private static final String SA_COUNTRY_CODE = "sa";
    // 用户配置字段
    private static final String SHARE_FIELD = "share_field"; // 分享次数字段
    private static final String TOTAL_DIAMOND_FIELD = "total_d_field"; // 总钻石字段
    private static final String LEFT_CHANCE_FIELD = "left_chance_field"; // 剩余抽奖机会字段

    @Resource
    private WhiteTestDao whiteTestDao;
    @Resource
    protected ResourceKeyHandlerService resourceKeyHandlerService;
    @Autowired(required = false)
    private EventReport eventReport;
    @Resource
    private GiftDao giftDao;
    @Resource
    private RechargeDailyInfoDao rechargeDailyInfoDao;

    @PostConstruct
    public void init() {
        if (ServerConfig.isNotProduct()) {
            ACTIVITY_ID = "bbbb";
            ACTIVITY_URL = String.format("https://test2.qmovies.tv/happy_saudi2025/?activityId=%s", ACTIVITY_ID);
        }
    }

    // Redis key 方法
    private String getLocalLockKey(String activityId, String uid) {
        return String.format("getLocalLockKey:%s:%s", activityId, uid);
    }


    // 积分key - zset存储用户每日积分 flied 为uid
    private String getDayPointsKey(String activityId, String dateStr) {
        return String.format("saNational:points:%s:%s", activityId, dateStr);
    }

    //  hash存储用户相关配置
    private String getUserConfigKey(String activityId, String uid) {
        return String.format("saNational:user:config:%s:%s", activityId, uid);
    }

    // 抽奖用户记录key - list存储最近抽奖用户id
    private String getRollRecordKey(String activityId) {
        return String.format("saNational:draw:record:%s", activityId);
    }

    // 中奖幸运儿历史记录key - list存储最近中奖幸运用户id,
    private String getLuckyHistoryKey(String activityId) {
        return activityId + ":saNational:lucky:history:%s" + activityId;
    }

    // 每日参与沙特用户key，大R抽中大奖的群发对象 - set存储每日打开活动的用户id
    private String getDailyJoinKey(String activityId, String dateStr) {
        return String.format("saNational:all:join:%s", activityId, dateStr);
    }

    public HappySaudiVO crashExchangeShopConfig(String activityId, String uid) {
        OtherRankingActivityData activity = otherActivityService.getOtherRankingActivity(activityId);

        HappySaudiVO vo = new HappySaudiVO();
        vo.setStartTime(activity.getStartTime());
        vo.setEndTime(activity.getEndTime());


        return vo;
    }

    public HappySaudiVO.LuckyOneVO draw(String activityId, String uid) {
        if (!inActivityTime(activityId)) {
            throw new CommonH5Exception(ActivityHttpCode.NOT_ACTIVE_TIME);
        }

        HappySaudiVO.LuckyOneVO vo = new HappySaudiVO.LuckyOneVO();
        int drawNum = 1;
        String dateStr = getDayByBase(activityId, uid);
        synchronized (stringPool.intern(getLocalLockKey(activityId, uid))) {
            String userConfigKey = getUserConfigKey(activityId, uid);
            int nowChance = activityCommonRedis.getCommonHashValue(userConfigKey, LEFT_CHANCE_FIELD);

            if (nowChance < drawNum) {
                logger.info("insufficient points. uid={} currentPoints={} drawNum={}", uid, nowChance, drawNum);
                throw new CommonH5Exception(ActivityHttpCode.INSUFFICIENT_NUMBER_POINT);
            }

            // 扣除积分
            nowChance = activityCommonRedis.incCommonHashNum(userConfigKey, LEFT_CHANCE_FIELD, -drawNum);

            // 执行抽奖逻辑
            // List<CrashExchangeShopVO.ResourceMetaTmp> resultList = new ArrayList<>();

            ResourceKeyConfigData.ResourceMeta meta = drawOne(uid, SA_NATIONAL_DRAW_KEY, eventDrawTitle);
            if (meta != null) {
                BeanUtils.copyProperties(meta, vo);
            }

            doReportSpecialItemsEvent(ACTIVITY_ID, uid, 2, 3, drawNum);
            vo.setChanceNum(nowChance);

            String rollRecordKey = getRollRecordKey(activityId);
            activityCommonRedis.addCommonListData(rollRecordKey, uid);
        }

        vo.setDrawUserList(getDrawUserList(activityId));

        return vo;
    }

    private List<HappySaudiVO.DrawUserVO> getDrawUserList(String activityId) {
        String rollRecordKey = getRollRecordKey(activityId);
        List<String> drawUserList = activityCommonRedis.getCommonListPageRecord(rollRecordKey, 0,0);
    }

    public void sendGiftHandle(SendGiftData data, String activityId) {
        int sendD = data.getNumber() * data.getPrice() * data.getAid_list().size();
        String uid = data.getFrom_uid();
        String dateStr = getDayByBase(activityId, uid);
        String userConfigKey = getUserConfigKey(activityId, uid);
        String dayPointsKey = getDayPointsKey(activityId, dateStr);
        String dailyJoinKey = getDailyJoinKey(activityId, dateStr);

        activityCommonRedis.incrCommonZSetRankingScoreSimple(dayPointsKey, uid, sendD);

        int beforeDiamond = activityCommonRedis.getCommonHashValue(userConfigKey, TOTAL_DIAMOND_FIELD);
        int afterDiamond = activityCommonRedis.incCommonHashNum(userConfigKey, TOTAL_DIAMOND_FIELD, sendD);

        int chanceNum = afterDiamond / DIAMONDS_PER_POINT - beforeDiamond / DIAMONDS_PER_POINT;

        if (chanceNum > 0) {
            activityCommonRedis.incCommonHashNum(userConfigKey, LEFT_CHANCE_FIELD, chanceNum);
            doReportSpecialItemsEvent(ACTIVITY_ID, uid, 1, 1, chanceNum);
        }
        addDailyJoinUser(dailyJoinKey, uid);
        logger.info("sendGiftHandle uid:{} sendD:{} beforeDiamond:{} afterDiamond:{} chanceNum:{}"
                , uid, sendD, beforeDiamond, afterDiamond, chanceNum);
    }

    private void addDailyJoinUser(String dailyJoinKey, String uid) {
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        String countryCode = ActorUtils.getCountryCode(actorData.getCountry());
        if (SA_COUNTRY_CODE.equals(countryCode)) {
            activityCommonRedis.addCommonSetData(dailyJoinKey, uid);
        }
    }

    private boolean checkAc(String uid) {
        OtherRankingActivityData activityData = getOtherRankingActivityNull(ACTIVITY_ID);
        if (ServerConfig.isProduct() && activityData.getAcNameEn().startsWith("test")) {
            boolean isWhiteTest = whiteTestDao.isMemberByType(uid, WhiteTestDao.WHITE_TYPE_RID);
            if (!isWhiteTest) {
                // 灰度测试,只统计测试用户的
                return false;
            }
        }
        return true;
    }

    // 保存历史记录
    private void leftPushAllHistoryList(String uid, List<CrashExchangeShopVO.ResourceMetaTmp> srcList) {
        String historyKey = "ss";
        List<String> strList = new ArrayList<>();
        for (CrashExchangeShopVO.ResourceMetaTmp meta : srcList) {
            String json = JSONObject.toJSONString(meta);
            strList.add(json);
        }
        if (CollectionUtils.isEmpty(strList)) {
            return;
        }
        activityCommonRedis.leftPushAllCommonList(historyKey, strList, HISTORY_USER_MAX_SIZE);
    }

    // 添加抽奖流水记录
    private void addRollRecord(String activityId, String uid, List<CrashExchangeShopVO.ResourceMetaTmp> resultList) {
        if (CollectionUtils.isEmpty(resultList)) {
            return;
        }

        String rollRecordKey = getRollRecordKey(activityId);
        List<String> strList = new ArrayList<>();
        // 只记录有价值的奖励到流水
        for (CrashExchangeShopVO.ResourceMetaTmp meta : resultList) {
            if (meta.getResourceType() != BaseDataResourcesConstant.TYPE_OTHER) {
                // 只记录有价值的奖励到流水
                CrashExchangeShopVO.RollRecordVO rollRecord = new CrashExchangeShopVO.RollRecordVO();
                rollRecord.setUid(uid);
                rollRecord.setResourceIcon(meta.getResourceIcon());
                rollRecord.setCtime(DateHelper.getNowSeconds());
                rollRecord.setCostValue(meta.getResourcePrice());

                String json = JSONObject.toJSONString(rollRecord);
                strList.add(json);
                break; // 每次抽奖只记录一条流水
            }
        }
        if (CollectionUtils.isEmpty(strList)) {
            return;
        }
        activityCommonRedis.leftPushAllCommonList(rollRecordKey, strList, HISTORY_USER_MAX_SIZE);
    }

    // 下发榜单奖励
    public void distributionRanking(String activityId) {
        try {
            int length = CRASH_SHOP_TOP_KEY_LIST.size();
            String betKey = "ccc";
            Map<String, Integer> totalRankingMap = activityCommonRedis.getCommonRankingMap(betKey, length);
            int rank = 1;
            for (Map.Entry<String, Integer> entry : totalRankingMap.entrySet()) {
                if (rank > length) {
                    continue;
                }
                String aid = entry.getKey();
                String resKey = CRASH_SHOP_TOP_KEY_LIST.get(rank - 1);
                handleRes(aid, resKey, eventRankingTitle);
                rank += 1;
            }
        } catch (Exception e) {
            logger.error("distributionRanking error: {}", e.getMessage(), e);
        }
    }

    private void handleRes(String aid, String resKey, String eventTitle) {
        resourceKeyHandlerService.sendResourceData(aid, resKey,
                eventTitle, eventTitle, eventTitle, ACTIVITY_URL, "");
    }

    /**
     * 上报机会获取消耗事件
     */
    private void doReportSpecialItemsEvent(String activityId, String uid, int action, int source, int num) {
        ActivitySpecialItemsChangeEvent event = new ActivitySpecialItemsChangeEvent();
        event.setUid(uid);
        event.setCtime(DateHelper.getNowSeconds());
        event.setChange_action(action);
        event.setActivity_special_items_id("0");
        event.setActivity_name(ACTIVITY_TITLE_EN);
        event.setActive_id(activityId);
        event.setActivity_special_items_resource(source);
        event.setResource_desc("");
        event.setChange_nums(num);
        eventReport.track(new EventDTO(event));
    }


}

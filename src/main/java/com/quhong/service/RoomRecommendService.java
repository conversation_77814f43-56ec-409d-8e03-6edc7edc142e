package com.quhong.service;

import com.quhong.api.UserInfoApiService;
import com.quhong.constant.RoomListConstant;
import com.quhong.constant.RoomListHttpCode;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.*;
import com.quhong.data.dto.PlayGameDTO;
import com.quhong.data.dto.RoomRecommendDTO;
import com.quhong.data.vo.ForYouListVO;
import com.quhong.data.vo.GameListVO;
import com.quhong.data.vo.NewUserRecommendVO;
import com.quhong.data.vo.RoomGuideRecommendVO;
import com.quhong.enums.RoomConstant;
import com.quhong.enums.SLangType;
import com.quhong.enums.SudGameConstant;
import com.quhong.exception.CommonException;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.MongoRoomDao;
import com.quhong.mongo.dao.RoomGuideConfigDao;
import com.quhong.mongo.dao.SudGameDao;
import com.quhong.mongo.data.MongoRoomData;
import com.quhong.mongo.data.RoomGuideConfigData;
import com.quhong.mongo.data.SudGameData;
import com.quhong.mongo.data.SudGamePlayerData;
import com.quhong.mysql.dao.RoomBlacklistDao;
import com.quhong.redis.*;
import com.quhong.room.redis.RoomKickRedis;
import com.quhong.room.redis.RoomPlayerRedis;
import com.quhong.utils.ActorUtils;
import com.quhong.utils.CollectionUtil;
import com.quhong.utils.PageUtils;
import com.quhong.utils.RoomUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 房间推荐
 */
@Service
public class RoomRecommendService {

    private static final Logger logger = LoggerFactory.getLogger(RoomRecommendService.class);
    private static final int RECOMMEND_FIRST_REGISTER = 24 * 3600;
    private static final int RECOMMEND_CHAT = 0;
    private static final int RECOMMEND_LUDO = 1;
    private static final int RECOMMEND_UMO = 2;
    private static final int RECOMMEND_LUCKY_WHEEL = 3;
    private static final int RECOMMEND_MONSTER_CRUSH = 4;
    private static final List<Integer> RECOMMEND_PLAY_LIST = Arrays.asList(RoomListConstant.GAME_LUDO, RoomListConstant.GAME_MONSTER_CRUSH,
            RoomListConstant.GAME_LUCKY_WHEEL, RoomListConstant.GAME_UMO);
    private static final String ROOM_WELCOME_GAME_EN = "Hi, %s！Do you like %s game,Let's play!";
    private static final String ROOM_WELCOME_GAME_AR = "مرحبًا، %s! هل تحب لعبة %s، دعونا نلعب!";
    private static final String ROOM_WELCOME_CHAT_EN = "Hi, %s! Recommend you to join the room to chat together!";
    private static final String ROOM_WELCOME_CHAT_AR = "مرحبًا، %s أنصحك بالانضمام إلى الغرفة للدردشة معًا!";
    private static final String LUDO_EN = "Ludo";
    private static final String LUDO_AR = "لودو";
    private static final String UMO_EN = "UMO";
    private static final String UMO_AR = "أومو";
    private static final String LUCKY_WHEEL_EN = "Lucky Wheel";
    private static final String LUCKY_WHEEL_AR = "عجلة الحظ";
    private static final String MONSTER_CRUSH_EN = "Monster Crush";
    private static final String MONSTER_CRUSH_AR = "مونستر كراش";
    private String newRookieRoomId = "";

    public static final Map<Integer, Integer> GAME_DEFAULT_WEIGHT_MAP = new HashMap<Integer, Integer>() {
        {
            put(SudGameConstant.LUDO_GAME, 100);
            put(SudGameConstant.CARROM_POOL_GAME, 90);
            put(SudGameConstant.MONSTER_CRUSH_GAME, 80);
            put(SudGameConstant.UMO_GAME, 70);
            put(SudGameConstant.DOMINO_GAME, 70);
        }
    };
    private static final int HISTORY_PLAY_GAME_WEIGHT = 10000;
    private static final int SYSTEM_RECOMMEND_GAME_WEIGHT = 1000;


    public static final Map<Integer, Integer> TYPE_RECOMMEND_TYPE_MAP = new HashMap<Integer, Integer>() {
        {
            put(4, RoomListConstant.TYPE_BIG_R_ROOM);
            put(3, RoomListConstant.TYPE_HIGH_QUALITY_ROOM);
            put(1, RoomListConstant.TYPE_ROOKIE_ROOM);
        }
    };

    @Resource
    private RoomListRedis roomListRedis;
    @Resource
    private RoomBlacklistDao roomBlacklistDao;
    @Resource
    private ActorDao actorDao;
    @Resource
    private RoomKickRedis roomKickRedis;
    @Resource
    private RecreationTagService recreationTagService;
    @Resource
    private MongoRoomDao mongoRoomDao;
    @Resource
    private TurntableGameRedis turntableGameRedis;
    @Resource
    private NewUserRoomRecommendRedis newUserRoomRecommendRedis;
    @Resource
    private RoomMicRedis roomMicRedis;
    @Resource
    private RoomPwdRedis roomPwdRedis;
    @Resource
    private UserInfoApiService userInfoApiService;
    @Resource
    private RecentlyRoomRedis recentlyRoomRedis;
    @Resource
    private RoomGuideConfigDao roomGuideConfigDao;
    @Resource
    private BackUserStateRedis backUserStateRedis;
    @Resource
    private SocialListService socialListService;
    @Resource
    private SudGameDao sudGameDao;
    @Resource
    private GameRoomRedis gameRoomRedis;
    @Resource
    private RoomPlayerRedis roomPlayerRedis;

    // 推荐在房间里发送过礼物或者上麦过的麦位大于等于4的房间
    private void hotChatRoomToVO(NewUserRecommendVO vo, ActorData actorData, int slang) {

        Set<String> roomList = newUserRoomRecommendRedis.getUserOnRoomGiftMicRooms(actorData.getUid(), 0, 30);
        for (String roomId : roomList) {

            if (newUserRoomRecommendRedis.isAlreadyRecommendSet(actorData.getUid(), roomId) || roomBlacklistDao.isBlock(roomId, actorData.getUid()) || roomKickRedis.isKick(roomId, actorData.getUid())) {
                continue;
            }

            Set<String> micList = roomMicRedis.getRoomMicSetRedis(roomId);
            if (micList.isEmpty() || micList.size() < 4) {
                continue;
            }

            MongoRoomData mongoRoomData = mongoRoomDao.getDataFromCache(roomId);
            if (mongoRoomData == null || !StringUtils.isEmpty(mongoRoomData.getPwd())) {
                continue;
            }

            vo.setName(mongoRoomData.getName());
            vo.setHead(ImageUrlGenerator.generateRoomUserUrl(mongoRoomData.getHead()));
            vo.setRoomId(roomId);
            vo.setRoomType(RECOMMEND_CHAT);
            String roomWelcome = slang == SLangType.ENGLISH ? ROOM_WELCOME_CHAT_EN : ROOM_WELCOME_CHAT_AR;
            vo.setDescription(String.format(roomWelcome, actorData.getName()));
            break;
        }
    }

    private void historyChatRoomToVO(NewUserRecommendVO vo, ActorData actorData, int slang) {

        try {
            Set<String> roomSet = recentlyRoomRedis.getRecentlyNumRooms(actorData.getUid(), 0, 30);
            List<String> roomList = new ArrayList<>();
            for (String roomId : roomSet) {

                if (newUserRoomRecommendRedis.isAlreadyRecommendSet(actorData.getUid(), roomId) || roomBlacklistDao.isBlock(roomId, actorData.getUid()) || roomKickRedis.isKick(roomId, actorData.getUid())) {
                    continue;
                }

                Set<String> micList = roomMicRedis.getRoomMicSetRedis(roomId);
                if (micList.isEmpty() || micList.size() < 4) {
                    continue;
                }

                MongoRoomData mongoRoomData = mongoRoomDao.getDataFromCache(roomId);
                if (mongoRoomData == null || !StringUtils.isEmpty(mongoRoomData.getPwd())) {
                    continue;
                }

                roomList.add(roomId);
            }


            if (roomList.isEmpty()) {
                return;
            }
            Random random = new Random();
            int randomIndex = random.nextInt(roomList.size());
            String roomId = roomList.get(randomIndex);
            MongoRoomData mongoRoomData = mongoRoomDao.getDataFromCache(roomId);
            if (mongoRoomData == null) {
                return;
            }

            vo.setName(mongoRoomData.getName());
            vo.setHead(ImageUrlGenerator.generateRoomUserUrl(mongoRoomData.getHead()));
            vo.setRoomId(roomId);
            vo.setRoomType(RECOMMEND_CHAT);
            String roomWelcome = slang == SLangType.ENGLISH ? ROOM_WELCOME_CHAT_EN : ROOM_WELCOME_CHAT_AR;
            vo.setDescription(String.format(roomWelcome, actorData.getName()));

        } catch (Exception e) {
            throw new CommonException(RoomListHttpCode.CANNOT_FIND_ROOM);
        }

    }

    //  调用新人礼包房间推荐
    private void rookieChatRoomToVO(NewUserRecommendVO vo, ActorData actorData, int slang) {
        String roomId = userInfoApiService.getNewUserChatRoomId(actorData.getUid());
        if (StringUtils.isEmpty(roomId)) {
            throw new CommonException(RoomListHttpCode.CANNOT_FIND_ROOM);
        }
        MongoRoomData mongoRoomData = mongoRoomDao.getDataFromCache(roomId);
        vo.setName(mongoRoomData.getName());
        vo.setHead(ImageUrlGenerator.generateRoomUserUrl(mongoRoomData.getHead()));
        vo.setRoomId(roomId);
        vo.setRoomType(RECOMMEND_CHAT);
        String roomWelcome = slang == SLangType.ENGLISH ? ROOM_WELCOME_CHAT_EN : ROOM_WELCOME_CHAT_AR;
        vo.setDescription(String.format(roomWelcome, actorData.getName()));
    }


    // 推荐转盘房
    private void luckyWheelGameRoomToVO(NewUserRecommendVO vo, ActorData actorData, int slang) {
        String uid = actorData.getUid();
        int reqGender = actorData.getFb_gender();
        String reqUserName = actorData.getName();

        List<GameListVO> turntableGameList = roomListRedis.getGameList(RoomListConstant.GAME_LUCKY_WHEEL);
        Map<String, TurntableGameInfo> turntableRoomMap = CollectionUtil.listToKeyMap(turntableGameRedis.getAllTurntableGame(), TurntableGameInfo::getRoomId);
        for (GameListVO gameVO : turntableGameList) {
            String roomId = gameVO.getRoomId();

            if (gameVO.getStatus() != 1 || newUserRoomRecommendRedis.isAlreadyRecommendSet(uid, roomId) || roomBlacklistDao.isBlock(roomId, uid) || roomKickRedis.isKick(roomId, uid)) {
                continue;
            }

            TurntableGameInfo turntableGameInfo = turntableRoomMap.get(roomId);
            if (turntableGameInfo == null) {
                continue;
            }

            List<TurntableGameInfo.Actor> playerList = turntableGameInfo.getPlayers();
            if (playerList == null || playerList.isEmpty()) {
                continue;
            }

            TurntableGameInfo.Actor turntablePlayer = playerList.stream().filter((e) -> e.getFb_gender() != reqGender).findAny().orElse(playerList.get(0));
            ActorData playerActor = actorDao.getActorDataFromCache(turntablePlayer.get_id());
            vo.setName(playerActor.getName());
            vo.setHead(ImageUrlGenerator.generateRoomUserUrl(playerActor.getHead()));
            vo.setGender(turntablePlayer.getGender());
            vo.setRoomId(roomId);
            vo.setRoomType(RECOMMEND_LUCKY_WHEEL);
            String roomWelcome = slang == SLangType.ENGLISH ? ROOM_WELCOME_GAME_EN : ROOM_WELCOME_GAME_AR;
            String gameName = slang == SLangType.ENGLISH ? LUCKY_WHEEL_EN : LUCKY_WHEEL_AR;
            vo.setDescription(String.format(roomWelcome, reqUserName, gameName));
            break;
        }
    }


    private void rookieGameRoomToVO(NewUserRecommendVO vo, ActorData actorData, int slang, List<SudGameInfo> gameList, int gameType) {
        String uid = actorData.getUid();
        int reqGender = actorData.getFb_gender();
        String reqUserName = actorData.getName();
        for (SudGameInfo gameInfo : gameList) {
            String roomId = gameInfo.getRoomId();
            if (RoomUtils.isGameRoom(roomId)) {
                continue;
            }
            if (gameInfo.getStatus() != 1 || newUserRoomRecommendRedis.isAlreadyRecommendSet(uid, roomId) || roomBlacklistDao.isBlock(roomId, uid) || roomKickRedis.isKick(roomId, uid)) {
                continue;
            }

            if (roomPwdRedis.hasPwdFromCache(roomId)) {
                continue;
            }

            List<SudGamePlayerData> playerList = gameInfo.getPlayerList();
            if (playerList == null || playerList.isEmpty()) {
                continue;
            }

            SudGamePlayerData sudGamePlayer = playerList.stream().filter((e) -> e.getGender() != reqGender).findAny().orElse(playerList.get(0));
            ActorData playerActor = actorDao.getActorDataFromCache(sudGamePlayer.getUid());
            vo.setName(playerActor.getName());
            vo.setHead(ImageUrlGenerator.generateRoomUserUrl(playerActor.getHead()));
            vo.setGender(sudGamePlayer.getGender());
            vo.setRoomId(roomId);
            break;
        }

        if (!StringUtils.isEmpty(vo.getRoomId())) {
            String roomWelcome = slang == SLangType.ENGLISH ? ROOM_WELCOME_GAME_EN : ROOM_WELCOME_GAME_AR;
            String gameName = "";
            switch (gameType) {
                case RoomListConstant.GAME_LUDO:
                    gameName = slang == SLangType.ENGLISH ? LUDO_EN : LUDO_AR;
                    vo.setRoomType(RECOMMEND_LUDO);
                    break;
                case RoomListConstant.GAME_MONSTER_CRUSH:
                    gameName = slang == SLangType.ENGLISH ? MONSTER_CRUSH_EN : MONSTER_CRUSH_AR;
                    vo.setRoomType(RECOMMEND_MONSTER_CRUSH);
                    break;
                case RoomListConstant.GAME_UMO:
                    gameName = slang == SLangType.ENGLISH ? UMO_EN : UMO_AR;
                    vo.setRoomType(RECOMMEND_UMO);
                    break;
            }
            vo.setDescription(String.format(roomWelcome, reqUserName, gameName));
        }
    }

    private void newUserGameRoomToVO(NewUserRecommendVO vo, ActorData actorData, int slang, List<Integer> gameSortList) {
        Map<Integer, List<SudGameInfo>> groupGameTypeRoomMap = recreationTagService.getAllSudGameInfo().values().stream().collect(Collectors.groupingBy(SudGameInfo::getGameType));
        for (Integer gameType : gameSortList) {

            if (RoomListConstant.GAME_LUCKY_WHEEL == gameType) {
                luckyWheelGameRoomToVO(vo, actorData, slang);
            } else {
                List<SudGameInfo> gameInfoList = groupGameTypeRoomMap.get(gameType);
                if (gameInfoList == null || gameInfoList.isEmpty()) {
                    continue;
                }
                rookieGameRoomToVO(vo, actorData, slang, gameInfoList, gameType);
            }

            if (!StringUtils.isEmpty(vo.getRoomId())) {
                break;
            }
        }
    }

    // 推荐在房间里玩某游戏最多的房间
    private void sortPlayGameRoomToVO(NewUserRecommendVO vo, ActorData actorData, int slang) {

        List<PlayGameDTO> playGameDTOList = new ArrayList<>();
        for (Integer gameType : RECOMMEND_PLAY_LIST) {
            PlayGameDTO dto = new PlayGameDTO();
            dto.setGameType(gameType);
            dto.setPlayNum(newUserRoomRecommendRedis.getRecommendGameNum(actorData.getUid(), gameType));
            playGameDTOList.add(dto);
        }
        List<PlayGameDTO> playGameDTOSortList = playGameDTOList.stream().sorted(Comparator.comparing(PlayGameDTO::getPlayNum)).collect(Collectors.toList());
        List<Integer> gameSortList = CollectionUtil.getPropertyList(playGameDTOSortList, PlayGameDTO::getGameType, 0);
        logger.info("sortPlayGameRoomToVO uid:{}, gameSortList: {}", actorData.getUid(), gameSortList);
        newUserGameRoomToVO(vo, actorData, slang, gameSortList);

    }


    public NewUserRecommendVO getNewUserRecommendInfo(RoomRecommendDTO dto) {
        String uid = dto.getUid();
        int slang = dto.getSlang();
        Integer num = dto.getNum();
        if (num == null) {
            throw new CommonException(RoomListHttpCode.PARAM_ERROR);
        }

        ActorData actorData = actorDao.getActorDataFromCache(uid);
        NewUserRecommendVO vo = new NewUserRecommendVO();
        int registerTime = new ObjectId(uid).getTimestamp();
        int currentTime = DateHelper.getNowSeconds();
        boolean isNewAccount = ActorUtils.isNewDeviceAccount(uid, 1, actorData.getFirstTnId());
        try {
            // 注册第一天的用户推荐规则
//            currentTime - registerTime <= RECOMMEND_FIRST_REGISTER
            if (isNewAccount) {
                logger.info("firstDay register getting");
                // 1、推荐游戏房
                newUserGameRoomToVO(vo, actorData, slang, RECOMMEND_PLAY_LIST);

                // 2、按新人礼包规则推荐
                if (StringUtils.isEmpty(vo.getRoomId())) {
                    rookieChatRoomToVO(vo, actorData, slang);
                }

            } else {
                // 非第一天注册的用户推荐规则
                logger.info("other register getting");

                // 1、每日首次推荐玩游戏最多的游戏房
                if (num == 1) {
                    sortPlayGameRoomToVO(vo, actorData, slang);
                }

                // 2、按上过麦或者发过礼物的房间进行推荐
                if (StringUtils.isEmpty(vo.getRoomId())) {
                    hotChatRoomToVO(vo, actorData, slang);
                }

                // 3、按进过的历史房间进行推荐
                if (StringUtils.isEmpty(vo.getRoomId())) {
                    historyChatRoomToVO(vo, actorData, slang);
                }
            }

        } catch (Exception e) {
            throw new CommonException(RoomListHttpCode.CANNOT_FIND_ROOM);
        }

        if (StringUtils.isEmpty(vo.getRoomId())) {
            throw new CommonException(RoomListHttpCode.CANNOT_FIND_ROOM);
        } else {
            newUserRoomRecommendRedis.addAlreadyRecommendSet(uid, vo.getRoomId());
        }

        return vo;
    }


    public RoomGuideRecommendVO roomGuideRecommend(RoomRecommendDTO dto) {
        String uid = dto.getUid();
        int slang = dto.getSlang();
        List<String> guideConfigIdList = dto.getGuideConfigIdList();

        ActorData actorData = actorDao.getActorDataFromCache(uid);
        RoomGuideRecommendVO vo = new RoomGuideRecommendVO();
        int userType = getUserType(actorData);
        int gender = actorData.getFb_gender() == 2 ? 2 : 1;
        String countryCode = ActorUtils.getCountryCode(actorData.getCountry());
        int area = RoomListConstant.MAJOR_COUNTRY_SET.contains(countryCode) ?
                RoomListConstant.MAJOR_COUNTRY : RoomListConstant.OTHER_COUNTRY;
        logger.info("uid:{} userType:{} countryCode:{} area:{} gender:{} guideConfigIdList:{}"
                , uid, userType, countryCode, area, gender, guideConfigIdList);
        if (userType == 0) {
            vo.setRoomGuideConfigVOList(Collections.emptyList());
            vo.setRoomGuideInfoList(Collections.emptyList());
            return vo;
        }

        List<RoomGuideRecommendVO.RoomGuideConfigVO> roomGuideConfigVOList = new ArrayList<>();
        List<RoomGuideConfigData> configDataList = roomGuideConfigDao.findListFromCache(userType, area, gender);
        configDataList.forEach(item -> {
            RoomGuideRecommendVO.RoomGuideConfigVO configVO = new RoomGuideRecommendVO.RoomGuideConfigVO();
            BeanUtils.copyProperties(item, configVO);
            configVO.setGuideConfigId(item.get_id().toString());
            roomGuideConfigVOList.add(configVO);
        });


        List<RoomGuideRecommendVO.RoomGuideInfo> roomGuideInfoList = new ArrayList<>();
        HashSet<String> allIdSet = new HashSet<>();
        if (!CollectionUtils.isEmpty(guideConfigIdList)) {
            guideConfigIdList.removeIf(StringUtils::isEmpty);
            allIdSet.addAll(guideConfigIdList);
        }
        if (!CollectionUtils.isEmpty(allIdSet)) {
            List<RoomGuideConfigData> allConfigList = roomGuideConfigDao.findAllListFromCache();
            Map<String, RoomGuideConfigData> allConfigMap = allConfigList.stream()
                    .collect(Collectors.toMap(k -> k.get_id().toString(), Function.identity()));
            allIdSet.forEach(configId -> {
                RoomGuideConfigData configData = allConfigMap.get(configId);
                if (configData != null) {
                    RoomGuideRecommendVO.RoomGuideInfo roomGuideInfo = getRoomGuideInfo(actorData, configData);
                    if (roomGuideInfo != null) {
                        roomGuideInfoList.add(roomGuideInfo);
                    }
                } else {
                    logger.info("not find config configId:{}", configId);
                }
            });
        }
        vo.setRoomGuideConfigVOList(roomGuideConfigVOList);
        vo.setRoomGuideInfoList(roomGuideInfoList);
        return vo;
    }

    private RoomGuideRecommendVO.RoomGuideInfo getRoomGuideInfo(ActorData actorData, RoomGuideConfigData configData) {
        int roomType = configData.getRoomType();
        String configId = configData.get_id().toString();
        int configTotalCountDay = configData.getTotalCountDay();
        Set<String> daySet = null;
        if (configTotalCountDay != 0) {
            String today = DateHelper.ARABIAN.formatDateInDay();
            daySet = roomListRedis.getRoomGuideTotalCountSet(actorData.getUid(), configId);
            daySet.add(today);
            // 累积弹出天数 后台判断，其余配置条件客户端判断
            if (configTotalCountDay < daySet.size()) {
                return null;
            }
        }
        RoomGuideRecommendVO.RoomGuideInfo roomGuideInfo = null;
        if (roomType == 1) {
            // 推荐语音房
            int recommendRoomType = configData.getRecommendRoomType();
            ForYouListVO toForYouRoom = null;
            String countryCode = ActorUtils.getCountryCode(actorData.getCountry());
//            boolean isNew = ActorUtils.isNewDeviceAccount(actorData.getUid(), actorData.getFirstTnId());
            List<ForYouListVO> allNewPageData = socialListService.getAllNewList(5, countryCode);
            List<ForYouListVO> allList = new ArrayList<>(allNewPageData);
            PageUtils.PageData<ForYouListVO> pageData = PageUtils.getPageData(allList, 1, 100);
            List<ForYouListVO> allNewList = new ArrayList<>(pageData.list);
            // 被列入房间黑名单的房间和被房间踢出的房间不推荐
            allNewList.removeIf(item -> roomKickRedis.isKick(item.getRoomId(), actorData.getUid())
                    || roomBlacklistDao.isBlock(item.getRoomId(), actorData.getUid())
                    || item.getRoomId().equals(RoomUtils.formatRoomId(actorData.getUid())));
            if (TYPE_RECOMMEND_TYPE_MAP.containsKey(recommendRoomType)) {
                int filterType = TYPE_RECOMMEND_TYPE_MAP.getOrDefault(recommendRoomType, 0);
                List<ForYouListVO> newRookieRoomList = allNewList.stream().filter(k ->
                        (k.getSocialWeight() == RoomListConstant.SOCIAL_NEW_ROOM_WEIGHT
                                && k.getRoomRecommendType() == filterType)).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(newRookieRoomList)) {
                    if (newRookieRoomList.size() == 1) {
                        toForYouRoom = newRookieRoomList.get(0);
                    } else {
                        Collections.shuffle(newRookieRoomList);
                        for (ForYouListVO one : newRookieRoomList) {
                            if (!one.getRoomId().equals(newRookieRoomId)) {
                                toForYouRoom = one;
                                newRookieRoomId = one.getRoomId();
                                break;
                            }
                        }
                    }
                }
            }


            if (toForYouRoom == null) {
                // 推荐非迎新房类型的普通房
                List<ForYouListVO> normalRoomList = allNewList.stream().filter(k ->
                        (k.getSocialWeight() < RoomListConstant.SOCIAL_NEW_ROOM_WEIGHT)).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(normalRoomList)) {
                    toForYouRoom = normalRoomList.get(0);
                }
                if (toForYouRoom == null) {
//                Collections.shuffle(allNewList);
                    toForYouRoom = allNewList.get(0);
                }
            }
            if (toForYouRoom != null) {
                String aid = toForYouRoom.getMicUserAid();
                if (StringUtils.isEmpty(aid)) {
                    Set<String> allInRoomUser = roomPlayerRedis.getRoomActors(toForYouRoom.getRoomId());
                    if (CollectionUtils.isEmpty(allInRoomUser)) {
                        return null;
                    }
                    List<String> ulist = new ArrayList<>(allInRoomUser);
                    aid = ulist.get(0);
                }
                ActorData aidActorData = actorDao.getActorDataFromCache(aid);
                roomGuideInfo = new RoomGuideRecommendVO.RoomGuideInfo();
                roomGuideInfo.setRoomId(toForYouRoom.getRoomId());
                roomGuideInfo.setGuideConfigId(configId);
                roomGuideInfo.setScene(configData.getScene());
                roomGuideInfo.setName(aidActorData.getName());
                roomGuideInfo.setHead(ImageUrlGenerator.generateRoomUserUrl(aidActorData.getHead()));
                roomGuideInfo.setGender(aidActorData.getFb_gender());
                roomGuideInfo.setRoomHead(StringUtils.isEmpty(toForYouRoom.getRoom_head())?"":toForYouRoom.getRoom_head());
                roomGuideInfo.setRoomName(StringUtils.isEmpty(toForYouRoom.getRoom_name())?"":toForYouRoom.getRoom_name());
                roomGuideInfo.setRoomNotice(StringUtils.isEmpty(toForYouRoom.getAnnounce())?"":toForYouRoom.getAnnounce());//
                String country = ActorUtils.getCountryCode(toForYouRoom.getCountry());
                roomGuideInfo.setRoomCountry(StringUtils.isEmpty(country)?"":country);
                roomGuideInfo.setRoomType(roomType);
            } else {
                logger.info("not find recommend voice room configId:{}", configId);
            }

        } else if (roomType == 2) {
            List<Integer> recommendGameTypeList = configData.getRecommendGameTypeList() == null ? Collections.emptyList() : configData.getRecommendGameTypeList();
            //  Map<Integer, List<SudGameData>> map = sudGameDataList.stream().collect(Collectors.groupingBy(SudGameData::getGameType));
            // 推荐游戏房
            List<SudGameData> sudGameDataList = sudGameDao.findGameRoomByStatus(SudGameConstant.GAME_MATCHING);
            SortSudGameData toSudGameRoom = getSortSudGame(actorData, sudGameDataList, recommendGameTypeList);
            if (toSudGameRoom == null) {
                sudGameDataList = sudGameDao.findGameRoomByStatus(SudGameConstant.GAME_PROCESSING);
                toSudGameRoom = getSortSudGame(actorData, sudGameDataList, recommendGameTypeList);
            }
            if (toSudGameRoom != null) {
                Map<Integer, SociaEntryData> gameMap =
                        RoomConstant.SOCIAL_ITEM_LIST.stream().filter(item -> item.getGameType() > 0).collect(Collectors.toMap(SociaEntryData::getGameType, Function.identity()));
                SociaEntryData sociaEntryData = gameMap.get(toSudGameRoom.getGameType());
                if (sociaEntryData == null) {
                    logger.info("not find sociaEntryData gameType:{} configId:{}", toSudGameRoom.getGameType(), configId);
                    return null;
                }
                String aid = StringUtils.hasLength(toSudGameRoom.getLeaderUid()) ? toSudGameRoom.getLeaderUid() : toSudGameRoom.getSelfUid();
                ActorData aidActorData = actorDao.getActorDataFromCache(aid);
                roomGuideInfo = new RoomGuideRecommendVO.RoomGuideInfo();
                roomGuideInfo.setRoomId(toSudGameRoom.getRoomId());
                roomGuideInfo.setGuideConfigId(configId);
                roomGuideInfo.setScene(configData.getScene());
                roomGuideInfo.setName(aidActorData.getName());
                roomGuideInfo.setHead(ImageUrlGenerator.generateRoomUserUrl(aidActorData.getHead()));
                roomGuideInfo.setGender(aidActorData.getFb_gender());
                roomGuideInfo.setGameName(actorData.getSlang() == SLangType.ARABIC ? sociaEntryData.getItemNameAr() : sociaEntryData.getItemNameEn());
                roomGuideInfo.setGameIcon(sociaEntryData.getSmallIcon());
                roomGuideInfo.setRoomType(roomType);

                roomGuideInfo.setRoomCountry("");
                roomGuideInfo.setRoomNotice("");
            } else {
                logger.info("not find recommend game room configId:{}", configId);
            }
        }
        if (roomGuideInfo != null && !CollectionUtils.isEmpty(daySet)) {
            roomListRedis.saveRoomGuideTotalCountSet(actorData.getUid(), configId, daySet);
        }
        return roomGuideInfo;
    }

    private SortSudGameData getSortSudGame(ActorData actorData, List<SudGameData> sudGameDataList, List<Integer> recommendGameTypeList) {
        if (!CollectionUtils.isEmpty(sudGameDataList)) {
            List<Integer> myLastList = getLastGameType(actorData);
            List<SortSudGameData> sortSudGameDataList = new ArrayList<>();
            for (SudGameData item : sudGameDataList) {
                SortSudGameData sortSudGameData = new SortSudGameData();
                BeanUtils.copyProperties(item, sortSudGameData);
                int gameType = sortSudGameData.getGameType();
                if (myLastList.contains(gameType)) {
                    sortSudGameData.setWeight(HISTORY_PLAY_GAME_WEIGHT);
                } else if (recommendGameTypeList.contains(gameType)) {
                    sortSudGameData.setWeight(SYSTEM_RECOMMEND_GAME_WEIGHT);
                } else if (item.getRoomId().equals(RoomUtils.formatRoomId(actorData.getUid()))) {
                    // 自己房间不推荐
                    continue;
                } else {
                    sortSudGameData.setWeight(GAME_DEFAULT_WEIGHT_MAP.getOrDefault(gameType, 70));
                }
                sortSudGameDataList.add(sortSudGameData);
            }
            if (!CollectionUtils.isEmpty(sortSudGameDataList)) {
                List<SortSudGameData> oneRoomList = sortSudGameDataList.stream().filter(k -> k.getWeight() == HISTORY_PLAY_GAME_WEIGHT).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(oneRoomList)) {
                    Collections.shuffle(oneRoomList);
                    return oneRoomList.get(0);
                }
                List<SortSudGameData> twoRoomList = sortSudGameDataList.stream().filter(k -> k.getWeight() == SYSTEM_RECOMMEND_GAME_WEIGHT).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(twoRoomList)) {
                    Collections.shuffle(twoRoomList);
                    return twoRoomList.get(0);
                }
                sortSudGameDataList.sort(Comparator.comparing(SortSudGameData::getWeight).reversed());
                return sortSudGameDataList.get(0);
            }
        }
        return null;
    }


    private int getUserType(ActorData actorData) {
        int regDays = ActorUtils.getRegDays(actorData.getUid());
        int userType = 0;
        if (regDays <= 7) {
            userType = 1;
        } else if (regDays <= 30) {
            userType = 2;
        } else if (backUserStateRedis.isBackUser(actorData, true, false) > 0) {
            // 15天内回归用户
            userType = 3;
        }
        return userType;
    }

    private List<Integer> getLastGameType(ActorData actorData) {
        Map<String, Integer> mapGame = gameRoomRedis.getAllLastPlayGameTime(actorData.getUid());
        List<Integer> lastList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(mapGame)) {
            mapGame.forEach((k, v) -> {
                try {
                    int gameType = Integer.parseInt(k);
                    if (gameType < 10) {
                        lastList.add(gameType);
                    }
                } catch (NumberFormatException e) {

                }

            });
        }
        return lastList;
    }
}

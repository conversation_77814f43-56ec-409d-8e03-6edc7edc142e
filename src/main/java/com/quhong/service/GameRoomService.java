package com.quhong.service;

import com.quhong.analysis.EventReport;
import com.quhong.config.RoomConfig;
import com.quhong.constant.DetectOriginConstant;
import com.quhong.constant.RoomConfigConstant;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.dailyTask.DailyTaskService;
import com.quhong.data.*;
import com.quhong.data.dto.GameRoomDTO;
import com.quhong.data.dto.RoomDTO;
import com.quhong.data.vo.GameItemInfoVO;
import com.quhong.data.vo.RoomGameConfigVO;
import com.quhong.data.vo.RoomVO;
import com.quhong.dto.ImageDTO;
import com.quhong.dto.InnerSudGameDTO;
import com.quhong.dto.TextDTO;
import com.quhong.enums.*;
import com.quhong.exception.CommonException;
import com.quhong.feign.IDetectService;
import com.quhong.feign.IGameService;
import com.quhong.handler.HttpEnvData;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.*;
import com.quhong.mongo.data.*;
import com.quhong.msg.obj.RoomMicInfoObject;
import com.quhong.msg.obj.RoomMicUserObject;
import com.quhong.msg.obj.UserInfoObject;
import com.quhong.msg.room.RoomInfoChangeMsg;
import com.quhong.msg.room.RoomInviteUserMsg;
import com.quhong.mysql.dao.RoomBlacklistDao;
import com.quhong.mysql.dao.VipInfoDao;
import com.quhong.mysql.dao.WhiteTestDao;
import com.quhong.mysql.data.RoomMicData;
import com.quhong.mysql.data.RoomMicListData;
import com.quhong.redis.*;
import com.quhong.room.*;
import com.quhong.room.api.zego.ZegoApi;
import com.quhong.room.cache.RoomActorCache;
import com.quhong.room.data.EnterCartonData;
import com.quhong.room.data.RoomActorDetailData;
import com.quhong.room.mic.RoomMicService;
import com.quhong.room.redis.MicFrameRedis;
import com.quhong.room.redis.RoomKickRedis;
import com.quhong.room.redis.RoomPlayerRedis;
import com.quhong.room.redis.RoomRedis;
import com.quhong.sdk.zego.ZegoService;
import com.quhong.task.TaskFactory;
import com.quhong.utils.*;
import com.quhong.vo.RoomMicListVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/5/27
 */
@Service
public class GameRoomService {
    public static final int ACTOR_PAGE_SIZE = 20;
    public static final int FACE_DETECTION = 10;
    public static final ZoneId zoneId = ZoneId.of("+3");
    public static final Set<String> GCC_COUNTRY_SET = new HashSet<>(Arrays.asList("SA", "KW", "AE", "QA", "OM", "BH"));
    private static final Logger logger = LoggerFactory.getLogger(GameRoomService.class);
    private final List<Integer> SUD_GAME_ROOM_TYPE = Arrays.asList(SudGameConstant.LUDO_GAME, SudGameConstant.JACKAROO_GAME,
            SudGameConstant.CARROM_POOL_GAME, SudGameConstant.BILLIARD_GAME, SudGameConstant.BALOOT_GAME,
            SudGameConstant.MONSTER_CRUSH_GAME, SudGameConstant.UMO_GAME, SudGameConstant.DOMINO_GAME);

    @Resource
    private MongoRoomDao roomDao;
    @Resource
    private ActorDao actorDao;
    @Resource
    private RoomTags roomTags;
    @Resource
    private IDetectService detectService;
    @Resource
    private RoomActorCache roomActorCache;
    @Resource
    private BlockRedis blockRedis;
    @Resource
    private RoomKickRedis roomKickRedis;
    @Resource
    private RoomBlacklistDao roomBlacklistDao;
    @Resource
    private ZegoApi zegoApi;
    @Resource
    private CommonDao commonDao;
    @Resource
    private RoomMemberDao roomMemberDao;
    @Resource
    private FollowDao followDao;
    @Resource
    private DailyTaskService dailyTaskService;
    @Resource
    private ZegoService zegoService;
    @Resource
    private RoomRedis roomRedis;
    @Resource
    private RoomWebSender roomWebSender;
    @Resource
    private RoomActorListService roomActorListService;
    @Resource
    private RoomMicService roomMicService;
    @Resource
    private RoomMicRedis roomMicRedis;
    @Resource
    private EnterRoomContent enterRoomContent;
    @Autowired
    private RoomPlayerRedis roomPlayerRedis;
    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterRedis;
    @Resource
    private VipInfoDao vipInfoDao;
    @Resource
    private UserMonitorDao userMonitorDao;
    @Resource
    private PackService packService;
    @Autowired
    private ActorConfigDao actorConfigDao;
    @Resource
    private RoomBannerDao roomBannerDao;
    @Resource
    private RoomConfigDao roomConfigDao;
    @Resource
    private RoomAdminRedis roomAdminRedis;
    @Resource
    private WhiteTestDao whiteTestDao;
    @Resource
    private EventReport eventReport;
    @Resource
    private RoomStreamService roomStreamService;
    @Resource
    private RoomConfig roomConfig;
    @Resource
    private RecentlyRoomRedis recentlyRoomRedis;
    @Resource
    private RoomNewService roomNewService;
    @Resource
    private MicFrameRedis micFrameRedis;
    @Resource
    private GameRoomRedis gameRoomRedis;
    @Resource
    private SudGameRedis sudGameRedis;
    @Resource
    private SudGameDao sudGameDao;
    @Resource
    private IGameService gameService;
    @Resource
    private RoomVisitorsRedis roomVisitorsRedis;

    /**
     * 创建游戏房间之前调用
     */
    public RoomGameConfigVO preCreateGameRoom(HttpEnvData dto) {
        RoomGameConfigVO vo = new RoomGameConfigVO();
        boolean showNewGame = whiteTestDao.isMemberByType(dto.getUid(), WhiteTestDao.WHITE_TYPE_RID);
        List<RoomGameConfigVO> gameInfoList = new ArrayList<>();
        for (Integer gameType : SUD_GAME_ROOM_TYPE) {
            // 新增游戏白名单可见
            // if ((SudGameConstant.JACKAROO_GAME == gameType || SudGameConstant.BALOOT_GAME == gameType) && !showNewGame) {
            //     continue;
            // }
            RoomSudGameConfigInfo gameConfigInfo = roomConfig.getSudGameInfoMap().get(gameType);
            RoomGameConfigVO gameInfo = new RoomGameConfigVO();
            BeanUtils.copyProperties(gameConfigInfo, gameInfo);
            gameInfo.setGameId("");//这里从config里面获取的是sud的gameId这里不返回
            gameInfo.setGameType(gameType);
            gameInfoList.add(gameInfo);
        }
        vo.setGameInfoList(gameInfoList);

        // 所有互动游戏
        Map<Integer, Integer> allOnlineMap = gameRoomRedis.getAllOnlinePlayCount();
        List<GameItemInfoVO> itemInfoList = new ArrayList<>();
        List<SociaEntryData> sociaEntryDataList = RoomConstant.SOCIAL_ITEM_LIST.stream().filter(item -> item.getGameType() > 0).collect(Collectors.toList());
        for (SociaEntryData entryData : sociaEntryDataList) {
            // 新增游戏白名单可见
            if (entryData.getShowType() > 0 && !showNewGame) {
                continue;
            }
            GameItemInfoVO itemInfoVO = new GameItemInfoVO();
            BeanUtils.copyProperties(entryData, itemInfoVO);
            itemInfoVO.setItemName(dto.getSlang() == SLangType.ENGLISH ? entryData.getItemNameEn() : entryData.getItemNameAr());
            itemInfoVO.setItemIcon(dto.getSlang() == SLangType.ENGLISH ? entryData.getIconEn() : entryData.getIconAr());
            itemInfoVO.setItemOnlineCount(allOnlineMap.getOrDefault(entryData.getGameCountType(), 0));
            itemInfoList.add(itemInfoVO);
        }
        // quite-more game(与社交主页一样的banner)
        vo.setMoreGameList(itemInfoList);
        return vo;
    }

    /**
     * 房主创建房间
     */
    public RoomVO create(GameRoomDTO.CreateDTO req) {
        ActorData actorData = actorDao.getActorDataFromCache(req.getUid());
        if (null == actorData) {
            logger.error("cannot find actor, uid={}", req.getUid());
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        if (null == req.getGameType() || !SUD_GAME_ROOM_TYPE.contains(req.getGameType())) {
            logger.error("cannot find gameType, uid={} gameType={}", req.getUid(), req.getGameType());
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        ActorData myActorData = actorDao.getActorData(req.getUid());
        int leftCoins = myActorData.getHeartGot();
        int currency = req.getCurrencyValue() == null ? 0 : req.getCurrencyValue();
        // 匹配推荐创建房间的
        if (currency == 0) {
            RoomSudGameConfigInfo gameConfigInfo = roomConfig.getSudGameInfoMap().get(req.getGameType());
            currency = gameConfigInfo.getCoinFeeList().get(0);
            req.setCurrencyType(1);
            req.setCurrencyValue(currency);
        }
        if (leftCoins < currency) {
            throw new CommonException(RoomHttpCode.NOT_ENOUGH_COIN);
        }
        String roomId = RoomUtils.formatGameRoomId(req.getUid());
        // 封禁检测
        // 脏词检测
        if (!ObjectUtils.isEmpty(req.getRoomName())) {
            checkDirtyWord(req.getUid(), req.getRoomName().toLowerCase());
        }
        if (!ObjectUtils.isEmpty(req.getRoomHead())) {
            String roomHeadUrl = req.getRoomHead().startsWith("http") ? CDNUtils.getHttpCdnUrl(req.getRoomHead()) :
                    ImageUrlGenerator.createCdnUrl(req.getRoomHead());
            req.setRoomHead(roomHeadUrl);
            checkImage(req.getUid(), roomHeadUrl);
        }
        req.setRoomId(roomId);
        req.setEnterRoomType(RoomConfigConstant.ENTER_ROOM_CREAT_TYPE);
        MongoRoomData mongoRoomData = roomDao.findData(roomId);
        if (null == mongoRoomData) {
            createRoom(actorData, roomId, req);
        } else {
            updateRoom(mongoRoomData, req, actorData);
        }
//        String voiceRoomId = RoomUtils.formatRoomId(req.getUid());
//        if (roomPlayerRedis.getRoomActorCount(voiceRoomId) > 0) {
//            roomWebSender.sendRoomWebMsg(voiceRoomId, req.getUid(), new RoomChangeMsg(roomId, RoomConstant.LIVE_ROOM_MODE, 1), false);
//        }
        // 调用加入房间接口
        return enterRoom(req);
    }


//    private void doReportCloseLiveEvent(String roomId, String uid, long liveOpenTime, Map<String, Long> liveDataMap) {
//        eventReport.track(new EventDTO(event));
//    }

    /**
     * 更新房间
     */
    public void updateRoom(MongoRoomData mongoRoomData, GameRoomDTO.CreateDTO req, ActorData actorData) {
        String roomId = mongoRoomData.getRid();
        // 更新房间信息
        mongoRoomData.setOwnerRid(actorData.getRid());
        mongoRoomData.setRoomMode(RoomConstant.GAME_ROOM_MODE);
        mongoRoomData.setStream_id(roomNewService.genStreamId(roomId, null));
        if (null != req.getTagId()) {
            mongoRoomData.setTag(roomTags.hasTag(req.getTagId()) ? req.getTagId() : 2);
        }
        if (!ObjectUtils.isEmpty(req.getRoomHead())) {
            mongoRoomData.setHead(req.getRoomHead());
        }
        if (!ObjectUtils.isEmpty(req.getRoomName())) {
            mongoRoomData.setName(req.getRoomName());
        }
        roomDao.save(mongoRoomData);
    }

    private void checkDirtyWord(String uid, String roomName) {
        if (detectService.detectText(new TextDTO(roomName, DetectOriginConstant.ROOM_NAME_RELATED, uid)).getData().getIsSafe() == 0) {
            throw new CommonException(RoomHttpCode.DIRTY_WORD_TITLE);
        }
    }

    public void checkImage(String uid, String roomHead) {
        String roomHeadUrl = roomHead.startsWith("http") ? CDNUtils.getHttpCdnUrl(roomHead) :
                ImageUrlGenerator.createCdnUrl(roomHead);
        if (detectService.detectImage(new ImageDTO(roomHeadUrl, DetectOriginConstant.GAME_ROOM_HEAD, uid)).getData().getIsSafe() == 0) {
            throw new CommonException(RoomHttpCode.DIRTY_WORD_TITLE);
        }
    }

    /**
     * 房主第一次创建房间
     */
    public void createRoom(ActorData actorData, String roomId, GameRoomDTO.CreateDTO req) {
        logger.info("first time create room uid={}", req.getUid());
        int nowSeconds = DateHelper.getNowSeconds();
        MongoRoomData mongoRoomData = new MongoRoomData();
        mongoRoomData.setName(ObjectUtils.isEmpty(req.getRoomName()) ? actorData.getName() : req.getRoomName());
        mongoRoomData.setRid(roomId);
        mongoRoomData.setOwnerRid(actorData.getRid());
        mongoRoomData.setPrivi(1);
        mongoRoomData.setFee(0);
        mongoRoomData.setTextLimit(-1);
        mongoRoomData.setHead(ObjectUtils.isEmpty(req.getRoomHead()) ? actorData.getHead() : req.getRoomHead());
        mongoRoomData.setStream_id(roomNewService.genStreamId(roomId, null));
        mongoRoomData.setCountry(ObjectUtils.isEmpty(actorData.getCountry()) ? "AE_United Arab Emirates" : actorData.getCountry());
        mongoRoomData.setVideo_switch(2);
        mongoRoomData.setMtime(nowSeconds);
        mongoRoomData.setCtime(nowSeconds);
        mongoRoomData.setTag(null == req.getTagId() || !roomTags.hasTag(req.getTagId()) ? 1 : req.getTagId());
        mongoRoomData.setRoomMode(RoomConstant.GAME_ROOM_MODE);
        roomDao.save(mongoRoomData);
    }

    /**
     * 加入房间，返回房间信息、麦位信息
     */
    public RoomVO enterRoom(GameRoomDTO.CreateDTO req) {
        if (!RoomUtils.isGameRoom(req.getRoomId())) {
            logger.error("roomId invalid, roomId={} uid={}", req.getRoomId(), req.getUid());
            throw new CommonException(HttpCode.PARAM_ERROR);
        }

        String myGameRoomId = RoomUtils.formatGameRoomId(req.getUid());
        if (req.getRoomId().equals(myGameRoomId) && req.getGameType() == null) {
            Integer gameType = gameRoomRedis.getGameRoomAdjustCampaign(req.getUid());
            req.setGameType(gameType != null ? gameType : req.getGameType());
            return create(req);
        }else {
            String ownerUid;
            try {
                ownerUid = RoomUtils.getRoomHostId(req.getRoomId());
            } catch (Exception e) {
                logger.error("roomId invalid, roomId={} uid={}", req.getRoomId(), req.getUid());
                throw new CommonException(HttpCode.PARAM_ERROR);
            }
            RoomActorDetailData myData = roomActorCache.getData(req.getRoomId(), req.getUid(), false);
            RoomActorDetailData ownerData = roomActorCache.getData(req.getRoomId(), ownerUid, true);
            if (null == myData || null == ownerData) {
                logger.error("cannot find actor, uid={} ownerUid={}", req.getUid(), ownerUid);
                throw new CommonException(HttpCode.PARAM_ERROR);
            }
            MongoRoomData roomData = roomDao.findData(req.getRoomId());
            if (null == roomData) {
                logger.error("cannot find room roomId={} uid={}", req.getRoomId(), req.getUid());
                throw new CommonException(RoomHttpCode.ROOM_NOT_EXIST);
            }
            // 进入房间权限校验
            checkJoinRoomPrivilege(myData, ownerData, req);
            return getJoinRoomResp(req, roomData, myData, ownerData);
        }
    }

    /**
     * 进入房间权限判断
     */
    private void checkJoinRoomPrivilege(RoomActorDetailData myData, RoomActorDetailData ownerData, GameRoomDTO.EnterDTO req) {
//        if (myData.getValid() != 1) {
//            throw new CommonException(RoomHttpCode.USER_INVALID);
//        }
//        if (ownerData.getValid() != 1) {
//            throw new CommonException(RoomHttpCode.ROOM_BLOCK);
//        }
//        if (roomKickRedis.isKick(req.getRoomId(), myData.getAid())) {
//            throw new CommonException(RoomHttpCode.KICKED);
//        }
//        if (myData.getAid().equals(ownerData.getAid())) {
//            // 房主被禁，不能创建和进入房间
//            String blockTime = blockRedis.checkBlock(actorDao.getActorDataFromCache(ownerData.getAid()).getTn_id(), BlockTnConstant.BLOCK_CREATE_ROOM);
//            if (!ObjectUtils.isEmpty(blockTime)) {
//                throw new CommonException(RoomHttpCode.BLOCK_CREATE_ROOM, blockTime);
//            }
//        } else {
//            if (roomBlacklistDao.isBlock(RoomUtils.getRoomId(req.getRoomId()), myData.getAid())) {
//                throw new CommonException(RoomHttpCode.BLOCKED);
//            }
//        }
    }

    /**
     * 处理进场对象
     */
    private void fillJoinCartoon(RoomActorDetailData myData, RoomActorDetailData detailData, RoomVO resp) {
        EnterCartonData cartonData = new EnterCartonData();
        enterRoomContent.fillJoinCartoon(myData.getRideOption(), cartonData, myData.getAid(), detailData);
        resp.getRoomVisitor().setSmallIcon(cartonData.getSmallIcon());
        resp.getRoomVisitor().setJoinCartoonId(cartonData.getJoinCartoonId());
        resp.getRoomVisitor().setJoinCartoonType(cartonData.getJoinCartoonType());
        resp.getRoomVisitor().setSourceType(cartonData.getSourceType());
        resp.getRoomVisitor().setSourceUrl(cartonData.getSourceUrl());
        resp.getRoomVisitor().setSourceMd5(cartonData.getSourceMd5());
        resp.getRoomVisitor().setCartoonTimes(cartonData.getCartoonTimes());
        // 处理进房通知
        resp.getRoomVisitor().setEntryEffectUrl(cartonData.getEntryEffectUrl());
        resp.getRoomVisitor().setEntryEffectUrlAr(cartonData.getEntryEffectUrlAr());
    }

    /**
     * 格式化房间贡献数值
     */
    private String formatDevotes(long devotes) {
        if (devotes >= 1000000) {
            return new BigDecimal(devotes / 1000000f + "").setScale(1, RoundingMode.HALF_UP) + "M";
        } else if (devotes >= 1000) {
            return new BigDecimal(devotes / 1000f + "").setScale(1, RoundingMode.HALF_UP) + "K";
        } else {
            return devotes + "";
        }
    }

    /**
     * 填充返回数据
     */
    private void fillRoomResp(RoomActorDetailData ownerData, RoomActorDetailData myData, MongoRoomData roomData, RoomVO resp, GameRoomDTO.CreateDTO req) {
        // 房主信息
        resp.copyFromActorData(ownerData);
        resp.getRoomOwner().setIsBeautifulRid(ownerData.getBeautifulRid());
        resp.getRoomOwner().setUserLevel(ownerData.getLevel());
        resp.getRoomOwner().setBadgeList(ownerData.getBadgeList());
        resp.getRoomOwner().setIdentify(ownerData.getIdentify());
        resp.getRoomOwner().setCountry(ownerData.getCountry());
        resp.getRoomOwner().setName(ownerData.getName());
        resp.getRoomOwner().setVip(ownerData.getVipLevel());
        resp.getRoomOwner().setVipMedal(ownerData.getVipMedal());
        resp.getRoomOwner().setHead(ImageUrlGenerator.generateRoomUserUrl(ownerData.getHead(), resp.getRoomOwner().getVip()));
        resp.getRoomOwner().setMicFrame(ownerData.getMicFrame());
        // 用户信息
        resp.getRoomVisitor().setUid(myData.getAid());
        resp.getRoomVisitor().setMyName(myData.getName());
        resp.getRoomVisitor().setMyHead(myData.getHead());
        resp.getRoomVisitor().setIdentify(myData.getIdentify());
        resp.getRoomVisitor().setBubbleId(myData.getBubbleId());
        resp.getRoomVisitor().setUserLevel(myData.getLevel());
        resp.getRoomVisitor().setBadgeList(myData.getBadgeList());
        resp.getRoomVisitor().setStreamId(zegoApi.generateActorStreamId(roomData.getOwnerRid(), myData.getRid(), myData.getAid()));
        // 处理进场动画
        fillJoinCartoon(myData, myData, resp);
        // 房间配置信息
        resp.copyFromMongoRoomData(roomData, req);
        resp.getRoomConfig().setTag(roomData.getTag());
        resp.getRoomConfig().setTagName(roomTags.getTagNameById(roomData.getTag(), req.getSlang()));
        resp.getRoomConfig().setRoomHead(ImageUrlGenerator.generateRoomUserUrl(roomData.getHead(), resp.getRoomOwner().getVip()));

        // 房间贡献度
        long devotes = commonDao.getRoomTotalDevote(roomData.getRid());
        resp.getRoomConfig().setDevotes(devotes);
        resp.getRoomConfig().setDevotesStr(formatDevotes(devotes));
        // 房间会员数
//        resp.getRoomConfig().setMemberCount(roomMemberDao.getMemberCount(req.getRoomId()));
        // 房间固定配置
        resp.getRoomConfig().setFollowTime(roomConfig.getFollowTime());
        resp.getRoomConfig().setDirtyVersion(roomNewService.getDirtyVersion());
        resp.getRoomConfig().setSendGiftExpire(roomConfig.getSendGiftExpireTime());
        resp.getRoomConfig().setVipRoomConfig(roomConfig.getVipRoomConfig());
        resp.getRoomConfig().setFollowWord(roomConfig.getRandomFollowStr(req.getSlang()));
        resp.getRoomConfig().setHttpHeartSwitch(roomConfig.getHttpHeartSwitch());
        resp.getRoomConfig().setEmojiSwitch(roomConfig.getEmojiSwitch());
        resp.getRoomConfig().setUploadLog(0);

        // 房间插件状态
        resp.getRoomConfig().setGuideList(roomConfig.getRandomGuideList(req.getSlang()));
        resp.getRoomConfig().setWelcomeWord(roomConfig.getWelcomeWordList(req.getSlang()));
    }


    /**
     * 其他用户进入房间
     */
    private void otherJoinRoom(RoomActorDetailData ownerData, GameRoomDTO.EnterDTO req, RoomVO resp) {
        // 设置房间角色
        RoomMemberData managerData = roomMemberDao.findData(req.getRoomId(), req.getUid());
        // 0 房主 1 管理员 2 观众 3 会员
        int roomRoleType = roomMemberDao.getRoleContainMember(managerData, req.getRoomId(), req.getUid());
        resp.getRoomVisitor().setRole(roomRoleType);
        resp.getRoomVisitor().setAdmin(roomRoleType == RoomRoleType.HOST || roomRoleType == RoomRoleType.MANAGER ? 1 : 2);
        resp.getRoomVisitor().setIsMember(roomRoleType != RoomRoleType.AUDIENCE ? 1 : 0);
        resp.getRoomVisitor().setIsFollowHost(followDao.isFollowed(req.getUid(), ownerData.getAid()) ? 1 : 0);
    }

    /**
     * 房主进入房间
     */
    private void ownerJoinRoom(GameRoomDTO.EnterDTO req, RoomVO resp) {
        resp.getRoomVisitor().setAdmin(1);
        resp.getRoomOwner().setName(resp.getRoomVisitor().getMyName());
        resp.getRoomOwner().setHead(resp.getRoomVisitor().getMyHead());
        // 每日任务房主进入自己的房间
        dailyTaskService.sendToMq(new DailyTaskMqData(req.getUid(), 3, DateHelper.ARABIAN.formatDateInDay2(), 1));
    }


    private RoomVO getJoinRoomResp(GameRoomDTO.CreateDTO req, MongoRoomData roomData, RoomActorDetailData myData, RoomActorDetailData ownerData) {
        RoomVO resp = new RoomVO();
        RoomGameConfigVO roomGameConfig = getRoomGameConfig(req);
        resp.getRoomVisitor().setVip(vipInfoDao.getIntVipLevelFromCache(req.getUid()));
        resp.getRoomVisitor().setVipMedal(myData.getVipMedal());
        // 基础信息填充
        fillRoomResp(ownerData, myData, roomData, resp, req);
        resp.getRoomVisitor().setRidData(myData.getRidData());
        resp.getRoomVisitor().setRid(myData.getRid());
        resp.getRoomVisitor().setMyName(myData.getName());
        resp.getRoomVisitor().setCoinBalance(roomGameConfig.getLeftCoins());
        if (myData.getAid().equals(ownerData.getAid())) {
            if (roomData.getOwnerRid() != ownerData.getRid()) {
                // 同步房主最新的rid
                roomDao.updateField(req.getRoomId(), "ownerRid", ownerData.getRid());
            }
            ownerJoinRoom(req, resp);
        } else {
            otherJoinRoom(ownerData, req, resp);
        }
        resp.getRoomConfig().setRoomGameConfig(roomGameConfig);

        // 设置房间主题
//        resp.getRoomConfig().setRoomTheme(roomNewService.getRoomTheme(resp.getRoomConfig().getMicTheme(), roomData));
        // 普通房间设置相关属性
        resp.getRoomVisitor().setTalkForbidTime(micFrameRedis.getForbidTime(req.getRoomId(), req.getUid()));
        resp.getRoomVisitor().setMicForbidTime(roomMicRedis.getMicForbidTime(req.getRoomId(), req.getUid()));

        if (pwdCheck(roomData, ownerData, myData, resp)) {
            // 进入房间后置处理
            afterJoinRoom(req, roomData, resp);
            // 最后才填充麦位信息
            fillMicData(roomData, req, resp);
        }
        // 房间配置
        fillActorRoomConfig(req.getUid(), resp);
        // 增加进房来源
        if (!ObjectUtils.isEmpty(req.getEnterRoomSource())){
            roomPlayerRedis.setEnterRoomSource(req.getRoomId(), req.getUid(), req.getEnterRoomSource());
        }
        return resp;
    }


    /**
     * 判断房间是否有密码，有密码时不返回流信息，app弹窗输入密码后再返回(pwd_check)
     */
    public boolean pwdCheck(MongoRoomData roomData, RoomActorDetailData ownerData, RoomActorDetailData myData, RoomVO resp) {
        // 房间密码处理
        if (!StringUtils.isEmpty(roomData.getPwd()) && !ownerData.getAid().equals(myData.getAid())) {
            // 删除旧的进房记录，py回重新写入带密码的值
            roomRedis.deleteRoomJoin(myData.getAid());
            return false;
        } else {
            // 这里增加进房记录
            roomRedis.addRoomJoin(myData.getAid(), roomData.getRid());
            // 第三方数据
            String streamId = roomNewService.genStreamId(roomData.getRid(), roomData.getPwd());
            resp.getRoomConfig().setStreamId(streamId);
            resp.getRoomConfig().setZegoToken(zegoService.getZegoToken(myData.getAid(), streamId));
//            resp.getRoomConfig().setAgoraToken(agoraService.getAgoraToken(streamId, myData.getAid()));
//            resp.getRoomConfig().setAgoraRtmToken(agoraService.getAgoraRtmToken(myData.getAid()));
            return true;
        }
    }

    /**
     * 填充麦位信息
     */
    private void fillMicData(MongoRoomData roomData, GameRoomDTO.CreateDTO req, RoomVO resp) {
        // 处理actor信息
        PageUtils.PageData<RoomActorDetailData> pageData = roomActorListService.findDetailList(req, 1, ACTOR_PAGE_SIZE, true, false, false);
        resp.getRoomActor().setNextPage(pageData.nextPage);
        resp.getRoomActor().setPageSize(pageData.pageSize);
        resp.getRoomActor().setTotalActors(pageData.totalSize);
        resp.getRoomActor().setRoomActorList(pageData.list);

        ActorData myActorData = actorDao.getActorDataFromCache(req.getUid());
        if (myActorData.getRobot() != 1) {
            // 检查是否可以自动上麦
            RoomMicListData listData = roomMicService.autoUpGameRoomMic(roomData, req.getUid(), req);
            // 处理麦位信息
            if (listData == null) {
                // 缓存的最新麦位数据
                RoomMicListVo roomMicListVo = roomMicRedis.getRoomMicFromRedis(req.getRoomId());
                if (null != roomMicListVo) {
                    resp.getRoomMic().setVersion(roomMicListVo.getVersion());
                    resp.getRoomMic().setList(roomMicListVo.getList());
                } else {
                    listData = roomMicService.getRoomMicList(req.getRoomId(), roomDao.getRoomType(roomData), roomData.getMicSize());
                    doFillMicData(roomData, listData, resp);
                }
            } else {
                doFillMicData(roomData, listData, resp);
            }
        } else {
            RoomMicListData listData = roomMicService.getRoomMicList(req.getRoomId(), roomDao.getRoomType(roomData), roomData.getMicSize());
            doFillMicData(roomData, listData, resp);
        }
    }

    private void doFillMicData(MongoRoomData roomData, RoomMicListData listData, RoomVO resp) {
        // 这里重新渲染数据
        resp.getRoomMic().setVersion(listData.getVersion());
        resp.getRoomMic().setList(convertRespMicList(roomData, listData.getRoomId(), listData.getList()));
    }


    protected List<RoomMicInfoObject> convertRespMicList(MongoRoomData roomData, String
            roomId, List<RoomMicData> micList) {
        List<RoomMicInfoObject> retList = new ArrayList<>();
//        Map<String, CheatGiftRedis.CheatGiftData> map = cheatGiftRedis.fillCheatGift(micList);
        List<String> inGameList = new ArrayList<>();
        if (RoomUtils.isGameRoom(roomId)) {
            inGameList = sudGameRedis.getInGameUserList(roomId);
        }
        for (RoomMicData micData : micList) {
            RoomMicInfoObject rspData = new RoomMicInfoObject();
            rspData.setIndex(micData.getPosition());
            rspData.setStatus(micData.getStatus());
            rspData.setMute(micData.getMute());
            String uid = micData.getUid();
            if (!StringUtils.isEmpty(uid)) {
                RoomActorDetailData detailData = roomActorCache.getData(roomId, uid, false);
                if (detailData == null) {
                    logger.error("can not find room actor data. roomId={} uid={}", roomId, uid);
                    continue;
                }
                RoomMicUserObject userData = new RoomMicUserObject();
                userData.setHead(detailData.getHead());
                userData.setName(detailData.getName());
                userData.setAid(detailData.getAid());
                userData.setRipple_url(detailData.getRippleUrl());
                userData.setMic_frame(detailData.getMicFrame());
                userData.setVip_level(detailData.getVipLevel());
                userData.setVipMedal(detailData.getVipMedal());
                userData.setRole(detailData.getRole());
                userData.setIdentify(detailData.getIdentify());
                userData.setViceHost(detailData.getViceHost());
//                CheatGiftRedis.CheatGiftData cheatGiftData = map.get(uid);
//                userData.setVoice_type(cheatGiftData.getVoiceType());
//                if (!StringUtils.isEmpty(cheatGiftData.getPrankMicFrame())) {
//                    userData.setMic_frame(cheatGiftData.getPrankMicFrame());
//                }
                userData.setGameRunning(inGameList.contains(uid) ? 1 : 0);
                rspData.setUser(userData);
                userData.setStreamId(zegoApi.generateActorStreamId(roomData.getOwnerRid(), detailData.getRid(), detailData.getAid()));
            }
            retList.add(rspData);
        }
        return retList;
    }


    /**
     * 进入房间后置处理
     */
    private void afterJoinRoom(GameRoomDTO.CreateDTO dto, MongoRoomData roomData, RoomVO resp) {
        TaskFactory.getFactory().add(new Task() {
            @Override
            protected void execute() {

                roomWebSender.sendForceEnterRoom(dto.getRoomId(), dto.getUid(), roomData.getRtc_type());
                // 资源按使用次数过期依赖这条消息
//                roomNewService.sendEnterRoomMq(dto.getUid(), dto.getRoomId());
                // 非房主加入房间记录
//                recentlyRoomRedis.addRecentlyActor(dto.getRoomId(), dto.getUid());
                // 记录进入房间的时间
                if (0 < dto.getRequestTime() && dto.getRequestTime() < System.currentTimeMillis() + 1000000L) {
                    recentlyRoomRedis.addEnterRoomClientTime(dto.getUid(), dto.getRequestTime());
                }
                roomPlayerRedis.setActorEnterGameRoomType(dto.getRoomId(), dto.getUid(), dto.getEnterRoomType());
                roomVisitorsRedis.addGameRoomTypeVisitorRecord(resp.getRoomConfig().getRoomGameConfig().getGameType(), dto.getUid());
            }
        });
    }


    /**
     * 退出房间记录
     */
    public void quitRoom(RoomDTO req) {
        String uid = req.getUid();
        String roomId = req.getRoomId();
        if (StringUtils.isEmpty(roomId)) {
            logger.info("roomId={} is empty", req.getRoomId());
            throw new CommonException(RoomHttpCode.PERMIT_ERROR);
        }
        // 8.42起客户端不发送2003离开房间的mars消息
//        long enterRoomTime = recentlyRoomRedis.getEnterRoomClientTime(uid);
//        if (0 != req.getRequestTime() && req.getRequestTime() < enterRoomTime) {
//            logger.info("quit request invalid, roomId={} uid={} enterRoomTime={} reqTime={}", roomId, uid, enterRoomTime, req.getRequestTime());
//        } else {
//            roomWebSender.sendLeaveRoom(roomId, uid);
//        }
        roomWebSender.sendLeaveRoom(roomId, uid);
        String gameId = sudGameRedis.getGameIdByRoomId(roomId);
        if (!StringUtils.isEmpty(gameId)) {
            InnerSudGameDTO dto = new InnerSudGameDTO();
            dto.setUid(uid);
            dto.setAid(uid);
            dto.setGameId(gameId);
            dto.setRoomId(roomId);
            gameService.quiteGame(dto);
        }
    }


    /**
     * 填充进入房间的用户自己设置的配置
     */
    private void fillActorRoomConfig(String uid, RoomVO resp) {
        ActorConfigData configData = actorConfigDao.findData(uid);
        if (null == configData) {
            configData = actorConfigDao.initActorConfigData(uid);
            actorConfigDao.save(configData);
        }
        Map<String, Object> config = configData.getGame_room_config();
        if (config == null) {
            config = new HashMap<>(ActorConfigDao.DEF_GAME_ROOM_CONFIG);
            configData.setGame_room_config(config);
            actorConfigDao.save(configData);
        }
        resp.getRoomConfig().setActorRoomConfig(config);
    }

    public RoomGameConfigVO switchGame(GameRoomDTO.CreateDTO req) {
        if (req.getGameType() == null || req.getGameType() <= 0) {
            logger.info("gameType is invalid req:{}", req);
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        String hostUid = RoomUtils.getRoomHostId(req.getRoomId());
        if (!req.getUid().equals(hostUid)) {
            throw new CommonException(RoomHttpCode.NOT_HAVE_PERMISSION);
        }
        return getRoomGameConfig(req);
    }

    public RoomGameConfigVO getRoomGameConfig(GameRoomDTO.CreateDTO req) {
        int enterRoomType = req.getEnterRoomType();
        Integer gameType;
        Integer currencyType;
        Integer currency;

        String gameId = "";
//        gameType = req.getGameType();
//        currencyType = req.getCurrencyType();
//        currency = req.getCurrencyValue();
        int createGameStatus = 1;

        ActorData myActorData = actorDao.getActorData(req.getUid());
        int leftCoins = myActorData.getHeartGot();
        if (enterRoomType == RoomConfigConstant.ENTER_ROOM_CREAT_TYPE) {
            currency = req.getCurrencyValue();
            if (leftCoins < currency) {
                throw new CommonException(RoomHttpCode.NOT_ENOUGH_COIN);
            }
        }

        // 测试逻辑
//        if (enterRoomType == RoomConfigConstant.ENTER_ROOM_CREAT_TYPE) {
//            gameType = req.getGameType();
//            currencyType = req.getCurrencyType();
//            currency = req.getCurrencyValue();
//            leftCoins = leftCoins - currency;
//        } else {
//            gameType = gameRoomRedis.getGameType(req.getRoomId());
//            gameType = gameType == null ? 2 : gameType;
//            currencyType = 1;
//            RoomSudGameConfigInfo gameConfigInfo = roomConfig.getSudGameInfoMap().get(gameType);
//            currency = gameConfigInfo.getCoinFeeList().get(0);
//        }


        // 正式逻辑
        String inGameId = sudGameRedis.getPlayerData(req.getUid());
        if (!StringUtils.isEmpty(inGameId)) {
            SudGameData inGameData = sudGameDao.findData(inGameId);
            if (inGameData != null && inGameData.getStatus() == SudGameConstant.GAME_PROCESSING) {
                if (!inGameData.getRoomId().equals(req.getRoomId())) {
                    // 用户在其他房间有未退出且进行中的游戏
                    logger.info("inGameId:{} inGameRoomId:{} game is going can not create or join other room",
                            inGameId, inGameData.getRoomId());
                    throw new CommonException(RoomHttpCode.GOING_ON_GAME_PROCESSING);
                }
            }
        }

        String gameIdType = sudGameRedis.getGameIdAndTypeByRoomId(req.getRoomId());
        if (StringUtils.isEmpty(gameIdType)) {
            if (enterRoomType == RoomConfigConstant.ENTER_ROOM_CREAT_TYPE) {
                gameType = req.getGameType();
                currencyType = req.getCurrencyType();
                currency = req.getCurrencyValue();
                leftCoins = leftCoins - currency;
            } else {
                if (enterRoomType == RoomConfigConstant.ENTER_ROOM_INVITE_TYPE) {
                    throw new CommonException(RoomHttpCode.GAME_ROOM_NOT_HAVE_GAME);
                } else {
                    // 匹配进房
                    throw new CommonException(RoomHttpCode.GAME_ROOM_NOT_HAVE_GAME);
                }
                // 匹配进房
//                gameType = 2;
//                currencyType = 1;
//                RoomSudGameConfigInfo gameConfigInfo = roomConfig.getSudGameInfoMap().get(gameType);
//                currency = gameConfigInfo.getCoinFeeList().get(0);
            }
        } else {
            String[] split = gameIdType.split("-");
            gameId = split[0];
            int runningGameType = Integer.parseInt(split[1]);
            SudGameData data = sudGameDao.findData(gameId);
            if (data == null) {
                logger.info("not find from mongodb gameId:{}", gameId);
                if (enterRoomType != RoomConfigConstant.ENTER_ROOM_CREAT_TYPE) {
                    throw new CommonException(RoomHttpCode.GAME_ROOM_NOT_HAVE_GAME);
                }
                gameType = req.getGameType();
                currencyType = req.getCurrencyType();
                currency = req.getCurrencyValue();
                leftCoins = leftCoins - currency;
//                gameType = 2;
//                currencyType = 1;
//                RoomSudGameConfigInfo gameConfigInfo = roomConfig.getSudGameInfoMap().get(gameType);
//                currency = gameConfigInfo.getCoinFeeList().get(0);
            } else if (data.getStatus() == SudGameConstant.GAME_CLOSED
                    || data.getStatus() == SudGameConstant.GAME_FINISH) {
                if (enterRoomType == RoomConfigConstant.ENTER_ROOM_INVITE_TYPE) {
                    throw new CommonException(RoomHttpCode.GAME_ROOM_NOT_HAVE_GAME);
                } else if (enterRoomType == RoomConfigConstant.ENTER_ROOM_CREAT_TYPE) {
                    // 游戏结束了，游戏逻辑那边创建新的
                    gameId = "";
                    gameType = req.getGameType();
                    currencyType = req.getCurrencyType();
                    currency = req.getCurrencyValue();
                    leftCoins = leftCoins - currency;
                } else {
                    gameId = "";
                    gameType = data.getGameType();
                    currencyType = data.getCurrencyType();
                    currency = data.getCurrency();
                }

            } else if (data.getStatus() == SudGameConstant.GAME_PROCESSING) {
                if (enterRoomType == RoomConfigConstant.ENTER_ROOM_CREAT_TYPE) {
                    // 房间内切换游戏，创建游戏失败
                    if (req.getSwipe() == 1) {
                        throw new CommonException(RoomHttpCode.NOT_SWITCH_GAME_RUNNING);
                    }
                    createGameStatus = 2;
                }
                gameType = data.getGameType();
                currencyType = data.getCurrencyType();
                currency = data.getCurrency();
            } else {
                String roomHostId = RoomUtils.getRoomHostId(req.getRoomId());
                if (enterRoomType == RoomConfigConstant.ENTER_ROOM_INVITE_TYPE
                        && !roomHostId.equals(req.getUid())
                        && CollectionUtils.isEmpty(data.getPlayerList())
                        && roomPlayerRedis.getRoomActorsCount(req.getRoomId()) == 0) {
                    logger.info("room not actor return uid:{} roomId:{} gameId:{}",
                            req.getUid(), req.getRoomId(), gameId);
                    throw new CommonException(RoomHttpCode.GAME_ROOM_NOT_HAVE_GAME);
                }
                // 匹配中，匹配暂停中
                gameType = data.getGameType();
                currencyType = data.getCurrencyType();
                currency = data.getCurrency();
                if (enterRoomType == RoomConfigConstant.ENTER_ROOM_CREAT_TYPE && roomHostId.equals(req.getUid())) {
                    //房主创建进房，切换了游戏类型或入场费
                    if (runningGameType != req.getGameType() || req.getCurrencyValue() != data.getCurrency()) {
                        // 旧的游戏需要先解散
                        gameType = req.getGameType();
                        currencyType = req.getCurrencyType();
                        currency = req.getCurrencyValue();
                        leftCoins = leftCoins - currency;
                        gameId = "";
                    }
                }
            }
        }
        currencyType = 1;
        RoomSudGameConfigInfo gameConfigInfo = roomConfig.getSudGameInfoMap().get(gameType);
        // 新版本游戏房相关配置
        RoomGameConfigVO roomGameConfig = new RoomGameConfigVO();
        roomGameConfig.setCreateGameStatus(createGameStatus);
        roomGameConfig.setGameType(gameType);
        roomGameConfig.setName(gameConfigInfo.getName());
        roomGameConfig.setNameAr(gameConfigInfo.getNameAr());
        roomGameConfig.setIconUrl(gameConfigInfo.getIconUrl());
        roomGameConfig.setCurrencyType(currencyType);
        roomGameConfig.setCurrencyValue(currency);
        roomGameConfig.setWidth(gameConfigInfo.getWidth());
        roomGameConfig.setHeight(gameConfigInfo.getHeight());
        roomGameConfig.setBackground(gameConfigInfo.getBackground());
        roomGameConfig.setGameId(gameId);
        roomGameConfig.setGameUrl(getGameUrl(enterRoomType, gameType, gameId, currencyType, currency));
        roomGameConfig.setLeftCoins(leftCoins);
        roomGameConfig.setSmallIconUrl(gameConfigInfo.getSmallIconUrl());
        roomGameConfig.setKeepIconUrl(gameConfigInfo.getKeepIconUrl());
        sendRoomInfoChangeMsg(req.getRoomId(), gameConfigInfo.getBackground(),
                gameConfigInfo.getKeepIconUrl(), gameType == null ? 0 : gameType);
//        if (req.getEnterRoomType() == RoomConfigConstant.ENTER_ROOM_CREAT_TYPE) {
//            gameRoomRedis.saveGameType(req.getRoomId(), req.getGameType());
//        }
        return roomGameConfig;
    }

    private String getGameUrl(Integer enterRoomType, Integer gameType, String gameId, Integer currencyType, Integer currency) {
        String baseUrl;
        if (ServerConfig.isProduct()) {
            baseUrl = SudGameConstant.ROOM_GAME_URL;
        } else {
            baseUrl = SudGameConstant.TEST_ROOM_GAME_URL;
        }
        UriComponentsBuilder urlBuilder = UriComponentsBuilder.fromHttpUrl(baseUrl);
        if (enterRoomType != null) {
            urlBuilder.queryParam("enterRoomType", enterRoomType);
        }
        if (gameType != null) {
            urlBuilder.queryParam("gameType", gameType);
        }
        if (currencyType != null) {
            urlBuilder.queryParam("currencyType", currencyType);
        }
        if (currency != null) {
            urlBuilder.queryParam("currency", currency);
        }
        if (!StringUtils.isEmpty(gameId)) {
            urlBuilder.queryParam("gameId", gameId);
        }
        return urlBuilder.build(false).encode().toUriString();
    }


    /**
     * 邀请用户玩游戏
     */
    public void inviteUser(RoomDTO dto) {
        String uid = dto.getUid();
        String roomId = dto.getRoomId();
        RoomInviteUserMsg msg = new RoomInviteUserMsg();
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        String gameId = sudGameRedis.getGameIdByRoomId(roomId);
        UserInfoObject userInfoObject = new UserInfoObject();
        userInfoObject.setUid(uid);
        userInfoObject.setName(actorData.getName());
        userInfoObject.setHead(actorData.getHead());
        msg.setInviteUser(userInfoObject);
        msg.setInviteMsgEn("Invite to join the game");
        msg.setInviteMsgAr("قم بالدعوة للانضمام إلى اللعبة");
        msg.setGameId(gameId);
        roomWebSender.sendPlayerWebMsg(roomId, dto.getUid(), dto.getAid(), msg, false);
    }

    private void sendRoomInfoChangeMsg(String roomId, String themeUrl
            , String keepIconUrl, int gameType) {
        TaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                // 房间名字变更
                RoomInfoChangeMsg msg = new RoomInfoChangeMsg();
                msg.setRid(roomId);
                msg.setThemeUrl(themeUrl);
                msg.setRoomName("");
                msg.setRoomHead("");
                msg.setKeepIconUrl(keepIconUrl);
                msg.setGameType(gameType);
                roomWebSender.sendRoomWebMsg(roomId, "", msg, true);
            }
        });
    }

}

package com.quhong.service.impl;

import com.quhong.constant.LoginConstant;
import com.quhong.constant.LoginHttpCode;
import com.quhong.data.RegisterOrLoginContext;
import com.quhong.data.ValidPhoneAccountData;
import com.quhong.data.dto.CheckOutPhoneDTO;
import com.quhong.exception.CommonException;
import com.quhong.mongo.dao.PhoneAccountDao;
import com.quhong.mongo.data.PhoneAccountData;
import com.quhong.redis.TnCheckRedis;
import com.quhong.service.AbstractLoginService;
import com.quhong.service.HuaWeiSendSmsService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;

@Service
public class HuaWeiPhoneLoginService extends AbstractLoginService {
    private static final Logger logger = LoggerFactory.getLogger(HuaWeiPhoneLoginService.class);
    @Resource
    private PhoneAccountDao phoneAccountDao;
    @Resource
    private FireBasePhoneLoginService fireBasePhoneLoginService;
    @Resource
    private HuaWeiSendSmsService huaWeiSendSmsService;
    @Resource
    private TnCheckRedis tnCheckRedis;

    @Override
    protected void initData(RegisterOrLoginContext context) {
        fireBasePhoneLoginService.initData(context);
    }

    @Override
    public RegisterOrLoginContext beforeLogin(RegisterOrLoginContext context) {
        String account = context.getAccount();
        String pwd = context.getPassword();
        String phoneCode = context.getHuaweiPhoneCode();
        phoneCode = StringUtils.isEmpty(phoneCode) ? "" : phoneCode.trim();
        if (!StringUtils.isEmpty(phoneCode)) { //  注册或者修改密码
            if (account.length() > 32) {
                logger.info("account length error context={} ", context);
                loginDataCountService.recordLoginStatusToTga(context, LoginConstant.LOGIN_STATE_SEVER_PARM_ERROR);
                throw new CommonException(LoginHttpCode.PHONE_LOGIN_ACCOUNT_ERROR);
            }
            String afterCode = handleArbNum(phoneCode);
            if (afterCode.length() != 6) {
                logger.info("phoneCode valid fail length!=6 account={} phoneCode={} afterCode={}  ",
                        account, phoneCode, afterCode);
                loginDataCountService.recordLoginStatusToTga(context, LoginConstant.LOGIN_STATE_SEVER_PARM_ERROR);
                throw new CommonException(LoginHttpCode.PHONE_LOGIN_ACCOUNT_ERROR);
            }
            String cacheCode = tnCheckRedis.getAccountCode(account);
            if (StringUtils.isEmpty(cacheCode)) {
                logger.info("cacheCode is empty account={} phoneCode={} afterCode={}  ",
                        account, phoneCode, afterCode);
                loginDataCountService.recordLoginStatusToTga(context, LoginConstant.LOGIN_STATE_SEVER_PARM_ERROR);
                throw new CommonException(LoginHttpCode.PHONE_LOGIN_ACCOUNT_ERROR);
            }

            if (afterCode.equals(cacheCode)) {
                context.setThirdUid(account);
                context.setRealPhoneNum(account);
                context.setCheckPhoneAccount(false);
                fireBasePhoneLoginService.getExistPhoneAccount(context);
                return context;
            } else {
                logger.info("phoneCode valid fail account={} phoneCode={} afterCode={} cacheCode={} ",
                        account, phoneCode, afterCode, cacheCode);
                loginDataCountService.recordLoginStatusToTga(context, LoginConstant.LOGIN_STATE_SEVER_PARM_ERROR);
                throw new CommonException(LoginHttpCode.PHONE_LOGIN_ACCOUNT_ERROR);
            }

        }
        PhoneAccountData phoneAccountData = fireBasePhoneLoginService.getExistPhoneAccount(context);
        if (null == phoneAccountData) {
            logger.info("not find phoneAccount context={} ", context);
            loginDataCountService.recordLoginStatusToTga(context, LoginConstant.LOGIN_STATE_SEVER_PARM_ERROR);
            throw new CommonException(LoginHttpCode.PHONE_LOGIN_NOT_FIND);
        } else {
            if (!phoneAccountData.getPwd().equals(pwd)) {
                logger.info("phoneAccount pwd error context={} ", context);
                loginDataCountService.recordLoginStatusToTga(context, LoginConstant.LOGIN_STATE_SEVER_PARM_ERROR);
                throw new CommonException(LoginHttpCode.SPECIAL_GUST_LOGIN_PWD_ERROR);
            } else {
                context.setThirdUid(phoneAccountData.getUid());
                context.setCheckPhoneAccount(true);
            }
        }
        return context;
    }

    @Override
    public RegisterOrLoginContext afterLogin(RegisterOrLoginContext context) {
        return fireBasePhoneLoginService.afterLogin(context);
    }


    public void sendSms(CheckOutPhoneDTO dto) {
        if (dto.getNew_versioncode() < 5) {
            throw new CommonException(LoginHttpCode.UPDATE_YOUR_APP);
        }
        ValidPhoneAccountData validPhoneAccountData = fireBasePhoneLoginService.getValidPhoneAccount(null, dto.getpCountryCode(), dto.getpNumber());
        if (validPhoneAccountData == null) {
            logger.info("params error dto={}", dto);
            throw new CommonException(LoginHttpCode.PARAM_ERROR);
        } else {
            if (!validPhoneAccountData.isValidAccount()) {
                logger.info("isValidAccount false params error dto={}", dto);
                throw new CommonException(LoginHttpCode.PARAM_ERROR);
            }
            String newAccount = validPhoneAccountData.getNewAccount();
            long remainTime = tnCheckRedis.getAccountCodeExpire(newAccount);
            if (remainTime > 60) {
                // 您已获取验证码，请勿频繁操作
                logger.info("remainTime gt 60s remainTime={} dto={}", remainTime, dto);
                throw new CommonException(LoginHttpCode.PARAM_ERROR);
            }
            try {
                String code = huaWeiSendSmsService.sendSms(newAccount);
                if (StringUtils.isEmpty(code)) {
                    logger.info("huaWeiSendSmsService send sms error dto={}", dto);
                    throw new CommonException(LoginHttpCode.SERVER_ERROR);
                } else {
                    tnCheckRedis.setAccountCode(newAccount, code);
                }
            } catch (Exception e) {
                logger.error("huaWeiSendSmsService send sms error dto={}", dto, e);
                throw new CommonException(LoginHttpCode.SERVER_ERROR);
            }

        }

    }


}

package com.quhong.service;

import com.alibaba.fastjson.JSONObject;
import com.quhong.analysis.ActorCampaignLogEvent;
import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.constant.ExpireTimeConstant;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.date.DateSupport;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.LoginSuccessData;
import com.quhong.monitor.MonitorSender;
import com.quhong.mysql.dao.ActorCampaignDao;
import com.quhong.mysql.dao.AppsFlyerCallbackLogDao;
import com.quhong.mysql.dao.FirebaseLoginSuccessDao;
import com.quhong.mysql.data.ActorCampaignData;
import com.quhong.mysql.data.ActorCampaignLogData;
import com.quhong.mysql.data.AppsFlyerCallbackLogData;
import com.quhong.mysql.data.FirebaseLoginSuccessData;
import com.quhong.redis.DataRedisBean;
import com.quhong.utils.ActorUtils;
import com.quhong.utils.BigQueryHelper;
import com.quhong.utils.CountryUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.TimeZone;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2022/9/13
 */
@Service
public class BigQueryService {

    private static final Logger logger = LoggerFactory.getLogger(BigQueryService.class);

    private static final String YOUSTAR_PROJECT_NAME = "findher-87c07";
    private static final String YOUSTAR_ANALYTICS_SUFFIX = "152452038";

    private static final String YOUSTAR_PRO_PROJECT_NAME = "youstar-lite";
    private static final String YOUSTAR_PRO_ANALYTICS_SUFFIX = "250084930";

    @Resource
    private FirebaseLoginSuccessDao firebaseLoginSuccessDao;
    @Resource
    private BigQueryHelper bigQueryHelper;
    @Resource
    private BigQueryHelper proBigQueryHelper;
    @Resource
    private ActorCampaignDao actorCampaignDao;
    @Resource
    private AppsFlyerCallbackLogDao appsFlyerCallbackLogDao;
    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate mainCluster;
    @Autowired(required = false)
    private EventReport eventReport;
    @Resource
    private MonitorSender monitorSender;

    public void dealFirebaseLoginSuccess(int startTime) {
        // Youstar
        dealFirebaseLoginSuccess(startTime, YOUSTAR_PROJECT_NAME, YOUSTAR_ANALYTICS_SUFFIX);
        // Youstar Pro
        // dealFirebaseLoginSuccess(startTime, YOUSTAR_PRO_PROJECT_NAME, YOUSTAR_PRO_ANALYTICS_SUFFIX);
    }

    public void dealFirebaseLoginSuccess(int startTime, String projectName, String analyticsSuffix) {
        //获取数据
        try {
            // if (true) {
            //     // 从BigQuery获取归因数据异常报错，这里不再请求，可能没续费，不在再请求firebase
            //     // deal fire base login success data error. e=Not found: Table youstar-lite:analytics_250084930.events_20250106 was not found in location U
            //     return;
            // }
            String date = DateSupport.yyyyMMdd(Instant.ofEpochSecond(startTime).atZone(ZoneOffset.ofHours(8)).toLocalDate());
            List<LoginSuccessData> analyticsData = getAnalyticsDataFromBigQuery(date, projectName, analyticsSuffix);
            if (CollectionUtils.isEmpty(analyticsData)) {
                logger.info("analyticsData is null");
                return;
            }
            int size = analyticsData.size();
            logger.info("analytics size={}", size);
            //分配处理
            int toIndex = 1000;
            for (int i = 0; i < size; i += 1000) {
                if (i + 1000 > size) {
                    toIndex = size - i;
                }
                // 存入数据库
                saveFireBaseLoginSuccessDataToDb(analyticsData.subList(i, i + toIndex), startTime);
            }
            BaseTaskFactory.getFactory().addSlow(new Task() {
                @Override
                protected void execute() {
                    saveToActorCampaignLog(analyticsData, startTime);
                }
            });
        } catch (Exception e) {
            logger.error("deal fire base login success data error. e={}{}", e.getMessage(), e);
        }
    }

    public void dealAppsFlyerCallbackLog(int startTime, int endTime) {
        //先根据开始时间结束时间获取数据库中的数据 分页 拿数据
        try {
            int page = 1;
            int count = 0;
            int pageSize = 1000;
            int index = 0;
            while (true) {
                if (index > 200) {
                    logger.error("deal fcm data count exceed 200.");
                    break;
                }
                index++;
                //拿数据
                int start = (page - 1) * pageSize;
                List<AppsFlyerCallbackLogData> dataList = appsFlyerCallbackLogDao.findList(startTime, endTime, start, pageSize);
                BaseTaskFactory.getFactory().addSlow(new Task() {
                    @Override
                    protected void execute() {
                        saveToActorCampaignLog(dataList);
                    }
                });
                if (CollectionUtils.isEmpty(dataList)) {
                    break;
                }
                count += dataList.size();
                if (dataList.size() < pageSize) {
                    break;
                }
                page++;
            }
            logger.info("deal apps flyer callback data. page={},count={}", page, count);
        } catch (Exception e) {
            logger.error("deal apps flyer callback data fail. e={}{}", e, e.getMessage());
        }
    }

    private void saveToActorCampaignLog(List<AppsFlyerCallbackLogData> analyticsDataList) {
        //处理数据 actor_campaign_log表 和actor_campaign表
        for (AppsFlyerCallbackLogData data : analyticsDataList) {
            ActorCampaignLogData logData = new ActorCampaignLogData();
            if (StringUtils.isEmpty(data.getUid())) {
                continue;
            }
            logData.setUid(data.getUid());
            logData.setGpAdId(data.getAdvertisingId());
            logData.setIdfa(data.getIdfa());
            logData.setCountryCode(data.getCountryCode());
            logData.setLanguage(data.getLanguage());
            logData.setAppId(data.getAppId());
            logData.setAppVer(data.getAppVersion());
            logData.setAttributionTime(convertTimeFormat(data.getInstallTime()));
            logData.setCampaign(data.getCampaign());
            logData.setTouchTime(data.getAttributedTouchTime());
            logData.setMedium(data.getMediaSource());
            logData.setSource("");
            logData.setCtime(data.getCtime());
            // 数数上报
            sendEventReport(logData);
            try {
                saveToActorCampaign(logData);
            } catch (Exception e) {
                logger.error("save data to actor campaign error.logData={} {}", JSONObject.toJSONString(logData), e.getMessage(), e);
            }
        }
    }

    private String convertTimeFormat(String strTime) {
        if (StringUtils.isEmpty(strTime)) {
            return "0";
        }
        try {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
            dateFormat.setTimeZone(TimeZone.getTimeZone("GMT"));
            return String.valueOf(dateFormat.parse(strTime).getTime() / 1000);
        } catch (Exception e) {
            logger.error("convert time format error. strTime = {}", strTime);
            return "0";
        }
    }

    private void saveToActorCampaignLog(List<LoginSuccessData> analyticsDataList, Integer startTime) {
        //处理数据 actor_campaign_log表 和actor_campaign表
        for (LoginSuccessData data : analyticsDataList) {
            ActorCampaignLogData logData = new ActorCampaignLogData();
            if (StringUtils.isEmpty(data.getRid())) {
                if ("first_open".equals(data.getEventName())) {
                    logData.setUid(data.getEventName());
                } else {
                    continue;
                }
            } else {
                logData.setUid(data.getRid());
            }

            String registerTime = "";
            if (!ObjectUtils.isEmpty(logData.getUid()) && logData.getUid().length() == 24) {
                registerTime = new ObjectId(logData.getUid()).getTimestamp() + "";
            }

            logData.setGpAdId(data.getQhDeviceId());
            logData.setIdfa("");
            logData.setCountryCode(CountryUtils.getCountryCodeByCountry(data.getFirebaseCountry()));
            logData.setLanguage(StringUtils.isEmpty(data.getFirebaseLanguage()) ? "" : data.getFirebaseLanguage());
            logData.setAppId(StringUtils.isEmpty(data.getFirebaseAppId()) ? "" : data.getFirebaseAppId());
            logData.setAppVer(StringUtils.isEmpty(data.getFirebaseVer()) ? "" : data.getFirebaseVer());
            logData.setAttributionTime(StringUtils.isEmpty(data.getFirebaseFirstOpenTime()) ?  registerTime : String.valueOf((Long.parseLong(data.getFirebaseFirstOpenTime()) / 1000)));
            logData.setCampaign(StringUtils.isEmpty(data.getFirebaseCampaign()) ? "" : data.getFirebaseCampaign());
            logData.setTouchTime(StringUtils.isEmpty(data.getFirebaseFirstTouchTime()) ? "" : String.valueOf((Long.parseLong(data.getFirebaseFirstTouchTime()) / 1000000)));
            logData.setMedium(StringUtils.isEmpty(data.getFirebaseMedium()) ? "" : data.getFirebaseMedium());
            logData.setSource(StringUtils.isEmpty(data.getFirebaseSource()) ? "" : data.getFirebaseSource());
            logData.setAdGroupName(StringUtils.isEmpty(data.getAdGroupName()) ? "" : data.getAdGroupName());
            logData.setAdGroupId(StringUtils.isEmpty(data.getAdGroupId()) ? "" : data.getAdGroupId());
            logData.setCtime(startTime);
            // 数数上报
            sendEventReport(logData);
            try {
                saveToActorCampaign(logData);
            } catch (Exception e) {
                logger.error("save data to actor campaign error.logData={} {}", JSONObject.toJSONString(logData), e.getMessage(), e);
            }
        }
    }

    private void saveToActorCampaign(ActorCampaignLogData logData) {
        if (!StringUtils.hasLength(logData.getUid()) || "first_open".equals(logData.getUid())) {
            return;
        }
        ActorCampaignData data = new ActorCampaignData();
        BeanUtils.copyProperties(logData, data);
        ActorCampaignData actorCampaignData = actorCampaignDao.selectOneByUid(logData.getUid());
        if (actorCampaignData == null) {
            data.setId(null);
            actorCampaignDao.insert(data);
        }
//        else {
//            data.setCtime(null);
//            data.setId(actorCampaignData.getId());
//            actorCampaignDao.updateById(data);
//        }
    }

    private void saveFireBaseLoginSuccessDataToDb(List<LoginSuccessData> analyticsDataList, Integer startTime) {
        //数据不做处理直接入库 t_firebase_login_success表
        logger.info("start save fire base login success data to db");
        List<FirebaseLoginSuccessData> list = new ArrayList<>();
        for (LoginSuccessData analyticsData : analyticsDataList) {
            FirebaseLoginSuccessData data = new FirebaseLoginSuccessData();
            data.setRid(analyticsData.getRid());
            data.setFirebaseFirstOpenTime(StringUtils.isEmpty(analyticsData.getFirebaseFirstOpenTime()) ? 0 : (int) (Long.parseLong(analyticsData.getFirebaseFirstOpenTime()) / 1000));
            data.setFirebaseFirstTouchTime(StringUtils.isEmpty(analyticsData.getFirebaseFirstTouchTime()) ? 0 : (int) (Long.parseLong(analyticsData.getFirebaseFirstTouchTime()) / 1000000));
            data.setMobileBrandName(StringUtils.isEmpty(analyticsData.getMobileBrandName()) ? "" : analyticsData.getMobileBrandName());
            data.setMobileModelName(StringUtils.isEmpty(analyticsData.getMobileModelName()) ? "" : analyticsData.getMobileModelName());
            data.setQhDeviceId(analyticsData.getQhDeviceId());
            data.setFirebaseLanguage(StringUtils.isEmpty(analyticsData.getFirebaseLanguage()) ? "" : analyticsData.getFirebaseLanguage());
            data.setFirebaseCountry(StringUtils.isEmpty(analyticsData.getFirebaseCountry()) ? "" : analyticsData.getFirebaseCountry());
            data.setFirebaseCity(StringUtils.isEmpty(analyticsData.getFirebaseCity()) ? "" : analyticsData.getFirebaseCity());
            data.setFirebaseAppId(StringUtils.isEmpty(analyticsData.getFirebaseAppId()) ? "" : analyticsData.getFirebaseAppId());
            data.setFirebaseVer(StringUtils.isEmpty(analyticsData.getFirebaseVer()) ? "" : analyticsData.getFirebaseVer());
            data.setFirebaseCampaign(StringUtils.isEmpty(analyticsData.getFirebaseCampaign()) ? "" : analyticsData.getFirebaseCampaign());
            data.setFirebaseMedium(StringUtils.isEmpty(analyticsData.getFirebaseMedium()) ? "" : analyticsData.getFirebaseMedium());
            data.setFirebaseSource(StringUtils.isEmpty(analyticsData.getFirebaseSource()) ? "" : analyticsData.getFirebaseSource());
            data.setAdGroupName(StringUtils.isEmpty(analyticsData.getAdGroupName()) ? "" : analyticsData.getAdGroupName());
            data.setAdGroupId(StringUtils.isEmpty(analyticsData.getAdGroupId()) ? "" : analyticsData.getAdGroupId());
            data.setCtime(startTime);
            list.add(data);
        }
        try {
            firebaseLoginSuccessDao.batchInsert(list);
        } catch (Exception e) {
            logger.error("batch insert firebase login success to db error. e={} {} {}", e.getMessage(), e, e.getStackTrace());
        }
    }

    private List<LoginSuccessData> getAnalyticsDataFromBigQuery(String date, String projectName, String analyticsSuffix) {
        //拼接sql
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT ");
        sql.append("t.`user_id` as rid,");
        sql.append("(select value.int_value from t.user_properties where key = 'first_open_time') as firebaseFirstOpenTime,");
        sql.append("t.`user_first_touch_timestamp` as firebaseFirstTouchTime,");
        sql.append("t.`device`.`mobile_brand_name` as mobileBrandName,");
        sql.append("t.`device`.`mobile_model_name` as mobileModelName,");
        sql.append("t.`device`.`advertising_id` as qhDeviceId,");
        sql.append("t.`device`.`language` as firebaseLanguage, ");
        sql.append("t.`geo`.`country` as firebaseCountry,");
        sql.append("t.`geo`.`city` as firebaseCity,");
        sql.append("t.`app_info`.`id` as firebaseAppId,");
        sql.append("t.`app_info`.`version` as firebaseVer,");
        sql.append("t.`traffic_source`.`name` as firebaseCampaign,");
        sql.append("t.`traffic_source`.`source` as firebaseSource,");
        sql.append("t.`session_traffic_source_last_click`.`google_ads_campaign`.`ad_group_name` as adGroupName,");
        sql.append("t.`session_traffic_source_last_click`.`google_ads_campaign`.`ad_group_id` as adGroupId,");
        sql.append("t.`event_name` as eventName ");
        sql.append(" FROM `");
        sql.append(projectName).append(".analytics").append("_").append(analyticsSuffix).append(".events_").append(date).append("` as t");
        sql.append(" WHERE `event_name` like \"login%succeeded\" ").append(" OR event_name = \"login_success_total\" ").append(" OR event_name = \"first_open\" ").append(" order by `event_timestamp` desc ");
        logger.info("deal firebase login success. sql={}", sql.toString());
        try {
            return projectName.equals(YOUSTAR_PROJECT_NAME) ? bigQueryHelper.queryForList(sql.toString(), LoginSuccessData.class) : proBigQueryHelper.queryForList(sql.toString(), LoginSuccessData.class);
        } catch (Exception e) {
            String key = projectName.equals(YOUSTAR_PROJECT_NAME) ? getKey() : getProKey();
            addAgainDealData(key, date);
            // // deal fire base login success data error. e=Not found: Table youstar-lite:analytics_250084930.events_20250106 was not found in location US
            logger.error("deal fire base login success data error. e={}{}", e.getMessage(), e);
            if (Integer.parseInt(DateHelper.ARABIAN.formatDateInDay2()) - Integer.parseInt(date) >= 2) {
                monitorSender.info("ustar_java_exception", "从BigQuery获取归因数据异常", "获取数据失败的日期：" + date);
            }
        }
        return null;
    }

    private void sendEventReport(ActorCampaignLogData logData) {
        ActorCampaignLogEvent event = new ActorCampaignLogEvent();
        event.setUid(logData.getUid());
        event.setGp_ad_id(logData.getGpAdId());
        event.setIdfa(logData.getIdfa());
        event.setCountry_code(logData.getCountryCode());
        event.setLanguage(logData.getLanguage());
        event.setApp_id(logData.getAppId());
        event.setApp_ver(logData.getAppVer());
        event.setAttribution_time(logData.getAttributionTime());
        event.setCampaign(logData.getCampaign());
        event.setTouch_time(logData.getTouchTime());
        event.setMedium(logData.getMedium());
        event.setSource(logData.getSource());
        event.setAd_group_name(logData.getAdGroupName());
        event.setCtime(logData.getCtime());
        event.setData_time(DateSupport.yyyyMMdd(DateSupport.ARABIAN.getLocalDate(logData.getCtime() * 1000L)));
        event.setBackendSource("bigQuery");
        logger.info("send event report. event={}", event);
        eventReport.track(new EventDTO(event));
    }

    public void againDealFirebaseData() {
        String key = getKey();
        Set<String> strDateSet = getOneAgainDealDataSet(key);
        if (!CollectionUtils.isEmpty(strDateSet)) {
            for (String strDate : strDateSet) {
                LocalDate localDate = DateSupport.BEIJING.parse_yyyyMMdd(strDate);
                removeAgainDealData(key, strDate);
                dealFirebaseLoginSuccess((int) DateSupport.ARABIAN.getTimeSeconds(localDate), YOUSTAR_PROJECT_NAME, YOUSTAR_ANALYTICS_SUFFIX);
            }
        }

        String proKey = getProKey();
        Set<String> strProDateSet = getOneAgainDealDataSet(proKey);
        if (!CollectionUtils.isEmpty(strProDateSet)) {
            for (String strDate : strProDateSet) {
                LocalDate localDate = DateSupport.BEIJING.parse_yyyyMMdd(strDate);
                removeAgainDealData(proKey, strDate);
                dealFirebaseLoginSuccess((int) DateSupport.ARABIAN.getTimeSeconds(localDate), YOUSTAR_PRO_PROJECT_NAME, YOUSTAR_PRO_ANALYTICS_SUFFIX);
            }
        }
    }

    private void addAgainDealData(String key, String date) {
        try {
            mainCluster.opsForSet().add(key, date);
            mainCluster.expire(key, ExpireTimeConstant.EXPIRE_DAYS_THIRTY, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("add again deal firebase date in redis error. date={} {}", date, e.getMessage(), e);
        }
    }

    private Set<String> getOneAgainDealDataSet(String key) {
        try {
            return mainCluster.opsForSet().members(key);
        } catch (Exception e) {
            logger.error("get again deal firebase date from redis error. {}", e.getMessage(), e);
            return null;
        }
    }

    private void removeAgainDealData(String key, String date) {
        try {
            mainCluster.opsForSet().remove(key, date);
        } catch (Exception e) {
            logger.error("remove again deal firebase date from redis error. date={} {}", date, e.getMessage(), e);
        }
    }

    private String getKey() {
        return "set:again_deal_firebase";
    }

    private String getProKey() {
        return "set:pro_again_deal_firebase";
    }
}

package com.quhong.constant;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

public class RoomListConstant {
    public static final int PARTY_ROOM = 1;
    public static final int LIVE_ROOM = 2;
    public static final int GAME_ROOM = 3;
    public static final int WATCH_ROOM = 4;

    // 1Youtube 2幸运转盘 3Ludo 4真心话大冒险 10Bumper Blaster
    public static final int YOUTUBE_TAG = 1;
    public static final int TURNTABLE_TAG = 2;
    public static final int LUDO_TAG = 3;
    public static final int TRUTH_DARE_TAG = 4;
    public static final int LIVE_ROOM_MODE_TAG = 5;
    public static final int BUMPER_BLASTER_TAG = 10;

    // forYou推荐列表房间数默认为38个房间
    public static final int FOR_YOU_SIZE = 38;
    // 847 discover chat默认为50个房间
    public static final int ACTIVE_ROOM_SIZE = 50;
    public static final int POPULAR_PAGE_SIZE = 30;
    public static final int GAME_PAGE_SIZE = 20;
    public static final int COUNTRY_PAGE_SIZE = 20;
    public static final int FOLLOW_PAGE_SIZE = 16;
    public static final int JOIN_PAGE_SIZE = 16;
    public static final int RECENTLY_PAGE_SIZE = 16;
    public static final int FOR_YOU_PAGE_SIZE = 16;
    public static final int DISCOVER_POPULAR_ROOMS_PAGE_SIZE = 18;
    public static final int GCC = 1;
    public static final int MIDDLE_EAST = 2;
    public static final int EVENT_PAGE_SIZE = 10;
    public static final int SEARCH_PAGE_SIZE = 14;
    // popular 特殊展示国家 沙特 伊拉克
    public static final Set<String> ONE_AREA_SET = new HashSet<>(Arrays.asList("sa", "iq"));
    // 海湾六国国家GCC：沙特阿拉伯、科威特、阿联酋、卡塔尔、阿曼、巴林、美国
    public static final Set<String> GCC_SET = new HashSet<>(Arrays.asList("sa", "kw", "ae", "qa", "om", "bh", "us"));
    // apple 隐藏国家：叙利亚 、俄罗斯 、朝鲜 、伊朗 、白俄罗斯、科特迪瓦、古巴 刚果民主共和国 利比里亚 津巴布韦 委内瑞拉
    public static final Set<String> APPLE_HIDE_SET = new HashSet<>(Arrays.asList("sy", "ru", "kp", "ir", "by", "kt", "cu", "cg", "lr", "zw", "ve"));
    // 海湾六国国家GCC：沙特阿拉伯、科威特、阿联酋、卡塔尔、阿曼、巴林、美国
    public static final Set<String> MAJOR_COUNTRY_SET = new HashSet<>(Arrays.asList(
            "sa", "kw", "ae", "qa", "om", "bh", // GCC
            "us", "ca", "iq", // US, Canada
            "gb", "de", "nl", "es", "it", "fr",  // European countries
            "at", "be", "bg", "hr", "cy", "cz", "dk", "ee", "fi", "gr",
            "hu", "ie", "lv", "lt", "lu", "mt", "pl", "pt", "ro", "sk",
            "si", "se"
    ));
    public static final int MAJOR_COUNTRY = 1; // 大户国家
    public static final int OTHER_COUNTRY = 2; // 非大户国家

    public static final int GCC_COUNTRY_AREA = 100; // gcc国家
    public static final int UN_GCC_COUNTRY_AREA = 101; // 非gcc大户国家

    // room devote list 拉取模式
    public static final int DAY_MODE = 1;
    public static final int WEEK_MODE = 2;

    // rank list 拉取模式
    public static final int RANK_HOUR_MODE = 4;
    public static final int RANK_DAY_MODE = 1;
    public static final int RANK_WEEK_MODE = 2;
    public static final int RANK_MONTH_MODE = 3;

    // rank list send receive room type
    public static final int USER_RANK_SEND_TYPE = 1;
    public static final int USER_RANK_RECEIVE_TYPE = 2;
    public static final int ROOM_RANK_TYPE = 3;

    // big_boss url
    public static final String DEV_BIG_BOSS_URL = "https://test2.qmovies.tv/top_up/";
    public static final String PRODUCT_BIG_BOSS_URL = "https://static.youstar.live/top_up/";
    public static final String SWITCH_BIG_BOSS_KEY = "big_boss";

    public static final int EVENT_COMING_SOON_AND_PARTYING = 2;
    public static final int EVENT_PARTYING = 1;
    public static final int EVENT_COMING_SOON = 0;

    // 0 真心话转盘 1幸运转盘 2Ludo 3Umo 4:消消乐
    public static final int GAME_TRUTH_WHEEL = 0;
    public static final int GAME_LUCKY_WHEEL = 1;
    public static final int GAME_LUDO = 2;
    public static final int GAME_UMO = 3;
    public static final int GAME_MONSTER_CRUSH = 4;

    public static final int SOCIAL_OFFICIAL_ROOM_WEIGHT = 200000; // 官方迎新房权重
    public static final int SOCIAL_NEW_ROOM_WEIGHT = 100000; // 迎新房，优质房，大R房都按这个权重
    public static final int SOCIAL_SAME_COUNTRY_WEIGHT = 80000;
    public static final int SOCIAL_SAME_AREA_WEIGHT = 40000;
    public static final int SOCIAL_ROOM_MAX_SIZE = 1000;

    public static final int TYPE_BIG_R_ROOM = 1;  // 权重的type值
    public static final int TYPE_HIGH_QUALITY_ROOM = 2; // 权重的type值
    public static final int TYPE_ROOKIE_ROOM = 3; // 权重的type值

    // 仅沙特用户可见房间(沙特运营房间) 10966
    public static final Set<String> SAUDI_ONLY_ROOM_SET = new HashSet<>(Arrays.asList("r:68c29ea1b8d8a81b66005578")); //
    public static final String SAUDI_COUNTRY = "sa";
}

package com.quhong.constant;


public class HttpCode {
    public static final HttpCode SUCCESS = new HttpCode(0, "success");
    public static final HttpCode PARAM_ERROR = new HttpCode(1, "params_error");
    public static final HttpCode SERVER_ERROR = new HttpCode(1, "server_error");

    private int code;
    private String msg;

    public HttpCode() {
    }

    public HttpCode(int code, String langMsg) {
        this.code = code;
        this.msg = langMsg;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}

package com.quhong.handler;

import com.quhong.config.ActivityCommonConfig;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.dailyTask.UserLevelTaskService;
import com.quhong.data.SendGiftData;
import com.quhong.data.UserLevelTaskData;
import com.quhong.enums.UserLevelConstant;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.mysql.dao.WhiteTestDao;
import com.quhong.service.*;
import com.quhong.utils.RoomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;

@Component
public class OtherActivityHandler extends ActivityCommonHandler implements ActivityHandler {

    private static final Logger logger = LoggerFactory.getLogger(OtherActivityHandler.class);

    @Resource
    private OtherActivityService otherActivityService;
    @Resource
    private UserLevelTaskService userLevelTaskService;
    @Resource
    private BlindBoxWeeklyService blindBoxWeeklyService;
    @Resource
    private WhiteTestDao whiteTestDao;
    @Resource
    private NewbieStarV2Service newbieStarV2Service;
    @Resource
    private TalentHostService talentHostService;
    @Resource
    private MiningService miningService;
    @Resource
    private OperationRoomWashService operationRoomWashService;
    @Resource
    private KingOfJungleService kingOfJungleService;
    @Resource
    private GloryOfCountriesService gloryOfCountriesService;
    @Resource
    private FootballActivityService footballActivityService;
    @Resource
    private GuardianService guardianService;
    @Resource
    private FalconService falconService;
    @Resource
    private ScratchLotteryService scratchLotteryService;
    @Resource
    private LuxuryCarService luxuryCarService;
    @Resource
    private DragonTrainService dragonTrainService;
    @Resource
    private Eid2025Service eid2025Service;
    @Resource
    private YourCircleGloryService yourCircleGloryService;
    @Resource
    private GiftIllustrationBookService giftIllustrationBookService;
    @Resource
    private OperationRoomCountrySelectService operationRoomCountrySelectService;
    @Resource
    private LuckyGlovesService luckyGlovesService;
    @Resource
    private AICustomizedGiftService aICustomizedGiftService;
    @Resource
    private RoomDataCenterService roomDataCenterService;
    @Resource
    private MonsterHuntingService monsterHuntingService;
    @Resource
    private MissContestV3Service missContestV3Service;
    @Resource
    private FootballCarnivalService footballCarnivalService;
    @Resource
    private PetFeedService petFeedService;
    @Resource
    private HappyTripService happyTripService;
    @Resource
    private LuckyNumberService luckyNumberService;
    @Resource
    private HappySaudiNationalService happySaudiNationalService;

    @Override
    public void process(SendGiftData giftData) {

        // 用户等级充值相关业务
        userLevelTaskService.sendTaskDataToMq(new UserLevelTaskData(giftData.getFrom_uid(), UserLevelConstant.SEND_GIFT, 1));


        if (giftData.getGift_cost_type() == 2) {
            // 金币礼物不统计
            return;
        }

        if (RoomUtils.isGameRoom(giftData.getRoomId())) {
            // 游戏房礼物不统计
            return;
        }
        // 当前进行的活动
        for (OtherRankingActivityData activity : otherActivityService.getOtherRankingActivities()) {

            // 总礼物配置，默认情况下礼物排行榜都使用此列表的礼物进行钻石数量统计，活动的礼物都在此列表内
            String activityId = activity.get_id().toString();
            int curTime = DateHelper.getNowSeconds();
            int giftId = giftData.getGid();
            int startTime = activity.getStartTime();
            int endTime = activity.getEndTime();
            int roundNum = activity.getRoundNum();
            int dailyNum = activity.getDailyNum();
            List<Integer> giftIds = activity.getActivityGiftList();
            String activityName = activity.getAcNameEn();

            if (ServerConfig.isProduct()) {
                // 灰度时只统计灰度房间的礼物
                if (activity.getAcNameEn().startsWith("test") && !StringUtils.isEmpty(giftData.getRoomId()) && !whiteTestDao.isMemberByType(giftData.getRoomId(), WhiteTestDao.WHITE_TYPE_ROOM_ID)) {
                    continue;
                }

                if (activity.getAcNameEn().startsWith("test") && StringUtils.isEmpty(giftData.getRoomId())) {
                    continue;
                }
            }
            if (giftIds.contains(giftId) && (curTime >= startTime && curTime < endTime)) {

                logger.info("ActivityName: {}, from_uid:{}, room_id: {}", activityName, giftData.getFrom_uid(), giftData.getRoomId());
                doOtherRankingDataCollect(giftData, activity);
                doOtherLevelDataCollect(giftData, activity);

                // 盲盒周礼物
                if (activity.getAcNameEn().contains("Blind Box")) {
                    blindBoxWeeklyService.handleBlindBoxGift(giftData, activityId, activity.getRoundNum());
                }

                if (NewbieStarV2Service.ACTIVITY_ID.equals(activityId)) {
                    newbieStarV2Service.sendGiftHandle(giftData, activityId);
                }

                if (MiningService.ACTIVITY_ID.equals(activityId)) {
                    miningService.sendGiftHandle(giftData, activityId);
                }

                // if (GloryOfCountriesService.ACTIVITY_ID.equals(activityId)) {
                //     gloryOfCountriesService.sendGiftHandle(giftData, activityId);
                // }

                if (LuckyGlovesService.ACTIVITY_ID.equals(activityId)) {
                    luckyGlovesService.sendGiftHandle(giftData, activityId);
                }
                // if (MonsterHuntingService.ACTIVITY_ID.equals(activityId)) {
                //     monsterHuntingService.sendGiftHandle(giftData, activityId);
                // }

                // if (MissContestV3Service.QUEEN_ACTIVITY_ID_LIST.contains(activityId)) {
                //     missContestV3Service.handleSendGift(giftData, activity);
                // }
                // if (FootballCarnivalService.ACTIVITY_ID.equals(activityId)) {
                //     footballCarnivalService.sendGiftHandle(giftData, activityId);
                // }
                if (HappyTripService.ACTIVITY_ID.equals(activityId)) {
                    happyTripService.sendGiftHandle(giftData, activityId);
                }
                if (LuckyNumberService.ACTIVITY_ID.equals(activityId)) {
                    luckyNumberService.sendGiftHandle(giftData, activityId);
                }
                if (HappySaudiNationalService.ACTIVITY_ID.equals(activityId)) {
                    happySaudiNationalService.sendGiftHandle(giftData, activityId);
                }

            }

            // 统计所有礼物的统一入口
            if (giftIds.contains(-1) && (curTime >= startTime && curTime < endTime)) {
                doOtherRankingDataCollect(giftData, activity);
                doOtherLevelDataCollect(giftData, activity);

                if (OperationRoomWashService.ACTIVITY_ID.equals(activityId)) {
                    operationRoomWashService.sendGiftHandle(giftData, activityId);
                }
                if (OperationRoomCountrySelectService.ACTIVITY_ID.equals(activityId)) {
                    operationRoomCountrySelectService.sendGiftHandle(giftData, activityId);
                }
                if (AICustomizedGiftService.ACTIVITY_ID.equals(activityId)) {
                    aICustomizedGiftService.sendGiftHandle(giftData, activityId);
                }
                if (RoomDataCenterService.ACTIVITY_ID.equals(activityId)) {
                    roomDataCenterService.sendGiftHandle(giftData, activityId);
                }
                if (PetFeedService.ACTIVITY_ID.equals(activityId)) {
                    petFeedService.sendGiftHandle(giftData, activityId);
                }

            }

            // 运营房统计所有礼物数据
            // if(activityName.contains("Activity Room Competition") && (curTime >= startTime && curTime < endTime)
            //         && !StringUtils.isEmpty(giftData.getRoomId()) && activityCommonConfig.getActivityRoomList().contains(giftData.getRoomId())){
            //     logger.info("ActivityName: {}, from_uid:{}, room_id: {}", activityName, giftData.getFrom_uid(), giftData.getRoomId());
            //     doOtherRankingDataCollect(giftData, activity);
            // }


            //  运营房选拔赛
            // if(activityName.contains("Select Competition") && (curTime >= startTime && curTime < endTime)
            //         && !StringUtils.isEmpty(giftData.getRoomId()) && !activityCommonConfig.getActivityRoomList().contains(giftData.getRoomId())){
            //     logger.info("ActivityName: {}, from_uid:{}, room_id: {}", activityName, giftData.getFrom_uid(), giftData.getRoomId());
            //     doOtherRankingDataCollect(giftData, activity);
            // }

        }

//         miniTaskService.handleGiftMqMsg(giftData);


        // 射门赢大奖
        // shootActivityService.shootGiftHandle(giftData);
        // if (activityCommonConfig.getShootActivityName().contains("test") && !StringUtils.isEmpty(giftData.getRoomId()) && testRoomService.isTestRoom(giftData.getRoomId())) {
        //     shootActivityService.shootGiftHandle(giftData);
        // }else if(!activityCommonConfig.getShootActivityName().contains("test")) {
        //     shootActivityService.shootGiftHandle(giftData);
        // }

        // lionActivityService.lionGiftHandle(giftData);


    }
}

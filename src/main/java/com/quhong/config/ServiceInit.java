package com.quhong.config;

import com.quhong.api.ConfigCenterApiService;
import com.quhong.api.UserInfoApiService;
import com.quhong.constant.ServiceConstant;
import com.quhong.init.DubboServiceInit;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

@Component
public class ServiceInit implements DubboServiceInit {

    @DubboReference(lazy = true, providedBy = ServiceConstant.USER_INFO, connections = 2, reconnect = "false", retries = 0)
    private UserInfoApiService userInfoApiService;

    @DubboReference(lazy = true, providedBy = ServiceConstant.CONFIG_CENTER, connections = 2, reconnect = "false", retries = 0)
    private ConfigCenterApiService configCenterApiService;
}

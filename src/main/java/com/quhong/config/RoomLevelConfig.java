package com.quhong.config;

import com.quhong.data.ResourceConfigData;
import com.quhong.data.UpdateLevelData;
import com.quhong.mongo.data.BadgeListData;
import org.springframework.beans.BeanUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;


@Component
@PropertySource(value = "classpath:room_level_exp.yml", encoding = "UTF-8", factory = YamlPropertySourceFactory.class)
@ConfigurationProperties(prefix = "level")
public class RoomLevelConfig {

    /**
     * 根据等级升序排序
     */
    private static final Comparator<ResourceConfigData> LEVEL_ASC = Comparator.comparing(ResourceConfigData::getStepLevel);

    // 房间200级以内的经验值
    private List<Long> expList;
    // 房间等级英语H5明细页面
    private List<UpdateLevelData> updateLevelMapData;
    // 房间等级阿语H5明细页面
    private List<UpdateLevelData> updateLevelMapDataAr;
    // 房间等级奖励下发
    private Map<Integer, ResourceConfigData> levelRewards;

    public List<Long> getExpList() {
        return new ArrayList<>(expList);
    }

    public List<Long> getUnsafeExpList() {
        return expList;
    }

    public void setExpList(List<Long> expList) {
        this.expList = expList;
    }

    public List<UpdateLevelData> getUpdateLevelMapData() {
        List<UpdateLevelData> list = new ArrayList<>();
        for (UpdateLevelData source : updateLevelMapData) {
            UpdateLevelData target = new UpdateLevelData();
            BeanUtils.copyProperties(source, target);
            list.add(target);
        }
        return list;
    }

    public void setUpdateLevelMapData(List<UpdateLevelData> updateLevelMapData) {
        this.updateLevelMapData = updateLevelMapData;
    }

    public List<UpdateLevelData> getUpdateLevelMapDataAr() {
        List<UpdateLevelData> list = new ArrayList<>();
        for (UpdateLevelData source : updateLevelMapDataAr) {
            UpdateLevelData target = new UpdateLevelData();
            BeanUtils.copyProperties(source, target);
            list.add(target);
        }
        return list;
    }

    public void setUpdateLevelMapDataAr(List<UpdateLevelData> updateLevelMapDataAr) {
        this.updateLevelMapDataAr = updateLevelMapDataAr;
    }


    public Map<Integer, ResourceConfigData> getLevelRewards() {
        return levelRewards;
    }

    public List<ResourceConfigData> getLevelRewardsList() {
        List<ResourceConfigData> dataList = new ArrayList<>();
        for (Map.Entry<Integer, ResourceConfigData> entry : levelRewards.entrySet()) {
            ResourceConfigData data = entry.getValue();
            ResourceConfigData configData = new ResourceConfigData();
            configData.setTitle(data.getTitle());
            configData.setExp(data.getExp());
            configData.setStepLevel(entry.getKey());
            dataList.add(configData);
        }
        dataList.sort(LEVEL_ASC);
        return dataList;
    }

    public void setLevelRewards(Map<Integer, ResourceConfigData> levelRewards) {
        this.levelRewards = levelRewards;
    }
}

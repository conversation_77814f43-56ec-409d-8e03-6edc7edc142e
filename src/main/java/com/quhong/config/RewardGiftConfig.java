package com.quhong.config;

import com.quhong.data.GiftData;
import com.quhong.utils.CollectionUtil;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;


@Component
@PropertySource(value = "classpath:moment.yml", encoding = "UTF-8", factory = YamlPropertySourceFactory.class)
@ConfigurationProperties(prefix = "reward-gift")
public class RewardGiftConfig {

    private List<GiftData> rewardGiftList;
    private Map<Integer, GiftData> rewardGIftMap;

    public String getRewardGiftIcon(int index) {
        if (CollectionUtils.isEmpty(rewardGiftList)) {
            return null;
        }
        return rewardGiftList.get(index >= rewardGiftList.size() ? 0 : index).getGiftIcon();
    }

    public List<GiftData> getRewardGiftList() {
        return rewardGiftList;
    }

    public void setRewardGiftList(List<GiftData> rewardGiftList) {
        this.rewardGiftList = rewardGiftList;
        this.rewardGIftMap = CollectionUtil.listToKeyMap(rewardGiftList, GiftData::getGiftId);
    }

    public Map<Integer, GiftData> getRewardGIftMap() {
        return rewardGIftMap;
    }
}

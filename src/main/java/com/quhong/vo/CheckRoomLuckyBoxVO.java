package com.quhong.vo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/2
 */
public class CheckRoomLuckyBoxVO {

    private List<BoxInfo> list;

    private List<BoxInfo> gift_box_list;

    public CheckRoomLuckyBoxVO() {
    }

    public CheckRoomLuckyBoxVO(List<BoxInfo> list, List<BoxInfo> gift_box_list) {
        this.list = list;
        this.gift_box_list = gift_box_list;
    }

    public static class BoxInfo {
        private String box_id;
        private int num;
        private String msg;
        private String uid;
        private String name;
        private String head;
        private int viplevel;
        private int ulvl;
        private int ctime;
        private int end_time;
        private int box_type;

        public BoxInfo() {
        }

        public BoxInfo(String box_id, int num, String msg, String uid, String name, String head, int viplevel, int ulvl, int ctime, int end_time, int box_type) {
            this.box_id = box_id;
            this.num = num;
            this.msg = msg;
            this.uid = uid;
            this.name = name;
            this.head = head;
            this.viplevel = viplevel;
            this.ulvl = ulvl;
            this.ctime = ctime;
            this.end_time = end_time;
            this.box_type = box_type;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getHead() {
            return head;
        }

        public void setHead(String head) {
            this.head = head;
        }

        public int getViplevel() {
            return viplevel;
        }

        public void setViplevel(int viplevel) {
            this.viplevel = viplevel;
        }

        public int getUlvl() {
            return ulvl;
        }

        public void setUlvl(int ulvl) {
            this.ulvl = ulvl;
        }

        public String getBox_id() {
            return box_id;
        }

        public void setBox_id(String box_id) {
            this.box_id = box_id;
        }

        public String getUid() {
            return uid;
        }

        public void setUid(String uid) {
            this.uid = uid;
        }

        public int getNum() {
            return num;
        }

        public void setNum(int num) {
            this.num = num;
        }

        public String getMsg() {
            return msg;
        }

        public void setMsg(String msg) {
            this.msg = msg;
        }

        public int getCtime() {
            return ctime;
        }

        public void setCtime(int ctime) {
            this.ctime = ctime;
        }

        public int getEnd_time() {
            return end_time;
        }

        public void setEnd_time(int end_time) {
            this.end_time = end_time;
        }

        public int getBox_type() {
            return box_type;
        }

        public void setBox_type(int box_type) {
            this.box_type = box_type;
        }
    }

    public List<BoxInfo> getList() {
        return list;
    }

    public void setList(List<BoxInfo> list) {
        this.list = list;
    }

    public List<BoxInfo> getGift_box_list() {
        return gift_box_list;
    }

    public void setGift_box_list(List<BoxInfo> gift_box_list) {
        this.gift_box_list = gift_box_list;
    }
}

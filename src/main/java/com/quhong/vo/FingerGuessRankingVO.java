package com.quhong.vo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/7
 */
public class FingerGuessRankingVO {


    private Integer myRank;

    private String myName;

    private String myHead;

    private Integer myGiftNum;

    private List<Actor> list;

    public static class Actor {

        private Integer rank;

        private String name;

        private String head;

        private Integer giftNum;

        public Actor() {
        }

        public Actor(Integer rank, String name, String head, Integer giftNum) {
            this.rank = rank;
            this.name = name;
            this.head = head;
            this.giftNum = giftNum;
        }

        public Integer getRank() {
            return rank;
        }

        public void setRank(Integer rank) {
            this.rank = rank;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getHead() {
            return head;
        }

        public void setHead(String head) {
            this.head = head;
        }

        public Integer getGiftNum() {
            return giftNum;
        }

        public void setGiftNum(Integer giftNum) {
            this.giftNum = giftNum;
        }
    }

    public List<Actor> getList() {
        return list;
    }

    public void setList(List<Actor> list) {
        this.list = list;
    }

    public Integer getMyRank() {
        return myRank;
    }

    public void setMyRank(Integer myRank) {
        this.myRank = myRank;
    }

    public String getMyName() {
        return myName;
    }

    public void setMyName(String myName) {
        this.myName = myName;
    }

    public String getMyHead() {
        return myHead;
    }

    public void setMyHead(String myHead) {
        this.myHead = myHead;
    }

    public Integer getMyGiftNum() {
        return myGiftNum;
    }

    public void setMyGiftNum(Integer myGiftNum) {
        this.myGiftNum = myGiftNum;
    }
}

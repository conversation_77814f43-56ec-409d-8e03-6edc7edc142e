package com.quhong;

import com.quhong.core.utils.DateHelper;
import org.springframework.web.util.UriComponentsBuilder;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.WeekFields;
import java.time.zone.ZoneRules;
import java.util.*;
import java.util.stream.LongStream;
import java.util.stream.Stream;


public class DateSupport {
    private static final int SECONDS_PER_DAY = 86400;

    public static final DateSupport UTC = new DateSupport(ZoneOffset.UTC);
    public static final DateSupport ARABIAN = new DateSupport(ZoneOffset.of("+3"));
    public static final DateSupport BEIJING = new DateSupport(ZoneOffset.of("+8"));

    private static DateTimeFormatter yyyy_mm_dd = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static DateTimeFormatter yyyy_mm = DateTimeFormatter.ofPattern("yyyy_MM");
    private static DateTimeFormatter yyyyMMdd = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static DateTimeFormatter yyyy_m_d = DateTimeFormatter.ofPattern("yyyy-M-d");
    private static DateTimeFormatter dm = DateTimeFormatter.ofPattern("d/M");

    public String yyyyMMdd() {
        return getToday().format(yyyyMMdd);
    }

    public static String yyyyMMdd(LocalDate localDate) {
        return localDate.format(yyyyMMdd);
    }

    public LocalDate parse_yyyyMMdd(String date) {
        return LocalDate.parse(date, yyyyMMdd);
    }

    public String dm(LocalDate localDate) {
        return localDate.format(dm);
    }

    /**
     * 解析，默认格式 yyyy-MM-dd
     *
     * @param date
     * @return
     */
    public static LocalDate parse(String date) {
        return LocalDate.parse(date, yyyy_mm_dd);
    }

    /**
     * 解析，yyyy-M-d
     *
     * @param date
     * @return
     */
    public static LocalDate parseYyyymd(String date) {
        return LocalDate.parse(date, yyyy_m_d);
    }

    /**
     * 格式化，默认格式 yyyy-MM-dd
     *
     * @param localDate
     * @return
     */
    public static String format(LocalDate localDate) {
        return localDate.format(yyyy_mm_dd);
    }

    /**
     * 解析 格式 yyyy_MM 用户分表
     *
     * @param date
     * @return
     */
    public static LocalDate parseYYYY_MM(String date) {
        return LocalDate.parse(date, yyyy_mm);
    }

    /**
     * 格式化 yyyy_MM
     *
     * @param localDate
     * @return
     */
    public static String formatYYYY_MM(LocalDate localDate) {
        return localDate.format(yyyy_mm);
    }

    public static Period getPeriod(LocalDate start, LocalDate end) {
        return Period.between(start, end);
    }

    /**
     * 如果start < end
     * duration。toDays 为负数
     *
     * @param start
     * @param end
     * @return
     */
    public static Duration getDuration(LocalDate start, LocalDate end) {
        return Duration.between(toLocalDateTime(start), toLocalDateTime(end));
    }

    /**
     * 转换成lcoalDateTime
     *
     * @param localDate
     * @return
     */
    public static LocalDateTime toLocalDateTime(LocalDate localDate) {
        return toLocalDateTime(localDate, LocalTime.of(0, 0));
    }

    public static LocalDateTime toLocalDateTime(LocalDate localDate, LocalTime localTime) {
        return LocalDateTime.of(localDate, localTime);
    }

    /**
     * 获取连续的日期stream
     *
     * @param start
     * @param endExclude 不包含end时间
     * @return
     */
    public static Stream<LocalDate> datesUntil(LocalDate start, LocalDate endExclude) {
        if (start.compareTo(endExclude) < 0) {
            return doDatesUntil(start, endExclude);
        } else {
            return doDatesUntil(endExclude, start);
        }

    }

    /**
     * java9之后有内置方法
     *
     * @param startDate
     * @param endExclusive
     * @return
     */
    public static Stream<LocalDate> doDatesUntil(LocalDate startDate, LocalDate endExclusive) {
        long end = endExclusive.toEpochDay();
        long start = startDate.toEpochDay();
        if (end < start) {
            throw new IllegalArgumentException(endExclusive + " < " + startDate);
        }
        return LongStream.range(start, end).mapToObj(LocalDate::ofEpochDay);
    }

    private ZoneOffset zoneOffset;

    public DateSupport(ZoneOffset zoneOffset) {
        this.zoneOffset = zoneOffset;
    }

    public LocalDate getLocalDate(Date date) {
        return ofInstant(date.toInstant(), zoneOffset);
    }


    /**
     * 获取LcoalDate
     *
     * @param epochMilli
     * @return
     */
    public LocalDate getLocalDate(long epochMilli) {
        Instant instant = Instant.ofEpochMilli(epochMilli);
        return ofInstant(instant, zoneOffset);
    }

    /**
     * java9 有本地方法
     *
     * @param instant
     * @param zone
     * @return
     */
    private LocalDate ofInstant(Instant instant, ZoneId zone) {
        Objects.requireNonNull(instant, "instant");
        Objects.requireNonNull(zone, "zone");
        ZoneRules rules = zone.getRules();
        ZoneOffset offset = rules.getOffset(instant);
        long localSecond = instant.getEpochSecond() + offset.getTotalSeconds();
        long localEpochDay = Math.floorDiv(localSecond, SECONDS_PER_DAY);
        return LocalDate.ofEpochDay(localEpochDay);
    }

    /**
     * 获取开始lcoalData
     *
     * @return
     */
    public LocalDate getToday() {
        return LocalDate.now(zoneOffset);
    }

    /**
     * 获取昨天开始date
     *
     * @return
     */
    public LocalDate getYesterday() {
        return getDayOffset(-1);
    }

    public LocalDate getDayOffset(int offset) {
        return getToday().plusDays(offset);
    }

    public long getTodayStartTime() {
        return getTimeSeconds(getToday());
    }

    public long getTimeSeconds(LocalDate localDate) {
        return getTimeSeconds(localDate, LocalTime.of(0, 0));
    }

    public long getTimeMillis(LocalDate localDate) {
        return getTimeSeconds(localDate, LocalTime.of(0, 0)) * 1000;
    }

    public Date getDate(LocalDate localDate) {
        return new Date(getTimeMillis(localDate));
    }

    /**
     * java9之后有内置方法
     *
     * @param localDate
     * @param localTime
     * @return
     */
    public long getTimeSeconds(LocalDate localDate, LocalTime localTime) {
        return toLocalDateTime(localDate, localTime).toEpochSecond(zoneOffset);
    }

    /**
     * 获取当前周
     *
     * @return 格式：2024-W01  2024年第一周
     * 注：不会有跨年问题，2024-12-31为2025-W01
     */
    public String getStrCurrentWeek() {
        LocalDate date = LocalDate.now(zoneOffset);
        // 使用美国的习惯（周日作为一周的开始）
        WeekFields weekFields = WeekFields.of(Locale.US);
        // 获取年份和周数
        int weekYear = date.get(weekFields.weekBasedYear());
        int weekOfYear = date.get(weekFields.weekOfWeekBasedYear());
        return weekYear + "-W" + String.format("%02d", weekOfYear);
    }

    public List<String> getDaysBetween(int startTime, int endTime) {
        LocalDate startDate = getLocalDate(startTime * 1000L);
        LocalDate endDate = getLocalDate(endTime * 1000L);
        List<String> strDateList = new ArrayList<>();
        while (!startDate.isAfter(endDate)) {
            strDateList.add(DateSupport.format(startDate));
            startDate = startDate.plusDays(1);
        }
        return strDateList;
    }

    private static String todayMinusDays(int days) {
        LocalDate nowDate = com.quhong.core.date.DateSupport.ARABIAN.getToday();
        LocalDate oldDate = nowDate.minusDays(days);
        // 获取字符格式 yyyy-MM-dd
        return format(oldDate);
    }


    public static void difference() {
        Set<Integer> set52 = new HashSet<>(Arrays.asList(147, 150, 149, 151, 152, 229, 153, 292, 154, 155, 221, 225, 156, 186, 157, 649, 223, 184, 158, 293, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 227, 169, 228, 170, 219, 172, 173, 174, 175, 176, 177, 178, 179, 222, 226, 180, 181, 182, 183, 652, 654, 655
        ));
        // 假设这里已经用你提供的数字填充了set1
        // ...（填充set1的代码省略，因为数字太多且已知两个集合相同）

        Set<Integer> set56 = new HashSet<>(Arrays.asList(147, 148, 149, 150, 151, 152, 229, 153, 292, 154, 155, 221, 225, 156, 186, 224, 157, 223, 184, 158, 293, 159, 187, 160, 161, 162, 163, 164, 165, 166, 167, 168, 227, 169, 228, 170, 219, 171, 172, 173, 174, 175, 176, 177, 178, 179, 222, 226, 180, 181, 182, 183, 649, 652, 654, 655
        ));
        // 假设这里已经用你提供的数字填充了set2（实际上与set1相同）
        // ...（填充set2的代码也省略）

        System.out.println("set52:" + set52.size() + " set56:" + set56.size());
        // 检查两个集合是否相同
        if (set52.equals(set56)) {
            System.out.println("集合1和集合2是相同的。");
        } else {
            // 找出差异（但在这个例子中不会执行）
            Set<Integer> difference1 = new HashSet<>(set52);
            difference1.removeAll(set56);
            System.out.println("在set52不在set56的数字: " + difference1);

            Set<Integer> difference2 = new HashSet<>(set56);
            difference2.removeAll(set52);
            System.out.println("在set56不在set52的数字: " + difference2);
        }
    }

    public static long calculateDaysBetween(String startDateStr, String endDateStr) {
        // 使用默认的日期格式 yyyy-MM-dd 解析日期字符串
        LocalDate startDate = LocalDate.parse(startDateStr, yyyy_mm_dd);
        LocalDate endDate = LocalDate.parse(endDateStr, yyyy_mm_dd);

        // 计算两个日期之间的天数差
        long daysBetween = ChronoUnit.DAYS.between(startDate, endDate);
        System.out.println("startDateStr:" + startDateStr + " endDateStr:" + endDateStr +
                " daysBetween:" + daysBetween);
        return daysBetween;
    }

    private static int getPassionTodayMax(int startTime, int endTime) {
        int max = 2500;
        int day1 = 800;
        int day2 = 700;
        String before = DateHelper.ARABIAN.formatDateInDay
                (new Date(startTime * 1000L));
        String after = DateHelper.ARABIAN.formatDateInDay
                (new Date(endTime * 1000L));
        int d = (int) (calculateDaysBetween(before, after) + 1);
        if (d <= 0) {
            return 0;
        }
        int div = (max + day1 - 1) / day1; // 向上取整
        int ret = 0;
        if (d <= div) {
            ret = day1 * d;
        } else {
            ret = day1 * div + day2 * (d - div);
        }
        System.out.println(" div:" + div + " d:" + d +
                " ret:" + ret);
        return ret;
    }

    private static String getShareActivityUrl(String url, int shareId, String activityId) {
        if (org.springframework.util.StringUtils.isEmpty(url)) {
            return "";
        }
        UriComponentsBuilder urlBuilder = UriComponentsBuilder.fromHttpUrl(url);
//        urlBuilder.queryParam("shareId", shareId);
//        if (!org.springframework.util.StringUtils.isEmpty(activityId)) {
//            urlBuilder.queryParam("activityId", activityId);
//        }
        urlBuilder.replaceQueryParam("shareId", shareId);
        urlBuilder.replaceQueryParam("activityId", activityId);
        String toUrl = urlBuilder.build(false).encode().toUriString();
        System.out.println("toUrl:" + toUrl);
        return toUrl;
    }

    public static void getHitMatchText(String riskdetail) {
        System.out.println(riskdetail.startsWith("test"));
        Map<String, List<List<String>>> MATCH_IMAGE_TEXT_MAP = new HashMap<String, List<List<String>>>() {
            {
                put("SnapChat", Arrays.asList(Arrays.asList("youstar.official", "Following"), Arrays.asList("youstar.official", "تمت الإضافة"))); //
                put("InStagram", Arrays.asList(Arrays.asList("youstar.official", "Follo..."), Arrays.asList("youstar.official", "أتابع"))); //
                put("TikTok", Arrays.asList(Arrays.asList("youstar.official", "Message"), Arrays.asList("youstar.official", "مراسلة"))); //
            }
        };

        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, List<List<String>>> entry : MATCH_IMAGE_TEXT_MAP.entrySet()) {
            for (List<String> itemList : entry.getValue()) {
                if (itemList == null || itemList.isEmpty()) {
                    continue;
                }
                // 使用 Set 提高查找效率
                Set<String> itemSet = new HashSet<>(itemList);
                int size = itemSet.size();
//                String info = String.format("key:%s--->size:%s itemSet:%s", entry.getKey(), size, itemSet);
//                System.out.println(info);
                int count = 0;
                for (String item : itemSet) {
                    // 这里对大小写敏感，因为这里有英语，阿语混排

                    if (riskdetail.contains(item)) {
                        count++;
                    }
                    String info2 = String.format("key:%s--->item:%s count:%s", entry.getKey(), item, count);
                    System.out.println(info2);
                    if (count == size) {
                        sb.append(entry.getKey()).append(",");
                    }
                }
            }
        }
        String str = sb.toString();
        System.out.println("ret:" + str);
    }

    public static void main(String[] args) {
              getHitMatchText("testdds");
//        getShareActivityUrl("https://static.youstar.live/national_2025/?shareId=11&activityId=aaa"
//                , 22, "bbb");
//        getPassionTodayMax(1739307600,1739826000);
//        calculateDaysBetween("2024-12-31","2025-01-02");
//        String day1 = todayMinusDays(0);
//        String day2 = todayMinusDays(-1);
//        System.out.println("day1:" + day1 + " day2:" + day2);
//        difference();
    }
}

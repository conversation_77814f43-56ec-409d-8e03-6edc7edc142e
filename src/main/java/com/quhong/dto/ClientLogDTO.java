package com.quhong.dto;

import com.quhong.handler.HttpEnvData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class ClientLogDTO extends HttpEnvData {
    private static final Logger logger = LoggerFactory.getLogger(ClientLogDTO.class);

    private String logPath; // 日志路径

    public String getLogPath() {
        return logPath;
    }

    public void setLogPath(String logPath) {
        this.logPath = logPath;
    }
}

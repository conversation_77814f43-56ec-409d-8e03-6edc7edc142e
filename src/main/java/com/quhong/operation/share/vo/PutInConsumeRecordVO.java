package com.quhong.operation.share.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;

import java.util.Set;

/**
 * 投放消耗
 */
public class PutInConsumeRecordVO {

    // 自增id
    @ExcelProperty("ID")
    private Long id;
    // 推广渠道
    @ExcelProperty("渠道")
    private String medium;
    // 广告系列名称
    @ExcelProperty("广告系列名称")
    private String campaign;
    // 花费金额
    @ExcelProperty("花费金额")
    private Float adCostMoney;
    // 推广包的系统
    @ExcelProperty("包系统")
    private String pkgOs;
    // 创建时间
    @ExcelIgnore
    private Integer ctime;
    // 更新时间
    @ExcelIgnore
    private Integer mtime;
    // 日期
    @ExcelProperty("日期")
    private String date; // yyyy-MM-dd
    // 创建时间字符串
    @ExcelProperty("创建时间")
    private String ctimeStr;
    // 更新时间字符串
    @ExcelProperty("更新时间")
    private String mtimeStr;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getMedium() {
        return medium;
    }

    public void setMedium(String medium) {
        this.medium = medium;
    }

    public String getCampaign() {
        return campaign;
    }

    public void setCampaign(String campaign) {
        this.campaign = campaign;
    }

    public Float getAdCostMoney() {
        return adCostMoney;
    }

    public void setAdCostMoney(Float adCostMoney) {
        this.adCostMoney = adCostMoney;
    }

    public String getPkgOs() {
        return pkgOs;
    }

    public void setPkgOs(String pkgOs) {
        this.pkgOs = pkgOs;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }

    public Integer getMtime() {
        return mtime;
    }

    public void setMtime(Integer mtime) {
        this.mtime = mtime;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public String getCtimeStr() {
        return ctimeStr;
    }

    public void setCtimeStr(String ctimeStr) {
        this.ctimeStr = ctimeStr;
    }

    public String getMtimeStr() {
        return mtimeStr;
    }

    public void setMtimeStr(String mtimeStr) {
        this.mtimeStr = mtimeStr;
    }
}
package com.quhong.operation.share.vo;

import java.util.List;

public class HuaWeiCheckVO {

    private Integer huaweiCheckVersion; // 当前版本号以上的（含）不做限制
    private int huaweiCheckSwitch; // 1 开启 0 关闭
    private List<HuaWeiSwitchLog> logList; // 开关日志


    public static class HuaWeiSwitchLog {
        private String opUid; // 操作人uid
        private String opName; // 操作人名称
        private int huaweiCheckSwitch;
        private int huaweiCheckVersion;
        private int cTime;

        public String getOpUid() {
            return opUid;
        }

        public void setOpUid(String opUid) {
            this.opUid = opUid;
        }

        public String getOpName() {
            return opName;
        }

        public void setOpName(String opName) {
            this.opName = opName;
        }

        public int getHuaweiCheckSwitch() {
            return huaweiCheckSwitch;
        }

        public void setHuaweiCheckSwitch(int huaweiCheckSwitch) {
            this.huaweiCheckSwitch = huaweiCheckSwitch;
        }

        public int getHuaweiCheckVersion() {
            return huaweiCheckVersion;
        }

        public void setHuaweiCheckVersion(int huaweiCheckVersion) {
            this.huaweiCheckVersion = huaweiCheckVersion;
        }

        public int getcTime() {
            return cTime;
        }

        public void setcTime(int cTime) {
            this.cTime = cTime;
        }
    }

    public Integer getHuaweiCheckVersion() {
        return huaweiCheckVersion;
    }

    public void setHuaweiCheckVersion(Integer huaweiCheckVersion) {
        this.huaweiCheckVersion = huaweiCheckVersion;
    }

    public int getHuaweiCheckSwitch() {
        return huaweiCheckSwitch;
    }

    public void setHuaweiCheckSwitch(int huaweiCheckSwitch) {
        this.huaweiCheckSwitch = huaweiCheckSwitch;
    }

    public List<HuaWeiSwitchLog> getLogList() {
        return logList;
    }

    public void setLogList(List<HuaWeiSwitchLog> logList) {
        this.logList = logList;
    }
}

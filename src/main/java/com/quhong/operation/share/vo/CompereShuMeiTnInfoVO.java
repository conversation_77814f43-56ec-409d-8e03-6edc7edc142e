package com.quhong.operation.share.vo;

import com.alibaba.excel.annotation.ExcelProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2020/8/3
 */
public class CompereShuMeiTnInfoVO implements Serializable {

    private static final long serialVersionUID = 5072617902694676378L;
    @ExcelProperty("表主键id-反查用")
    private String id;
    @ExcelProperty("数美请求id-反查用")
    private String shuMeiReqId;
    @ExcelProperty("数美id")
    private String shuMeiId;
    @ExcelProperty("图灵id")
    private String tnId;
    @ExcelProperty("数美实时风险标签")
    private String shuMeiRisk;
    @ExcelProperty("图灵顿风险标签")
    private String tnRisk;
    @ExcelProperty("记录时间戳")
    private Integer ctime;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getShuMeiReqId() {
        return shuMeiReqId;
    }

    public void setShuMeiReqId(String shuMeiReqId) {
        this.shuMeiReqId = shuMeiReqId;
    }

    public String getShuMeiRisk() {
        return shuMeiRisk;
    }

    public void setShuMeiRisk(String shuMeiRisk) {
        this.shuMeiRisk = shuMeiRisk;
    }

    public String getTnRisk() {
        return tnRisk;
    }

    public void setTnRisk(String tnRisk) {
        this.tnRisk = tnRisk;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }

    public String getShuMeiId() {
        return shuMeiId;
    }

    public void setShuMeiId(String shuMeiId) {
        this.shuMeiId = shuMeiId;
    }

    public String getTnId() {
        return tnId;
    }

    public void setTnId(String tnId) {
        this.tnId = tnId;
    }


    @Override
    public String toString() {
        final StringBuffer sb = new StringBuffer("CompereShuMeiTnInfoVO{");
        sb.append("id='").append(id).append('\'');
        sb.append(", shuMeiReqId='").append(shuMeiReqId).append('\'');
        sb.append(", shuMeiRisk='").append(shuMeiRisk).append('\'');
        sb.append(", tnRisk='").append(tnRisk).append('\'');
        sb.append(", ctime=").append(ctime);
        sb.append('}');
        return sb.toString();
    }
}

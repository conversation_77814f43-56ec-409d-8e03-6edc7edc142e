package com.quhong.operation.share.dto;


import com.quhong.mongo.data.AppThemeData;

import java.util.List;

public class AppThemeDTO {
    private String docId;
    private String themeName; // 主题名称
    private String iosZipUrl; // ios主题url
    private String iosZipMd5; // 包MD5
    private String iosXZipUrl; // ios-x主题url
    private String iosXZipMd5; // 包MD5
    private String androidZipUrl; // android主题url
    private String androidZipMd5; // 包MD5

    private int zipTime; // theme生成时间
    private List<AppThemeData.ThemeConfig> themeConfigList; // 进阶配置
    private int startTime; // 开始生效时间
    private int endTime;   // 结束生效时间
    private int status; // 是否有效  1: 有效
    private Integer ctime; // 创建时间

    public String getDocId() {
        return docId;
    }

    public void setDocId(String docId) {
        this.docId = docId;
    }

    public String getThemeName() {
        return themeName;
    }

    public void setThemeName(String themeName) {
        this.themeName = themeName;
    }

    public String getIosZipUrl() {
        return iosZipUrl;
    }

    public void setIosZipUrl(String iosZipUrl) {
        this.iosZipUrl = iosZipUrl;
    }

    public String getIosXZipUrl() {
        return iosXZipUrl;
    }

    public void setIosXZipUrl(String iosXZipUrl) {
        this.iosXZipUrl = iosXZipUrl;
    }

    public String getAndroidZipUrl() {
        return androidZipUrl;
    }

    public void setAndroidZipUrl(String androidZipUrl) {
        this.androidZipUrl = androidZipUrl;
    }

    public int getZipTime() {
        return zipTime;
    }

    public void setZipTime(int zipTime) {
        this.zipTime = zipTime;
    }

    public List<AppThemeData.ThemeConfig> getThemeConfigList() {
        return themeConfigList;
    }

    public void setThemeConfigList(List<AppThemeData.ThemeConfig> themeConfigList) {
        this.themeConfigList = themeConfigList;
    }

    public int getStartTime() {
        return startTime;
    }

    public void setStartTime(int startTime) {
        this.startTime = startTime;
    }

    public int getEndTime() {
        return endTime;
    }

    public void setEndTime(int endTime) {
        this.endTime = endTime;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }

    public String getIosZipMd5() {
        return iosZipMd5;
    }

    public void setIosZipMd5(String iosZipMd5) {
        this.iosZipMd5 = iosZipMd5;
    }

    public String getIosXZipMd5() {
        return iosXZipMd5;
    }

    public void setIosXZipMd5(String iosXZipMd5) {
        this.iosXZipMd5 = iosXZipMd5;
    }

    public String getAndroidZipMd5() {
        return androidZipMd5;
    }

    public void setAndroidZipMd5(String androidZipMd5) {
        this.androidZipMd5 = androidZipMd5;
    }
}

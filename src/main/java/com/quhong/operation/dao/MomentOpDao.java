package com.quhong.operation.dao;

import com.quhong.mongo.config.MongoBean;
import com.quhong.redis.DataRedisBean;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.annotation.Id;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;

@Component
public class MomentOpDao {

    private static final Logger logger = LoggerFactory.getLogger(MomentOpDao.class);
    private static final int PAGE_SIZE = 10;
    public static final String TABLE_NAME = "moment";

    @Resource(name = MongoBean.MOVIES)
    private MongoTemplate mongoTemplate;
    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate redisTemplate;

    public List<String> getTopMoment() {
        try {
            return redisTemplate.opsForList().range(getTopMomentKey(), 0, -1);
        } catch (Exception e) {
            logger.error("get top moment error, msg={}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    private String getTopMomentKey() {
        return "set_top_moment_rds";
    }

    public List<MomentData> getMomentList(String aid, long page) {
        List<MomentData> dataList = new ArrayList<>();
        try {
            page = page < 0 ? 0 : page - 1;
            Criteria criteria = Criteria.where("uid").is(aid);
            Query query = new Query(criteria);
            Sort sort = Sort.by(Sort.Direction.DESC, "c_time");
            query.with(sort).skip(PAGE_SIZE * page).limit(PAGE_SIZE);
            dataList = mongoTemplate.find(query, MomentData.class);
            for (MomentData momentData : dataList) {
                momentData.setId(momentData.get_id().toString());
                momentData.set_id(null);
            }
        } catch (Exception e) {
            logger.error("get moment list error. {}", e.getMessage(), e);
        }
        return dataList;
    }

    public MomentData findMomentOne(String momentId) {
        try {
            Criteria criteria = Criteria.where("_id").is(momentId);
            Query query = new Query(criteria);
            return mongoTemplate.findOne(query, MomentData.class);
        } catch (Exception e) {
            logger.error("findMomentOne error={}", e.getMessage(), e);
            return null;
        }
    }

    public List<MomentData> getPublicMomentList(String aid, Integer startTime, Integer endTime, long page, int pageSize) {
        List<MomentData> dataList = new ArrayList<>();
        try {
            Criteria criteria = Criteria.where("show").is(1).and("is_blocked").is(0);
            if (null != aid) {
                criteria.and("uid").is(aid);
            }
            if (null != startTime && null != endTime) {
                criteria.and("c_time").gte(startTime).lte(endTime);
            }
            Query query = new Query(criteria);
            Sort sort = Sort.by(Sort.Direction.DESC, "c_time");
            query.with(sort).skip(pageSize * (page - 1)).limit(pageSize);
            dataList = mongoTemplate.find(query, MomentData.class);
        } catch (Exception e) {
            logger.error("get moment list error. {}", e.getMessage(), e);
        }
        return dataList;
    }

    public List<Comment> getComments(String mid) {
        try {
            Criteria criteria = Criteria.where("moment_id").is(mid);
            Sort sort = Sort.by(Sort.Direction.DESC, "c_time");
            Query query = new Query(criteria).with(sort);
            return mongoTemplate.find(query, Comment.class);
        } catch (Exception e) {
            logger.error("get comments error. mid={} {}", mid, e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    @Document(collection = "comment")
    public static class Comment {
        @Id
        private ObjectId _id;
        private String moment_id; // 对应的动态
        private String commentator; // 评论者
        private String content; // 评论内容
        private String reply_to; // 回复对象
        private String comment_id; // 回复评论
        private String original_id; // 原始评论id，用于新版本合并评论查询，【为空时设置为NULL】
        private int reply_count; // 原始评论的回复数，等于original_id的数量
        private Set<String> likes; // 新版本评论点赞列表
        private List<MomentData.AtUser> at_list; // @用户列表
        private int is_blocked;  // 是否被封  1表示被封
        private int c_time; // 时间

        public ObjectId get_id() {
            return _id;
        }

        public void set_id(ObjectId _id) {
            this._id = _id;
        }

        public String getMoment_id() {
            return moment_id;
        }

        public void setMoment_id(String moment_id) {
            this.moment_id = moment_id;
        }

        public String getCommentator() {
            return commentator;
        }

        public void setCommentator(String commentator) {
            this.commentator = commentator;
        }

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }

        public String getReply_to() {
            return reply_to;
        }

        public void setReply_to(String reply_to) {
            this.reply_to = reply_to;
        }

        public String getComment_id() {
            return comment_id;
        }

        public void setComment_id(String comment_id) {
            this.comment_id = comment_id;
        }

        public List<MomentData.AtUser> getAt_list() {
            return at_list;
        }

        public void setAt_list(List<MomentData.AtUser> at_list) {
            this.at_list = at_list;
        }

        public int getIs_blocked() {
            return is_blocked;
        }

        public void setIs_blocked(int is_blocked) {
            this.is_blocked = is_blocked;
        }

        public int getC_time() {
            return c_time;
        }

        public void setC_time(int c_time) {
            this.c_time = c_time;
        }

        public String getOriginal_id() {
            return original_id;
        }

        public void setOriginal_id(String original_id) {
            this.original_id = original_id;
        }

        public int getReply_count() {
            return reply_count;
        }

        public void setReply_count(int reply_count) {
            this.reply_count = reply_count;
        }

        public Set<String> getLikes() {
            return likes;
        }

        public void setLikes(Set<String> likes) {
            this.likes = likes;
        }
    }

    @Document(collection = TABLE_NAME)
    public static class MomentData {
        @Id
        private ObjectId _id;
        private String uid;  // 用户uid
        private String text;  // 文字消息
        private List<Image> imgs;  // 图片信息
        private int show = 1;  // 浏览权限  1 公开，2 朋友可见，3 仅自己可见
        private int comments;  // 评论数
        private int reports;  // 举报数
        private int repost;  // 转发数量
        private int is_blocked;  // 是否被封  1表示被封
        private int gifted;  // 礼物打赏总数
        private List<String> likes;  // 点赞列表，存aid
        private List<AtUser> at_list;  // @用户列表
        private Quote quote; // 引用对象，链接、转发及分享等
        private Theme theme; // 主题背景
        @Deprecated
        private String location;  // 地理位置
        private int c_time;  // 发布时间
        private String id;  // 朋友圈id

        public static class Image {
            private String origin;
            private String thumbnail;
            private Integer safe;
            private String width;
            private String height;

            public String getOrigin() {
                return origin;
            }

            public void setOrigin(String origin) {
                this.origin = origin;
            }

            public String getThumbnail() {
                return thumbnail;
            }

            public void setThumbnail(String thumbnail) {
                this.thumbnail = thumbnail;
            }

            public Integer getSafe() {
                return safe;
            }

            public void setSafe(Integer safe) {
                this.safe = safe;
            }

            public String getWidth() {
                return width;
            }

            public void setWidth(String width) {
                this.width = width;
            }

            public String getHeight() {
                return height;
            }

            public void setHeight(String height) {
                this.height = height;
            }
        }

        public static class AtUser {
            private String aid;
            private Integer start;
            private Integer end;

            public String getAid() {
                return aid;
            }

            public void setAid(String aid) {
                this.aid = aid;
            }

            public Integer getStart() {
                return start;
            }

            public void setStart(Integer start) {
                this.start = start;
            }

            public Integer getEnd() {
                return end;
            }

            public void setEnd(Integer end) {
                this.end = end;
            }
        }

        public static class Quote {
            private int type; // 1转发动态 2分享链接 3YouTube链接 4官方链接(需要拼接uid&token) 5分享房间
            private String content; // 引用内容，原博内容或网页title
            private String icon; // 引用的图标，原博主头像、原博第一个图片、网站图标等
            private String action; // type=1为mid、type=2为普通link、type=3为YouTube链接
            private String videoId; // 视频id，YouTube才有值

            public String getContent() {
                return content;
            }

            public void setContent(String content) {
                this.content = content;
            }

            public String getIcon() {
                return icon;
            }

            public void setIcon(String icon) {
                this.icon = icon;
            }

            public String getAction() {
                return action;
            }

            public void setAction(String action) {
                this.action = action;
            }

            public int getType() {
                return type;
            }

            public void setType(int type) {
                this.type = type;
            }

            public String getVideoId() {
                return videoId;
            }

            public void setVideoId(String videoId) {
                this.videoId = videoId;
            }
        }

        public static class Theme {
            private int bgId; // 背景id
            private String bg; // 背景
            private String fontColor; // 字体颜色

            public int getBgId() {
                return bgId;
            }

            public void setBgId(int bgId) {
                this.bgId = bgId;
            }

            public String getBg() {
                return bg;
            }

            public void setBg(String bg) {
                this.bg = bg;
            }

            public String getFontColor() {
                return fontColor;
            }

            public void setFontColor(String fontColor) {
                this.fontColor = fontColor;
            }
        }

        public ObjectId get_id() {
            return _id;
        }

        public void set_id(ObjectId _id) {
            this._id = _id;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getUid() {
            return uid;
        }

        public void setUid(String uid) {
            this.uid = uid;
        }

        public String getText() {
            return text;
        }

        public void setText(String text) {
            this.text = text;
        }

        public List<Image> getImgs() {
            return imgs;
        }

        public void setImgs(List<Image> imgs) {
            this.imgs = imgs;
        }

        public String getLocation() {
            return location;
        }

        public void setLocation(String location) {
            this.location = location;
        }

        public int getShow() {
            return show;
        }

        public void setShow(int show) {
            this.show = show;
        }

        public int getC_time() {
            return c_time;
        }

        public void setC_time(int c_time) {
            this.c_time = c_time;
        }

        public int getComments() {
            return comments;
        }

        public void setComments(int comments) {
            this.comments = comments;
        }

        public int getReports() {
            return reports;
        }

        public void setReports(int reports) {
            this.reports = reports;
        }

        public int getRepost() {
            return repost;
        }

        public void setRepost(int repost) {
            this.repost = repost;
        }

        public int getIs_blocked() {
            return is_blocked;
        }

        public void setIs_blocked(int is_blocked) {
            this.is_blocked = is_blocked;
        }

        public int getGifted() {
            return gifted;
        }

        public void setGifted(int gifted) {
            this.gifted = gifted;
        }

        public List<String> getLikes() {
            return likes;
        }

        public void setLikes(List<String> likes) {
            this.likes = likes;
        }

        public List<AtUser> getAt_list() {
            return at_list;
        }

        public void setAt_list(List<AtUser> at_list) {
            this.at_list = at_list;
        }

        public Quote getQuote() {
            return quote;
        }

        public void setQuote(Quote quote) {
            this.quote = quote;
        }

        public Theme getTheme() {
            return theme;
        }

        public void setTheme(Theme theme) {
            this.theme = theme;
        }
    }

}

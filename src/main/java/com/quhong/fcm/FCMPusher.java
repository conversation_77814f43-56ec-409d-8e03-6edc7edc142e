package com.quhong.fcm;

import com.google.auth.oauth2.ServiceAccountCredentials;
import com.google.firebase.FirebaseApp;
import com.google.firebase.FirebaseOptions;
import com.google.firebase.messaging.*;
import com.quhong.config.AsyncConfig;
import com.quhong.data.ActorData;
import com.quhong.enums.LogType;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.redis.UserOnlineRedis;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.InputStream;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <a href="https://firebase.google.cn/docs/cloud-messaging/send-message?hl=zh-cn">FCM API</a>
 */
@Component
public class FCMPusher {
    private static final Logger logger = LoggerFactory.getLogger(FCMPusher.class);
    private static final Logger msgLogger = LoggerFactory.getLogger(LogType.MESSAGE_LOG);
    /**
     * fcm批量发送最大发送量
     */
    public static final int FCM_BATCH_SEND_MAX = 500;
    private FirebaseApp firebaseApp;

    @Resource
    private ActorDao actorDao;
    @Resource
    private UserOnlineRedis userOnlineRedis;
    @Value(value = "classpath:findher-87c07-firebase.json")
    private org.springframework.core.io.Resource sa;

    @PostConstruct
    public void init() {
        try {
            ServiceAccountCredentials credentials;
            try (InputStream stream = sa.getInputStream()) {
                credentials = ServiceAccountCredentials.fromStream(stream);
            }
            FirebaseOptions options = FirebaseOptions.builder()
                    .setCredentials(credentials)
                    .setProjectId(credentials.getProjectId())
                    .build();
            firebaseApp = FirebaseApp.initializeApp(options, credentials.getProjectId());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public String getFirebaseToken(String toUid) {
        ActorData actorData = actorDao.getActorDataFromCache(toUid);
        if (null == actorData
                || null == actorData.getPush()
                || (null != actorData.getGeneralConfActorData() && 0 == actorData.getGeneralConfActorData().getLogin_status())) {
            return null;
        }
        if (null != actorData.getPush().getPrivate_message() && actorData.getPush().getPrivate_message() == 0) {
            return null;
        }
        // 对方在线
        if (userOnlineRedis.isOnline(toUid) > 0) {
            return null;
        }
        return actorData.getPush().getToken();
    }

    @Async(AsyncConfig.ASYNC_TASK)
    public void pushSingle(String toUid, Map<String, String> paramMap, String title, String body, String img) {
        String token = getFirebaseToken(toUid);
        if (ObjectUtils.isEmpty(token)) {
            return;
        }
        try {
            Notification.Builder notificationBuilder = Notification.builder();
            if (StringUtils.hasLength(title)) {
                notificationBuilder.setTitle(title);
            }
            if (StringUtils.hasLength(body)) {
                notificationBuilder.setBody(body);
            }
            AndroidNotification.Builder androidBuilder = AndroidNotification.builder().setTitle(title).setBody(body);
            Aps.Builder apsBuilder = Aps.builder();
            ApnsFcmOptions.Builder apnsFcmOptions = ApnsFcmOptions.builder();
            apsBuilder.setContentAvailable(true);

            if (StringUtils.hasLength(img)) {
                notificationBuilder.setImage(img);
                androidBuilder.setImage(img);
                apsBuilder.setMutableContent(true);
                apnsFcmOptions.setImage(img);
            }
            // 构建消息内容
            Message message = Message.builder()
                    .setNotification(notificationBuilder.build())
                    .setAndroidConfig(AndroidConfig.builder()
                            .setTtl(TimeUnit.DAYS.toMillis(1L))
                            .setPriority(AndroidConfig.Priority.NORMAL)
                            .setNotification(androidBuilder.build())
                            .build())
                    .setApnsConfig(ApnsConfig.builder()
                            .putHeader("apns-priority", "5")
                            .setAps(apsBuilder.build())
                            .setFcmOptions(apnsFcmOptions.build())
                            .build())
                    .setToken(token)
                    .putAllData(paramMap)
                    .build();
            String messageId = FirebaseMessaging.getInstance(firebaseApp).send(message);
            msgLogger.info("push fcm success. toUid={} messageId={} ", toUid, messageId);
        } catch (FirebaseMessagingException e) {
            if (e.getMessagingErrorCode() != null) {
                logger.info("push fcm failed, toUid={}, errorCode={}, msg={}", toUid, e.getMessagingErrorCode(), e.getMessage());
            } else {
                logger.error("push fcm failed, toUid={}", toUid, e);
            }
        }
    }

    @Async(AsyncConfig.ASYNC_TASK)
    public void pushSingleByToken(String token, Map<String, String> paramMap, String title, String body, String img) {
        if (ObjectUtils.isEmpty(token)) {
            return;
        }
        try {
            Notification.Builder notificationBuilder = Notification.builder();
            if (StringUtils.hasLength(title)) {
                notificationBuilder.setTitle(title);
            }
            if (StringUtils.hasLength(body)) {
                notificationBuilder.setBody(body);
            }
            AndroidNotification.Builder androidBuilder = AndroidNotification.builder().setTitle(title).setBody(body);
            Aps.Builder apsBuilder = Aps.builder();
            ApnsFcmOptions.Builder apnsFcmOptions = ApnsFcmOptions.builder();
            apsBuilder.setContentAvailable(true);

            if (StringUtils.hasLength(img)) {
                notificationBuilder.setImage(img);
                androidBuilder.setImage(img);
                apsBuilder.setMutableContent(true);
                apnsFcmOptions.setImage(img);
            }
            // 构建消息内容
            Message message = Message.builder()
                    .setNotification(notificationBuilder.build())
                    .setAndroidConfig(AndroidConfig.builder()
                            .setTtl(TimeUnit.DAYS.toMillis(1L))
                            .setPriority(AndroidConfig.Priority.NORMAL)
                            .setNotification(androidBuilder.build())
                            .build())
                    .setApnsConfig(ApnsConfig.builder()
                            .putHeader("apns-priority", "5")
                            .setAps(apsBuilder.build())
                            .setFcmOptions(apnsFcmOptions.build())
                            .build())
                    .setToken(token)
                    .putAllData(paramMap)
                    .build();
            String messageId = FirebaseMessaging.getInstance(firebaseApp).send(message);
            msgLogger.info("push fcm success. token={} messageId={} ", token, messageId);
        } catch (FirebaseMessagingException e) {
            if (e.getMessagingErrorCode() != null) {
                logger.info("push fcm failed, token={}, errorCode={}, msg={}", token, e.getMessagingErrorCode(), e.getMessage());
            } else {
                logger.error("push fcm failed, token={}", token, e);
            }
        }
    }

    /**
     * 批量发送通知（官方规定最大数为500）
     */
    public void pushBatch(Set<String> tokenSet, Map<String, String> paramMap, String title, String body) {
        pushBatch(tokenSet, paramMap, title, body, null);
    }

    public void pushBatch(Set<String> tokenSet, Map<String, String> paramMap, String title, String body, String img) {
        try {
            if (CollectionUtils.isEmpty(tokenSet)) {
                return;
            }
            if (tokenSet.size() > FCM_BATCH_SEND_MAX) {
                tokenSet = tokenSet.stream().limit(FCM_BATCH_SEND_MAX).collect(Collectors.toSet());
            }
            Notification.Builder notificationBuilder = Notification.builder();
            if (StringUtils.hasLength(title)) {
                notificationBuilder = notificationBuilder.setTitle(title);
            }
            if (StringUtils.hasLength(body)) {
                notificationBuilder = notificationBuilder.setBody(body);
            }
            Aps.Builder apsBuilder = Aps.builder();
            ApnsFcmOptions.Builder apnsFcmOptions = ApnsFcmOptions.builder();
            apsBuilder.setContentAvailable(true);
            apsBuilder.setBadge(0);
            AndroidNotification.Builder androidBuilder = AndroidNotification.builder().setTitle(title).setBody(body);

            if (StringUtils.hasLength(img)) {
                notificationBuilder.setImage(img);
                androidBuilder.setImage(img);
                apsBuilder.setMutableContent(true);
                apnsFcmOptions.setImage(img);
            }


            //构建消息内容
            MulticastMessage message = MulticastMessage.builder()
                    .setNotification(notificationBuilder.build())
                    .setAndroidConfig(AndroidConfig.builder()
                            .setTtl(3600 * 1000)
                            .setPriority(AndroidConfig.Priority.HIGH)
                            .setNotification(androidBuilder.build())
                            .build())
                    .setApnsConfig(ApnsConfig.builder()
                            .putHeader("apns-priority", "5")
                            .setAps(apsBuilder.build()).setFcmOptions(apnsFcmOptions.build()).build())
                    .addAllTokens(tokenSet)
                    .putAllData(paramMap)
                    .build();
            BatchResponse messageId = FirebaseMessaging.getInstance(firebaseApp).sendEachForMulticast(message);
            msgLogger.info("push batch fcm success. messageId={} total={}", messageId, tokenSet.size());
        } catch (Exception e) {
            logger.error("push batch fcm failed total={} {}", tokenSet.size(), e.getMessage(), e);
        }
    }
}

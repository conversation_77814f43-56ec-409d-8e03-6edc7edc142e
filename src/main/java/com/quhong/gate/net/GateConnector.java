package com.quhong.gate.net;

import com.quhong.core.clusters.ClusterGroup;
import com.quhong.core.concurrency.queues.TaskQueue;
import com.quhong.core.concurrency.tasks.AbstractTask;
import com.quhong.core.concurrency.tasks.ITask;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.msg.Msg;
import com.quhong.core.msg.server.PlayerFrontOnlineMsg;
import com.quhong.core.msg.server.PlayerLogoutMsg;
import com.quhong.core.net.connect.AbstractConnector;
import com.quhong.core.timers.SecondTask;
import com.quhong.core.timers.TimerService;
import com.quhong.core.utils.SpringUtils;
import com.quhong.data.ActorData;
import com.quhong.datas.PlayerData;
import com.quhong.enums.ClientOS;
import com.quhong.enums.ClusterEnum;
import com.quhong.enums.LogType;
import com.quhong.gate.logback.GateLogger;
import com.quhong.gate.redis.PlayerRedis;
import com.quhong.msg.MarsCacheMsg;
import com.quhong.msg.MarsMsg;
import com.quhong.redis.UserOnlineRedis;
import io.netty.channel.ChannelHandlerContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import java.util.LinkedList;

/**
 * <AUTHOR>
 * @date 2018/10/18
 * @copyright szxx
 */
public class GateConnector extends AbstractConnector {
    private static final Logger logger = LoggerFactory.getLogger(GateConnector.class);
    private static final Logger msgLogger = LoggerFactory.getLogger(LogType.MESSAGE_LOG);

    private static final int SERVER_MD5_KEY = 0x9a7ef1a4;

    private static final long LONG_MD5_KEY = 0xa97ef1a45ee9ab04L;

    private static final int CHECK_TIME_OUT_RANGE = 10000; //误差10秒检查

    private static final int DEFAULT_EXPIRE_TIME = 90 * 1000; //默认过期时间为90s

    private static final int HEART_INTERVAL = 45 * 1000; //心跳间隔

    private static final int CONNECT_TOO_LONG_TIME = 3 * 60 * 1000; //没有验证，连接超时

    private static final int REQ_CLINET_HEART_INTERVAL = 60 * 1000; //一分钟客户端未响应，引导客户端发消息

    private GateMsgProcessor msgProcessor;

    private TaskQueue taskQueue;
    private PlayerData playerData;

    private long heartExpireTime;
    private SecondTask secondTask;
    private boolean login;
    private long receiveMsgTime;
    private PlayerRedis playerRedis;
    private long updateHeartTime;
    private boolean sendRequestHeart;
    /**
     * actor
     **/
    private ActorData actorData;
    private long loginTime;

    private boolean clientFront = true; //默认前台
    private int curScene; //当前场景
    private long updateSceneTime;
    private LinkedList<Long> repeatMsgList;
    private boolean hasSendOffline;
    private boolean loginFlag; //用于心跳辨别使用
    private String roomId; //房间id
    private boolean requestUnSendMsg; //是否发送过房间离线请求
    private long openTime;
    private long lastSendHeartTime;// 上次发送心跳时间
    private String remoteAddress;
    private boolean hasSendFrontOnline;

    @Override
    protected void doDispose() {
        try {
            super.doDispose();
        } catch (Exception e) {
            GateLogger.error(logger, this, "do dispose error.{}", e.getMessage(), e);
        }
        this.login = false;
        if (!kicked) {
            boolean result = SessionMgr.instance.remove(GateConnector.this);
            if (result && (!StringUtils.isEmpty(getUid()))) {
                // 移除成功才调用playerRedis
                PlayerRedis playerRedis = SpringUtils.getBean(PlayerRedis.class);
                playerRedis.offline(getUid(), getSessionId());
                UserOnlineRedis userOnlineRedis = SpringUtils.getBean(UserOnlineRedis.class);
                userOnlineRedis.offline(getUid());
            }
        }
    }

    public GateConnector(GateMsgProcessor msgProcessor) {
        this.msgProcessor = msgProcessor;
        this.taskQueue = new TaskQueue();
        this.repeatMsgList = new LinkedList<>();
    }

    @Override
    public void sessionOpened() {
        this.openTime = System.currentTimeMillis();
        this.remoteAddress = session.channel().remoteAddress().toString();
        GateLogger.info(msgLogger, this, "client session opened. ");
        GateLogger.info(logger, this, "client session opened. remoteAddress={}", session.channel().remoteAddress());
        SessionMgr.instance.addToSessionMap(this);

        if (secondTask != null) {
            TimerService.getService().removeTicker(secondTask);
        }
        this.secondTask = new SecondTask(this.taskQueue) {
            @Override
            protected void execute() {
                onTick();
            }
        };
        TimerService.getService().addTicker(secondTask);
    }

    @Override
    public void sessionClosed() {
        GateLogger.info(msgLogger, this, "session closed");
        GateLogger.info(logger, this, "session closed");
        this.dispose();
    }

    public void sendOffline() {
        if (playerData == null) {
            return;
        }
        if (hasSendOffline) {
            return;
        }
        hasSendOffline = true;
        if (null == playerRedis) {
            playerRedis = SpringUtils.getBean(PlayerRedis.class);
        }
        PlayerData redisPlayerData = playerRedis.getPlayerDataFromRedis(getUid());
        if (redisPlayerData != null && redisPlayerData.getSessionId() != -1) {
            int serverId = ClusterGroup.fetchServerIdFromSessionId(redisPlayerData.getSessionId());
            if (serverId != ServerConfig.getServerID()) {
                GateLogger.info(logger, this, "actor in other gate, skip send offline msg.");
                return;
            }
        }
        PlayerLogoutMsg msg = new PlayerLogoutMsg();
        msg.getHeader().setUid(getUid());
        msg.getHeader().setSessionId(this.getSessionId());
        msg.setPlayerUid(playerData.getUid());
        if (loginTime != 0) {
            msg.setDuration(System.currentTimeMillis() - loginTime);
        }
        byte[] msgBody = msg.toBody();
        ClusterGroup.sendCacheMsgByClusterId(ClusterEnum.ROOM, msg, msgBody);
        ClusterGroup.sendCacheMsgByClusterId(ClusterEnum.DATA, msg, msgBody);
    }

    @Override
    public void receiveMsg(ChannelHandlerContext session, Msg msg) {
        MarsMsg marsMsg = (MarsMsg) msg;
        receiveMsgTime = System.currentTimeMillis();
//        GateLogger.info(msgLogger, this, "receive msg, cmd={} msgId={} seq={}", msg.getCmd(), marsMsg.getProtoHeader().getMsgId(), marsMsg.getHeader().getSeq());
        taskQueue.add(new AbstractTask() {
            @Override
            protected void execute() {
                msgProcessor.execute(GateConnector.this, msg);
                updateHeartStatus();
            }
        });
    }

    @Override
    public void connectingLong() {
        GateLogger.info(logger, this, "connect too long. close the session");
        this.close();
    }

    private void onTick() {
        checkExpire();
        checkConnectTooLong();

    }

    private void checkConnectTooLong() {
        if (StringUtils.isEmpty(getUid())) {
            if (System.currentTimeMillis() - this.openTime > CONNECT_TOO_LONG_TIME) {
                // 链接没有验证超过三分钟，结束掉
                this.dispose();
            }
        }
    }

    private void checkExpire() {
        if (heartExpireTime == 0) {
            return;
        }
        long curTime = System.currentTimeMillis();
        if (curTime > heartExpireTime) {
            GateLogger.info(logger, this, "heart expired. ");
            this.close();
        }
    }

//    @Override
//    public void sendMsg(Msg msg) {
//        MarsMsg marsMsg = (MarsMsg) msg;
//        if (marsMsg.getProtoHeader() == null) {
//            GateLogger.info(msgLogger, this, "send msg to client. cmd={} seq={} sessionState={}", marsMsg.getCmd(), marsMsg.getHeader().getSeq(), getSessionState());
//        } else {
//            GateLogger.info(msgLogger, this, "send msg to client. cmd={} msgId={} seq={} sessionState={}", marsMsg.getCmd(), marsMsg.getProtoHeader().getMsgId(), marsMsg.getHeader().getSeq(), getSessionState());
//        }
//        super.sendMsg(msg);
//    }

    private String getSessionState() {
        if (this.session != null) {
            if (this.session.channel().isActive()) {
                return "active";
            } else {
                return "deactive";
            }
        } else {
            return "null";
        }
    }

    public String getUid() {
        if (playerData == null) {
            return "";
        }
        return playerData.getUid();
    }

    public PlayerData getPlayerData() {
        return playerData;
    }

    public void setPlayerData(PlayerData playerData) {
        this.playerData = playerData;
    }

    public void add(ITask task) {
        this.taskQueue.add(task);
    }

    public long getHeartExpireTime() {
        return heartExpireTime;
    }

    @Override
    protected void doClose() {
        super.doClose();
        if (secondTask != null) {
            TimerService.getService().removeTicker(secondTask);
            secondTask = null;
        }
    }

    /**
     * 更新心跳过期时间，如果超过interval三倍时间，则视为客户端断线
     *
     * @param interval
     */
    public void updateHeartExpireTime(int interval) {
        sendRequestHeart = false;
        updateHeartTime = System.currentTimeMillis();
        if (interval == 0) {
            this.heartExpireTime = System.currentTimeMillis() + DEFAULT_EXPIRE_TIME;
        } else {
            this.heartExpireTime = System.currentTimeMillis() + interval * 3;
        }
    }

    public boolean isLogin() {
        return login;
    }

    public void setLogin(boolean login) {
        this.login = login;
        this.loginTime = System.currentTimeMillis();
    }

    public TaskQueue getTaskQueue() {
        return taskQueue;
    }

    public void updateHeartStatus() {
        if (heartExpireTime == 0) {
            return;
        }
        long curTime = System.currentTimeMillis();
        if (curTime - updateHeartTime < HEART_INTERVAL) {
            return;
        }
        try {
            if (System.currentTimeMillis() - receiveMsgTime < HEART_INTERVAL) {
                if (playerRedis == null) {
                    playerRedis = SpringUtils.getBean(PlayerRedis.class);
                }
                playerRedis.updateStatus(this);
                GateLogger.info(msgLogger, this, "update heart by other msg");
                updateHeartExpireTime(HEART_INTERVAL);
            }
        } catch (Exception e) {
            GateLogger.error(logger, this, "{}", e.getMessage(), e);
        }
    }

    public boolean isClientFront() {
        return clientFront;
    }

    /**
     * 切换到前台
     */
    public void clientFront() {
        if (!clientFront) {
            GateLogger.info(msgLogger, this, "client from back to front");
            clientFront = true;
        }
        if (!hasSendFrontOnline) {
            hasSendFrontOnline = true;
            sendFrontOnline();
        }
    }

    private void sendFrontOnline() {
        PlayerFrontOnlineMsg msg = new PlayerFrontOnlineMsg();
        msg.getHeader().setUid(this.getUid());
        msg.setPlayerUid(this.getUid());
        ClusterGroup.sendMsgByClusterId(ClusterEnum.DATA, msg);
    }

    public void clientBack() {
        if (clientFront) {
            GateLogger.info(msgLogger, this, "client from front to back");
            clientFront = false;
        }
    }

    public int getCurScene() {
        return curScene;
    }

    public void setCurScene(int curScene) {
        this.curScene = curScene;
        this.updateSceneTime = System.currentTimeMillis();
    }

    public long getUpdateSceneTime() {
        return updateSceneTime;
    }

    public void setUpdateSceneTime(long updateSceneTime) {
        this.updateSceneTime = updateSceneTime;
    }

    public LinkedList<Long> getRepeatMsgList() {
        return repeatMsgList;
    }

    public void setRepeatMsgList(LinkedList<Long> repeatMsgList) {
        this.repeatMsgList = repeatMsgList;
    }

    public long getUpdateHeartTime() {
        return updateHeartTime;
    }

    public boolean isLoginFlag() {
        return loginFlag;
    }

    public void setLoginFlag(boolean loginFlag) {
        this.loginFlag = loginFlag;
    }

    public String getRoomId() {
        return roomId;
    }

    public void setRoomId(String roomId) {
        this.roomId = roomId;
    }

    public boolean isRequestUnSendMsg() {
        return requestUnSendMsg;
    }

    public void requestUnSendMsg() {
        this.requestUnSendMsg = true;
    }

    public ActorData getActorData() {
        return actorData;
    }

    public void setActorData(ActorData actorData) {
        this.actorData = actorData;
    }

    public int getOs() {
        if (actorData != null) {
            return actorData.getIntOs();
        }
        int os = playerData.getClientSystem() - 1;
        if (os == -1) {
            GateLogger.info(logger, this, "can not get os.");
            os = ClientOS.ANDROID;
        }
        return os;
    }

    public long getLastSendHeartTime() {
        return lastSendHeartTime;
    }

    public void setLastSendHeartTime(long lastSendHeartTime) {
        this.lastSendHeartTime = lastSendHeartTime;
    }

    public String getRemoteAddress() {
        return remoteAddress;
    }

    public void setRemoteAddress(String remoteAddress) {
        this.remoteAddress = remoteAddress;
    }
}

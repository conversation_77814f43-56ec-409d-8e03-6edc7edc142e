package com.quhong.gate.executors;

import com.quhong.cache.CacheMap;
import com.quhong.core.annotation.MsgExecutor;
import com.quhong.core.clusters.ClusterConnector;
import com.quhong.core.executors.AbstractMsgExecutor;
import com.quhong.core.msg.server.PlayerLoginAck;
import com.quhong.data.ActorData;
import com.quhong.enums.BaseServerCmd;
import com.quhong.gate.logback.GateLogger;
import com.quhong.gate.net.GateConnector;
import com.quhong.gate.net.SessionMgr;
import com.quhong.gate.redis.PlayerRedis;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.PostConstruct;

@MsgExecutor
public class LoginAckExecutor extends AbstractMsgExecutor<ClusterConnector, PlayerLoginAck> {
    private static final Logger logger = LoggerFactory.getLogger(LoginAckExecutor.class);

    private static final int CACHE_TIME = 5 * 60 * 1000;

    @Autowired
    private PlayerRedis playerRedis;
    private CacheMap<String, ActorData> cacheMap;

    public LoginAckExecutor() {
        super(BaseServerCmd.PLAYER_LOGIN_ACK);
        this.cacheMap = new CacheMap<>(CACHE_TIME);
    }

    @PostConstruct
    public void postInit(){
        this.cacheMap.start();
    }

    @Override
    public void execute(ClusterConnector connector, PlayerLoginAck msg) {
        GateConnector gateConnector = SessionMgr.instance.getConnectorByUid(msg.getUid());
        if(gateConnector == null){
            logger.error("login ack. can not find connector. uid={}", msg.getUid());
            return;
        }
        ActorData actorData = cacheMap.getData(msg.getUid());
        if(actorData == null) {
            actorData = playerRedis.getActorFromRedis(msg.getUid());
            if (actorData == null) {
                logger.error("login ack. can not find actorData. uid={} sessionId={}", msg.getUid(), gateConnector.getSessionId());
                return;
            }
            cacheMap.cacheData(msg.getUid(), actorData);
        }
        gateConnector.setActorData(actorData);
        GateLogger.info(logger, gateConnector, "login ack. os={} rid={} name={}", actorData.getIntOs(), actorData.getRid(), actorData.getName());
    }
}

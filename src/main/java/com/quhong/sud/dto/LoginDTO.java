package com.quhong.sud.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 登录请求
 * 注意：请求体中的字段命名格式为SNAKE_CASE，需配置spring.jackson.property-naming-strategy=SNAKE_CASE
 *
 * <AUTHOR>
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class LoginDTO {
    /**
     * 用户ID（必填参数），字段名：user_id
     */
    @JSONField(name = "user_id")
    String userId;

    /**
     * TODO：其他参数视接入方业务决定
     */
}

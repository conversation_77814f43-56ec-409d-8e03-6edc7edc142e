package com.quhong.sud.dto;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.quhong.data.ActorData;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.sud.vo.GetUserInfoVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


/**
 * <a href="https://docs.sud.tech/zh-CN/app/Server/API/PushEventData/QuickStartReqData.html">...</a>
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class EventDTO {

    public static final String LUDO_GAME_STR = "1468180338417074177"; // 即构ludo游戏id
    public static final String UMO_GAME_STR = "1472142559912517633"; // 即构UMO游戏id

    private String event; // 游戏事件
    private String mg_id; // 游戏id
    private long timestamp; // 推送时间戳 (毫秒)
    private JSONObject data; // 事件数据

    public static EventDTO GameEndEvent(String sudGameId, String gameId, String uid) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("room_id", gameId);
        jsonObject.put("uid", uid);
        return new EventDTO("game_end", sudGameId, System.currentTimeMillis(), jsonObject);
    }

    public static EventDTO RoomClearEvent(String sudGameId, String gameId) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("room_id", gameId);
        return new EventDTO("room_clear", sudGameId, System.currentTimeMillis(), jsonObject);
    }

    public static EventDTO QuickStartEvent(String sudGameId, String gameId, List<GetUserInfoVO> userInfos, JSONObject rule) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("room_id", gameId);
        jsonObject.put("user_infos", userInfos);
        jsonObject.put("rule", rule);
        jsonObject.put("report_game_info_extras", "");
        jsonObject.put("report_game_info_key", gameId);
        if (null != rule && rule.containsKey("mode_ex")) {
            jsonObject.put("mode_ex", rule.getIntValue("mode_ex"));
        }
        if ("1599672757949743105".equals(sudGameId)){
            jsonObject.put("language", "ar-SA");
        }
        return new EventDTO("quick_start", sudGameId, System.currentTimeMillis(), jsonObject);
    }

    public static EventDTO addAi(String sudGameId, String gameId, ActorData actorData) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("room_id", gameId);
        jsonObject.put("is_ready", 1);
        JSONArray players = new JSONArray();
        JSONObject player = new JSONObject();
        player.put("uid", actorData.getUid());
        player.put("avatar", ImageUrlGenerator.generateSudGameUserUrl(actorData.getHead(), 0));
        player.put("name", actorData.getName());
        player.put("gender", actorData.getFb_gender() == 1 ? "male" : "female");
        players.add(player);
        jsonObject.put("ai_players", players);
        return new EventDTO("ai_add", sudGameId, System.currentTimeMillis(), jsonObject);
    }
}

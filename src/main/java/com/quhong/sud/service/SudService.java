package com.quhong.sud.service;

import cn.hutool.crypto.digest.HMac;
import cn.hutool.crypto.digest.HmacAlgorithm;
import com.alibaba.fastjson.JSONObject;
import com.quhong.analysis.CreateGameLogEvent;
import com.quhong.analysis.EventReport;
import com.quhong.api.SudGameApi;
import com.quhong.config.SudGameConfig;
import com.quhong.controllers.SudController;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.queues.SlowTaskQueue;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.distribution.DistributeLock;
import com.quhong.core.timers.LoopTask;
import com.quhong.core.timers.TimerService;
import com.quhong.core.utils.DateHelper;
import com.quhong.dailyTask.CommonTaskService;
import com.quhong.data.*;
import com.quhong.data.dto.MoneyDetailReq;
import com.quhong.data.dto.MuteMicDTO;
import com.quhong.data.dto.UpMicDTO;
import com.quhong.enums.*;
import com.quhong.exception.GameException;
import com.quhong.feign.DataCenterService;
import com.quhong.feign.IRoomService;
import com.quhong.handler.HttpEnvData;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.*;
import com.quhong.mongo.data.MongoRoomData;
import com.quhong.mongo.data.SudGameData;
import com.quhong.mongo.data.SudGamePlayerData;
import com.quhong.monitor.MonitorSender;
import com.quhong.mq.MqSenderService;
import com.quhong.msg.MarsServerMsg;
import com.quhong.msg.obj.HighlightTextObject;
import com.quhong.msg.obj.RoomMicInfoObject;
import com.quhong.msg.obj.UNameObject;
import com.quhong.msg.room.RoomLudoKitOutMsg;
import com.quhong.msg.room.RoomNotificationMsg;
import com.quhong.msg.room.RoomSudGameOperateMsg;
import com.quhong.mysql.dao.HeartRecordDao;
import com.quhong.mysql.dao.RoomBlacklistDao;
import com.quhong.mysql.dao.RoomMicDao;
import com.quhong.mysql.dao.VipInfoDao;
import com.quhong.mysql.mapper.ustar.RoomMicInfoMapper;
import com.quhong.redis.GameRoomRedis;
import com.quhong.redis.RoomMicRedis;
import com.quhong.redis.SudGameRedis;
import com.quhong.redis.TruthOrDareV2Redis;
import com.quhong.room.RoomWebSender;
import com.quhong.room.cache.RoomActorCache;
import com.quhong.room.data.RoomActorDetailData;
import com.quhong.room.redis.RoomKickRedis;
import com.quhong.room.redis.RoomPlayerRedis;
import com.quhong.service.TruthDareV2Service;
import com.quhong.service.TurntableService;
import com.quhong.sud.data.SudGameConfigInfo;
import com.quhong.sud.dto.*;
import com.quhong.sud.vo.*;
import com.quhong.utils.AppVersionUtils;
import com.quhong.utils.CollectionUtil;
import com.quhong.utils.RoomUtils;
import com.quhong.vo.RoomMicListVo;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import tech.sud.mgp.auth.api.SudCode;
import tech.sud.mgp.auth.api.SudMGPAuth;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/7/1
 */
@Service
public class SudService extends SlowTaskQueue implements MqMessageImpl {

    private static final Logger logger = LoggerFactory.getLogger(SudService.class);

    private static final String CREATE_GAME_MSG = "Created the %s game."; // 创建游戏消息
    private static final String CREATE_GAME_MSG_AR = "إنشاء لعبة %s"; // 创建游戏消息阿语
    private static final String CLOSED_GAME_MSG = "%s game is closed."; // 关闭游戏消息
    private static final String CLOSED_GAME_MSG_AR = "تم إغلاق اللعبة %s"; // 关闭游戏消息阿语
    private static final String OTHER_GAME_IN_PROGRESS = "The %s game is playing in the room, please wait for the game to end and try again."; // 有其他游戏正在进行提示
    private static final String OTHER_GAME_IN_PROGRESS_AR = "هناك لعبة %s تلعب في الغرفة ، يرجى الانتظار حتى تنتهي اللعبة والمحاولة مرة أخرى."; // 有其他游戏正在进行提示阿语
    private static final String GAME_TIMEOUT_NOT_START = "%s game timeout does not start, it has ended automatically."; // 游戏超时未开始
    private static final String GAME_TIMEOUT_NOT_START_AR = "لا تبدأ مهلة لعبة %s وتنتهي تلقائيا."; // 游戏超时未开始阿语

    private static final int COIN = 1;
    private static final int DIAMOND = 2;

    private static final int LOOP_TIME = 60 * 1000; // 每分钟扫描一下是否有超时的游戏
    private static final int CHECK_TIME = 5 * 60; // 5分钟未开始游戏系统自动关闭游戏
    private static final int CHECK_OVER_TIME = 2 * 60 * 60; // 存在2个小时的游戏系统自动关闭游戏

    private static final int GAME_ROOM_CHECK_TIME = 1 * 60 * 60; // 游戏房1小时未开始游戏系统自动关闭游戏
    private static final int GAME_ROOM_CHECK_OVER_TIME = 3 * 60 * 60; // 游戏房存在3个小时的游戏系统自动关闭游戏


    private static final String JOIN_GAME_MSG = "%s has joined %s game.";
    private static final String JOIN_GAME_MSG_AR = "انضم %s إلى لعبة %s.";

    private static final String QUIT_GAME_MSG = "%s quit the game.";
    private static final String QUIT_GAME_MSG_AR = "%s خرج من اللعبة.";

    private static final String GAME_TYPE_JOIN_SUD_GAME = "join_sud_game";

    private static final String GAME_TYPE_QUITE_SUD_GAME = "quite_sud_game";

    public static final Map<String, SudGameInfo> gameMap = new ConcurrentHashMap<>();

    private static final int PLATFORM_WARNING_LINE = 10000; // 单款游戏平台亏损1万钻以上时触发告警

    private static final int UMO_CREATE_FEE = 0;

    private static final int LUDO_CREATE_FEE = 10;

    public static final List<Integer> GAME_MATCHING_LIST = Arrays.asList(SudGameConstant.GAME_MATCHING, SudGameConstant.GAME_MATCHING_PAUSE);

    private static final String ADD_ROBOT_JOIN_GAME_FEE_TITLE = "Robot Join Game Fee";

    private static final String ADD_ROBOT_JOIN_GAME_FEE_DESC = "add robot join game fee";

    @Resource
    private RoomMemberDao roomMemberDao;
    @Resource
    private MongoRoomDao mongoRoomDao;
    @Resource
    private ActorDao actorDao;
    @Resource
    private SudGameConfig sudGameConfig;
    @Resource
    private SudGameDao sudGameDao;
    @Resource
    private SudGameRedis sudGameRedis;
    @Resource
    private SudMGPAuth sudMGPAuth;
    @Resource
    private RoomWebSender roomWebSender;
    @Resource
    private RoomActorCache roomActorCache;
    @Resource
    private RoomMicInfoMapper roomMicInfoMapper;
    @Resource
    private HeartRecordDao heartRecordDao;
    @Resource
    private SudGameApi sudGameApi;
    @Resource
    private DataCenterService dataCenterService;
    @Resource
    private RoomConfigDao roomConfigDao;
    @Resource
    private VipInfoDao vipInfoDao;
    @Resource
    private EventReport eventReport;
    @Resource
    private RoomBlacklistDao roomBlacklistDao;
    @Resource
    private RoomKickRedis roomKickRedis;
    @Resource
    private RoomPlayerRedis roomPlayerRedis;
    @Resource
    private MonitorSender monitorSender;
    @Resource
    private MqSenderService mqSenderService;
    @Resource
    private CommonTaskService commonTaskService;
    @Resource
    private TurntableService turntableService;
    @Resource
    private GameRoomRedis gameRoomRedis;
    @Resource
    protected BaseInitData baseInitData;
    @Resource
    protected RoomMicDao roomMicDao;
    @Resource
    private TruthDareV2Service truthDareV2Service;
    @Resource
    private TruthOrDareV2Redis truthOrDareV2Redis;
    @Resource
    private RoomMicRedis roomMicRedis;
    @Resource
    private IRoomService iRoomService;

    @PostConstruct
    public void postInit() {
        // 从redis恢复用户数据
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                recoverGame();
            }
        });
        TimerService.getService().addDelay(new LoopTask(this, LOOP_TIME) {
            @Override
            protected void execute() {
                onTick();
            }
        });
    }

    private void onTick() {
        logger.info("dismiss sud game. gameMap.size={}", gameMap.size());
        for (SudGameInfo game : gameMap.values()) {
            try (DistributeLock lock = new DistributeLock(SudController.SUD_LOCK_KEY + game.getRoomId(), 20)) {
                lock.lock();
                dismissGame(game);
            }
        }
    }

    private void recoverGame() {
        List<SudGameInfo> allGameInfo = sudGameRedis.getAllSudGameInfo();
        if (allGameInfo.size() > 0) {
            synchronized (gameMap) {
                for (SudGameInfo gameInfo : allGameInfo) {
                    if (gameInfo.getStatus() == SudGameConstant.GAME_MATCHING
                            || gameInfo.getStatus() == SudGameConstant.GAME_PROCESSING
                            || gameInfo.getStatus() == SudGameConstant.GAME_MATCHING_PAUSE) {
                        logger.info("recover sud game from redis gameId={}", gameInfo.getGameId());
                        gameMap.put(gameInfo.getGameId(), gameInfo);
                    } else {
                        logger.info("remove finished or closed game gameId={}", gameInfo.getGameId());
                        sudGameRedis.removeGameInfo(gameInfo.getGameId(), gameInfo.getRoomId());
                    }
                }
            }
        }
    }

    /**
     * 关闭五分钟未开始的游戏
     */
    private void dismissGame(SudGameInfo game) {
        int gameExistsTime = DateHelper.getNowSeconds() - game.getCreateTime();
        int checkTime = CHECK_TIME;
        int checkOverTime = CHECK_OVER_TIME;
        boolean isGameRoom = RoomUtils.isGameRoom(game.getRoomId());
        if (isGameRoom) {
            checkTime = GAME_ROOM_CHECK_TIME;
            checkOverTime = GAME_ROOM_CHECK_OVER_TIME;
        }
        if ((GAME_MATCHING_LIST.contains(game.getStatus()) && gameExistsTime > checkTime) || gameExistsTime > checkOverTime) {
            try {
                synchronized (gameMap) {
                    logger.info("dismiss sud game. status={} createTime={} roomId={} gameId={}",
                            game.getStatus(), game.getCreateTime(), game.getRoomId(), game.getGameId());
                    String roomId = game.getRoomId();
                    gameMap.remove(game.getGameId());
                    // 删除redis里的游戏信息
                    sudGameRedis.removeGameInfo(game.getGameId(), roomId);
                    // 更新游戏信息
                    SudGameData data = sudGameDao.findData(game.getGameId());
                    if (data == null) {
                        return;
                    }
                    data.setStatus(SudGameConstant.GAME_CLOSED);
                    data.setEndTime(DateHelper.getNowSeconds());
                    sudGameDao.save(data);
                    data.getPlayerList().forEach(k -> {
                        // 移除玩家在游戏的redis
                        sudGameRedis.removePlayerData(k.getUid());
                        if (GAME_MATCHING_LIST.contains(game.getStatus())) {
                            // 游戏超时未开始，返还入场费
                            returnGameCurrency(data, k.getUid());
                        }
                    });
                    logger.info("dismiss not started sud game. gameId={}", game.getGameId());
                    // 发送游戏超时自动关闭消息
                    sendRoomSudGameOperateMsg(null, game, SudGameConstant.GAME_TIME_OUT_MSG);
                    gameChange(data);
                }
            } catch (Exception e) {
                logger.error("dismiss finished sud game. gameId={} {}", game.getGameId(), e.getMessage(), e);
            }
        }
        if ((GAME_MATCHING_LIST.contains(game.getStatus()) && isGameRoom)) {
            try {
                String inGameRoomId = game.getRoomId();
                SudGameInfo sudGameInfo = sudGameRedis.getSudGameInfo(game.getGameId());
                if (sudGameInfo != null && !CollectionUtils.isEmpty(sudGameInfo.getPlayerList())) {
                    List<String> inGameList = sudGameInfo.getPlayerList().stream().map(SudGamePlayerData::getUid).collect(Collectors.toList());
//                    List<SudGamePlayerData> humanList = sudGameInfo.getPlayerList().stream().filter(item -> item.getRobot() == 0).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(inGameList)) {
                        inGameList.forEach(item -> {
                            String nowRoomId = roomPlayerRedis.getActorRoomStatus(item);
                            if (!inGameRoomId.equals(nowRoomId)) {
                                SudGameData gameData = sudGameDao.findData(game.getGameId());
                                quitAndReturnCoin(item, item, gameData);
                                gameChange(gameData);
                                logger.info("sys clear success aid:{} leave room :{} " +
                                                "leave game:{} now room:{}"
                                        , item, inGameRoomId, game.getGameId(), nowRoomId);
                            }

                        });
                    }

                }
            } catch (Exception e) {
                logger.error("dismiss not in game room user sud game. gameId={} {}", game.getGameId(), e.getMessage(), e);
            }
        }
    }

    /**
     * 发送游戏操作消息
     * opt 1创建游戏 2关闭游戏 3超时关闭 4开始游戏  5 remind 提示消息
     */
    public void sendRoomSudGameOperateMsg(RoomActorDetailData actorData, SudGameInfo game, int opt) {
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                RoomSudGameOperateMsg msg = new RoomSudGameOperateMsg();
                // gameType 1碰碰我最强 2 Ludo 3 UMO
                int gameType = game.getGameType();
                msg.setOpt_user(buildUNameObject(actorData));
                msg.setGame_icon(sudGameConfig.getIconUrlByGameType(gameType));
                msg.setGame_type(gameType);
                msg.setOpt(opt);
                if (opt == SudGameConstant.GAME_CREATE_MSG) {
                    // 创建游戏
                    msg.setMsg(String.format(CREATE_GAME_MSG, game.getGameName()));
                    msg.setMsg_ar(String.format(CREATE_GAME_MSG_AR, game.getGameNameAr()));
                } else if (opt == SudGameConstant.GAME_CLOSED_MSG) {
                    // 结束游戏
                    msg.setMsg(String.format(CLOSED_GAME_MSG, game.getGameName()));
                    msg.setMsg_ar(String.format(CLOSED_GAME_MSG_AR, game.getGameNameAr()));
                } else if (opt == SudGameConstant.GAME_REMIND_MSG) {
                    // 提示消息
                    msg.setMsg("Can not wait to start the game!");
                    msg.setMsg_ar("لا أستطيع الانتظار لبدء اللعبة!");
                } else {
                    // 超时结束游戏
                    msg.setOpt(SudGameConstant.GAME_CLOSED_MSG);
                    msg.setMsg(String.format(GAME_TIMEOUT_NOT_START, game.getGameName()));
                    msg.setMsg_ar(String.format(GAME_TIMEOUT_NOT_START_AR, game.getGameNameAr()));
                }

                int onlineVersion = sudGameConfig.getSudGameInfoMap().get(game.getGameType()).getOnlineVersion();
                roomWebSender.sendRoomWebMsg(game.getRoomId(), "", msg, true, onlineVersion);
            }
        });
    }

    /**
     * 设置房间内创建游戏权限
     */
    public void setPermissions(GamePermissionsDTO reqDTO) {
        String uid = reqDTO.getUid();
        String roomId = reqDTO.getRoomId();
        // 房主才能设置房间内成员创建游戏的权限
        if (!RoomUtils.getRoomHostId(roomId).equals(uid)) {
            throw new GameException(GameHttpCode.NOT_ROOM_OWNER);
        }
        int gameType = reqDTO.getGameType() != null ? reqDTO.getGameType() : SudGameConstant.LUDO_GAME;
        if (gameType == SudGameConstant.LUDO_GAME) {
            mongoRoomDao.updateCreateGamePermissions(roomId, reqDTO.getCreateGame());
        } else if (gameType == SudGameConstant.UMO_GAME) {
            roomConfigDao.updateRoomConfig(roomId, RoomConfigDao.CREATE_UMO, reqDTO.getCreateGame());
        } else if (gameType == SudGameConstant.MONSTER_CRUSH_GAME) {
            roomConfigDao.updateRoomConfig(roomId, RoomConfigDao.CREATE_MONSTER_CRUSH, reqDTO.getCreateGame());
        } else if (gameType == SudGameConstant.DOMINO_GAME) {
            roomConfigDao.updateRoomConfig(roomId, RoomConfigDao.CREATE_DOMINO, reqDTO.getCreateGame());
        } else {
            roomConfigDao.updateRoomConfig(roomId, RoomConfigDao.CREATE_SUD_GAME + gameType, reqDTO.getCreateGame());
        }
    }

    /**
     * 校验房间内创建和开始游戏的权限
     */
    public void checkPermissions(CreateSudGameDTO reqDTO) {
        MongoRoomData roomData = mongoRoomDao.findData(reqDTO.getRoomId());
        if (roomData == null) {
            logger.error("check permissions.roomData = null roomId={}", reqDTO.getRoomId());
            throw new GameException(HttpCode.PARAM_ERROR);
        }
        if (reqDTO.getGameType() == SudGameConstant.WOISSPY_GAME && roomData.getRoomMode() == RoomConstant.LIVE_ROOM_MODE) {
            throw new GameException(GameHttpCode.THE_GAME_NOT_SUPPORT_LIVE);
        }

        int roomMicSize = roomData.getMicSize() <= 0 ? 8 : roomData.getMicSize();
        if (reqDTO.getGameType() == SudGameConstant.WOISSPY_GAME && roomMicSize < 4) {
            throw new GameException(GameHttpCode.THE_GAME_MIC_SIZE_ERROR);
        }

        int createPermissions;
        if (reqDTO.getGameType() == SudGameConstant.LUDO_GAME) {
            createPermissions = roomData.getCreate_game();
        } else if (reqDTO.getGameType() == SudGameConstant.UMO_GAME) {
            createPermissions = roomConfigDao.getIntRoomConfig(reqDTO.getRoomId(), RoomConfigDao.CREATE_UMO, 0);
        } else if (reqDTO.getGameType() == SudGameConstant.MONSTER_CRUSH_GAME) {
            createPermissions = roomConfigDao.getIntRoomConfig(reqDTO.getRoomId(), RoomConfigDao.CREATE_MONSTER_CRUSH, 0);
        } else if (reqDTO.getGameType() == SudGameConstant.DOMINO_GAME) {
            createPermissions = roomConfigDao.getIntRoomConfig(reqDTO.getRoomId(), RoomConfigDao.CREATE_DOMINO, 0);
        } else {
            createPermissions = roomConfigDao.getIntRoomConfig(reqDTO.getRoomId(), RoomConfigDao.CREATE_SUD_GAME + reqDTO.getGameType(), 0);
        }
        RoomRoleData roleData = roomMemberDao.getRoleData(reqDTO.getRoomId(), reqDTO.getUid());
        switch (createPermissions) {
            case SudGameConstant.MEMBER:
                if (roleData.getRole() == RoomRoleType.AUDIENCE) {
                    throw new GameException(GameHttpCode.NOT_THE_ROOM_MEMBER);
                }
                break;
            case SudGameConstant.ADMIN:
                if (roleData.getRole() == RoomRoleType.AUDIENCE || roleData.getRole() == RoomRoleType.MEMBER) {
                    throw new GameException(GameHttpCode.NOT_THE_ROOM_ADMIN);
                }
                break;
            case SudGameConstant.ONLY_ROOM_OWNER:
                if (roleData.getRole() != RoomRoleType.HOST) {
                    throw new GameException(GameHttpCode.NOT_THE_ROOM_OWNER);
                }
                break;
            default:
                break;
        }
    }


    /**
     * 创建游戏
     */
    public CreateSudGameVO createGame(CreateSudGameDTO reqDTO) {
        String uid = reqDTO.getUid();
        String roomId = reqDTO.getRoomId();
        RoomActorDetailData actorData = roomActorCache.getData(roomId, uid, false);
        if (actorData == null) {
            logger.error("can not find actor data, uid ={}", uid);
            throw new GameException(HttpCode.PARAM_ERROR);
        }
        if (SudGameConstant.CARROM_POOL_GAME == reqDTO.getGameType()) {
            if (!reqDTO.getRule().containsKey("mode_ex")) {
                throw new GameException(HttpCode.PARAM_ERROR);
            }
            reqDTO.setPlayerNumber(3 == reqDTO.getRule().getIntValue("mode_ex") ? 4 : 2);
        }
        // 即构小游戏只能同时存在一种
        checkExistOtherSudGame(reqDTO, roomId, reqDTO.getGameType());
        SudGameData sudGameData = checkGame(roomId);
        if (null != sudGameData) {
            logger.info("There are other games in progress. roomId={} ,gameType={}", sudGameData.getGameId(), sudGameData.getGameType());
            throw new GameException(new HttpCode(3021, String.format(OTHER_GAME_IN_PROGRESS, sudGameData.getGameName()), String.format(OTHER_GAME_IN_PROGRESS_AR, sudGameData.getGameNameAr())), gameDataTOVO(sudGameData));
        }
        // 校验创建游戏的权限
        checkPermissions(reqDTO);
        SudGameData gameData = buildSudGameData(reqDTO, actorData);
        ActorData actorDataFromCache = actorDao.getActorDataFromCache(uid);
        if (actorDataFromCache.getRobot() != 1) {
            // Ludo,UMO,Monster Crush游戏创建时需要校验玩家是否已上麦
            List<String> roomMicUidDataList = roomMicInfoMapper.getUidDataList(roomId);
            if (!roomMicUidDataList.contains(uid)) {
                logger.info("player not in mic. uid={} roomId={}", uid, roomId);
                throw new GameException(GameHttpCode.NOT_IN_MIC);
            }
        }
        gameData.setGameId(new ObjectId());
        joinSudGame(uid, roomId, gameData, reqDTO, true);
        // 创建游戏
        if (RoomUtils.isGameRoom(roomId)) {
            // 更新队长
            gameData.setLeaderUid(uid);
        }
        SudGameData data = sudGameDao.save(gameData);
        // 往redis里存入游戏相关信息
        SudGameInfo gameInfo = new SudGameInfo();
        BeanUtils.copyProperties(data, gameInfo);
        gameInfo.setGameId(data.getGameId().toString());
        sudGameRedis.saveSudGameInfo(gameInfo);
        gameMap.put(gameInfo.getGameId(), gameInfo);
        sudGameRedis.savePlayerData(uid, data.getGameId().toString());
        // 发送创建游戏消息
        sendRoomSudGameOperateMsg(actorData, gameInfo, SudGameConstant.GAME_CREATE_MSG);
        logger.info("create sud game.creator={} gameId={} gameType={}", gameInfo.getSelfUid(), gameInfo.getGameId(), gameInfo.getGameType());
        // 用户创建游戏时上报数数
        doReportEvent(gameData);
        sendJoinGameMsg(data, actorDao.getActorDataFromCache(uid));
        return gameChange(data);
    }

    public void joinSudGame(String uid, String roomId, SudGameData gameData, HttpEnvData reqDTO, boolean isCreate) {
        if (RoomUtils.isVoiceRoom(roomId)) {
            // 8.33之前版本无法进行Ludo游戏
            if (gameData.getGameType() == SudGameConstant.LUDO_GAME && !AppVersionUtils.versionCheck(833, reqDTO.getVersioncode(), reqDTO.getOs())) {
                logger.error("Need to update to latest version. uid={} os={} version={}", uid, reqDTO.getOs(), reqDTO.getVersioncode());
                throw new GameException(GameHttpCode.NEED_UPGRADE);
            }
        }
        // 加入游戏的校验
        if (gameData.getPlayerList().size() >= gameData.getPlayerNumber()) {
            // 游戏人数已满无法加入
            logger.info("joinSudGame game play is full. roomId={} uid={} gameId:{} ", roomId, uid, gameData.getGameId().toString());
            throw new GameException(GameHttpCode.GAME_RUNNING);
        }
        // 校验玩家是否有未退出的游戏
        String inGameId = sudGameRedis.getPlayerData(uid);
        if (null != inGameId) {
            SudGameInfo sudGameInfo = gameMap.get(inGameId);
            if (null == sudGameInfo) {
                // 游戏不存在，是脏数据，需要删除
                sudGameRedis.removePlayerData(uid);
                logger.error("clear dirty data uid={} inGameId={}", uid, inGameId);
            } else if (GAME_MATCHING_LIST.contains(sudGameInfo.getStatus()) && !roomId.equals(sudGameInfo.getRoomId())) {
                // 用户创建或加入游戏后，如果游戏没有开始，用户主动退出房间、被踢出房间后，需退出游戏
                leaveRoomQuiteGame(uid, inGameId);
            } else {
                logger.info("player has in game uid={} isCreate={}", uid, null == gameData.getGameId());
                String gameName = sudGameConfig.getGameNameByGameType(sudGameInfo.getGameType(), reqDTO.getSlang());
                if (isCreate) {
                    throw new GameException(GameHttpCode.PLAYER_IN_GAME_C, new CreateSudGameVO(), new Object[]{gameName});
                } else {
                    throw new GameException(GameHttpCode.PLAYER_IN_GAME_J, gameDataTOVO(gameData), new Object[]{gameName});
                }
            }
        }
        SudGameConfigInfo gameConfigInfo = sudGameConfig.getSudGameInfoMap().get(gameData.getGameType());
        if (COIN == gameData.getCurrencyType()) {
            int currency = gameData.getCurrency();
            if (currency > 0) {
//                ActorData actorData = actorDao.getActorDataFromCache(uid);
//                if (actorData.getRobot() == 1) {
//                    logger.info("join sud game. actor is robot, skip deduct cost. uid={} gameId={}", uid, gameData.getGameId());
//                    return;
//                }

                boolean heartFlag = heartRecordDao.changeHeart(uid, -currency, gameConfigInfo.getFeeTitle(), gameConfigInfo.getFeeDesc());
                if (!heartFlag && RoomUtils.isGameRoom(roomId)) {
                    ActorData actorData = actorDao.getActorDataFromCache(uid);
                    if (actorData.getRobot() == 1) {
                        // 机器人进游戏房金币不足加金币
                        heartRecordDao.changeHeart(uid, currency, ADD_ROBOT_JOIN_GAME_FEE_TITLE, ADD_ROBOT_JOIN_GAME_FEE_DESC);
                        heartFlag = heartRecordDao.changeHeart(uid, -currency, gameConfigInfo.getFeeTitle(), gameConfigInfo.getFeeDesc());
                    }
                }
                if (!heartFlag) {
                    logger.info("player not enough heart. uid={} roomId={} change={}", uid, roomId, currency);
                    CreateSudGameVO createSudGameVO = new CreateSudGameVO();
                    createSudGameVO.setGameType(gameData.getGameType());
                    throw new GameException(GameHttpCode.NOT_ENOUGH_COIN, isCreate ? toCreateSudGameV2VO(createSudGameVO) : toCreateSudGameV2VO(gameDataTOVO(gameData)));

                }
            }
        } else if (DIAMOND == gameData.getCurrencyType()) {
            int currency = gameData.getCurrency();
            if (currency > 0) {
                MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
                moneyDetailReq.setRandomId();
                moneyDetailReq.setUid(uid);
                moneyDetailReq.setAtype(gameConfigInfo.getFeeActType());
                moneyDetailReq.setChanged(-currency);
                moneyDetailReq.setTitle(gameConfigInfo.getFeeTitle());
                moneyDetailReq.setDesc(gameConfigInfo.getFeeDesc());
                moneyDetailReq.setRoomId(roomId);
                ApiResult<String> reduceBeansResult = dataCenterService.reduceBeans(moneyDetailReq);
                if (!reduceBeansResult.isOk()) {
                    if (1 == reduceBeansResult.getCode().getCode()) {
                        logger.info(JSONObject.toJSONString(gameData));
                        throw new GameException(GameHttpCode.DIAMOND_NOT_ENOUGH, isCreate ? new CreateSudGameVO() : gameDataTOVO(gameData));
                    }
                    logger.error("join sud game reduce beans error, uid={} diamond={} msg={}", uid, -currency, reduceBeansResult.getCode().getMsg());
                    throw new GameException(HttpCode.SERVER_ERROR, isCreate ? new CreateSudGameVO() : gameDataTOVO(gameData));
                }
                sudGameRedis.incPrizePoolBeans(gameData.getGameId().toString(), currency);
                gameDiamondAlert(gameData.getGameId().toString(), gameData.getGameType(), -currency);
            }
        } else {
            logger.info("not support currency type. uid={} roomId={} currency type={}", uid, roomId, gameData.getCurrencyType());
            CreateSudGameVO createSudGameVO = new CreateSudGameVO();
            createSudGameVO.setGameType(gameData.getGameType());
            throw new GameException(GameHttpCode.SERVER_ERROR, isCreate ? toCreateSudGameV2VO(createSudGameVO) : toCreateSudGameV2VO(gameDataTOVO(gameData)));
        }
    }

    public void leaveRoomQuiteGame(String uid, String gameId) {
        SudGameData gameData = sudGameDao.findData(gameId);
        quitAndReturnCoin(uid, uid, gameData);
        gameChange(gameData);
        logger.info("actor has leave room. quit old game. uid={} gameId={}", uid, gameId);
    }

    /**
     * 检测是否存在其他sud游戏
     */
    public void checkExistOtherSudGame(HttpEnvData envData, String roomId, int gameType) {
        if (gameType == SudGameConstant.WOISSPY_GAME && truthOrDareV2Redis.getGameIdByRoomId(roomId) != null) {
            logger.info("There is Truth or Dare game playing in the room. roomId={} ", roomId);
            throw new GameException(GameHttpCode.EXIST_GAME, new Object[]{SLangType.ENGLISH == envData.getSlang() ? "Truth or Dare" : "الحقيقة أو التحدي"});
        }

        int runningGameType = sudGameRedis.getGameTypeByRoomId(roomId);
        if (runningGameType == 0 || runningGameType == gameType) {
            return;
        }
        SudGameConfigInfo gameConfigInfo = sudGameConfig.getSudGameInfoMap().get(gameType);
        int onlineVersion = gameConfigInfo.getOnlineVersion();
        if (!AppVersionUtils.versionCheck(onlineVersion, envData)) {
            logger.info("Please upgrade to the latest version to use it. roomId={} uid={} gameType={} version={}", roomId, envData.getUid(), gameType, envData.getVersioncode());
            throw new GameException(GameHttpCode.NEED_UPGRADE);
        }
        if (runningGameType == SudGameConstant.LUDO_GAME) {
            logger.info("There is Ludo game playing in the room. roomId={} ", roomId);
            throw new GameException(GameHttpCode.EXIST_LUDO_GAME);
        } else if (runningGameType == SudGameConstant.UMO_GAME) {
            logger.info("There is UMO game playing in the room. roomId={} ", roomId);
            throw new GameException(GameHttpCode.EXIST_UMO_GAME);
        } else if (runningGameType == SudGameConstant.MONSTER_CRUSH_GAME) {
            logger.info("There is Monster Crush game playing in the room. roomId={} ", roomId);
            throw new GameException(GameHttpCode.EXIST_MONSTER_CRUSH_GAME);
        } else if (runningGameType == SudGameConstant.DOMINO_GAME) {
            logger.info("There is Domino game playing in the room. roomId={} ", roomId);
            throw new GameException(GameHttpCode.EXIST_DOMINO_GAME);
        } else {
            logger.info("There is game playing in the room. roomId={} gameType={}", roomId, gameType);
            SudGameConfigInfo runningGameInfo = sudGameConfig.getSudGameInfoMap().get(runningGameType);
            throw new GameException(GameHttpCode.EXIST_GAME, new Object[]{SLangType.ENGLISH == envData.getSlang() ? runningGameInfo.getName() : runningGameInfo.getNameAr()});
        }
    }

    /**
     * 构建UNameObject
     */
    private UNameObject buildUNameObject(RoomActorDetailData actorData) {
        if (actorData == null) {
            return null;
        }
        String uid = actorData.getAid();
        UNameObject userInfo = new UNameObject();
        userInfo.setRid(actorData.getRid());
        userInfo.setName(actorData.getName());
        userInfo.setRole(actorData.getNewRole());
        userInfo.setHead(actorData.getHead());
        // 设置vip
        userInfo.setVip(actorData.getVipLevel());
        userInfo.setVipMedal(actorData.getVipMedal());
        // 设置徽章
        userInfo.setBadgeList(actorData.getBadgeList());
        userInfo.setLevel(actorData.getLevel());
        // 设置气泡
        userInfo.setBid(actorData.getBubbleId());
        userInfo.setIdentify(actorData.getIdentify());
        userInfo.setUid(uid);
        userInfo.setIsNewUser(actorData.getIsNewUser());
        return userInfo;
    }

    /**
     * 加入游戏
     */
    public CreateSudGameVO joinGame(SudGameDTO reqDTO) {
        String uid = reqDTO.getUid();
        SudGameData data = sudGameDao.findData(reqDTO.getGameId());
        if (data == null || data.getStatus() == SudGameConstant.GAME_CLOSED
                || data.getStatus() == SudGameConstant.GAME_FINISH) {
            throw new GameException(GameHttpCode.GAME_OVER);
        }
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData == null) {
            logger.error("can not find actor data, uid ={}", uid);
            throw new GameException(HttpCode.PARAM_ERROR);
        }

        checkJoinGame(data, reqDTO, uid, actorData.getRobot());
        CreateSudGameVO createSudGameVO = playerJoinGame(reqDTO, data, actorData);
        createSudGameVO.setHandleType(1);
        return createSudGameVO;
    }

    public CreateSudGameVO changeTeam(SudGameDTO reqDTO) {
        if (null == reqDTO.getIndex()) {
            throw new GameException(GameHttpCode.PARAM_ERROR);
        }
        String uid = reqDTO.getUid();
        int index = reqDTO.getIndex();
        SudGameData data = sudGameDao.findData(reqDTO.getGameId());
        if (data == null || data.getStatus() == SudGameConstant.GAME_CLOSED || data.getStatus() == SudGameConstant.GAME_FINISH) {
            throw new GameException(GameHttpCode.GAME_OVER);
        }
        List<SudGamePlayerData> playerList = data.getPlayerList();
        SudGamePlayerData playerData = null;
        boolean conflict = false;
        for (SudGamePlayerData sudGamePlayerData : playerList) {
            if (sudGamePlayerData.getUid().equals(uid)) {
                playerData = sudGamePlayerData;
            }
            if (sudGamePlayerData.getIndex() == index) {
                conflict = true;
            }
        }
        if (null == playerData) {
            if (RoomUtils.isGameRoom(data.getRoomId())) {
                return joinGame(reqDTO);
            } else {
                throw new GameException(HttpCode.AUTH_ERROR);
            }
        }
        if (conflict) {
            throw new GameException(GameHttpCode.PLAYER_FULL);
        }
        playerData.setIndex(index);
        sudGameDao.save(data);
        // 更新redis里的游戏信息
        SudGameInfo gameInfo = new SudGameInfo();
        BeanUtils.copyProperties(data, gameInfo);
        gameInfo.setGameId(data.getGameId().toString());
        sudGameRedis.updateSudGameInfo(gameInfo);
        return gameChange(data);
    }

    /**
     * 切换游戏模式
     */
    public CreateSudGameVO changeMode(SudGameDTO reqDTO) {
        if (null == reqDTO.getRule()) {
            throw new GameException(GameHttpCode.PARAM_ERROR);
        }
        String uid = reqDTO.getUid();
        SudGameData data = sudGameDao.findData(reqDTO.getGameId());
        if (data == null || data.getStatus() == SudGameConstant.GAME_CLOSED || data.getStatus() == SudGameConstant.GAME_FINISH) {
            throw new GameException(GameHttpCode.GAME_OVER);
        }
        data.setRule(reqDTO.getRule());
        sudGameDao.save(data);
        // 更新redis里的游戏信息
        SudGameInfo gameInfo = new SudGameInfo();
        BeanUtils.copyProperties(data, gameInfo);
        gameInfo.setGameId(data.getGameId().toString());
        sudGameRedis.updateSudGameInfo(gameInfo);
        return gameChange(data);
    }


    /**
     * 加入游戏校验
     */
    private void checkJoinGame(SudGameData gameData, SudGameDTO reqDTO, String uid, int robot) {
        String roomId = gameData.getRoomId();
        if (gameData.getStatus() == SudGameConstant.GAME_PROCESSING) {
            // 游戏正在进行无法加入
            logger.info("checkJoinGame game is running. roomId={} uid={} gameId:{}", roomId, uid, gameData.getGameId().toString());
            throw new GameException(GameHttpCode.GAME_RUNNING, gameDataTOVO(gameData));
        }
        if (gameData.getStatus() == SudGameConstant.GAME_CLOSED) {
            // 游戏已结束无法加入
            logger.info("game is closed. roomId={} uid={}", roomId, uid);
            throw new GameException(GameHttpCode.GAME_CLOSED, gameDataTOVO(gameData));
        }
        if (RoomUtils.isVoiceRoom(roomId)) {
            // 8361版本之前的不能加入Ludo入参费为钻石的局
            if (gameData.getGameType() == SudGameConstant.LUDO_GAME && gameData.getCurrencyType() == DIAMOND) {
                if (!AppVersionUtils.versionCheck(8361, reqDTO)) {
                    logger.info("Please upgrade to the latest version to use it. roomId={} uid={}", roomId, uid);
                    throw new GameException(GameHttpCode.NEED_UPGRADE, gameDataTOVO(gameData));
                }
            }
        }
        // 校验是否已经加入游戏
        List<SudGamePlayerData> playerList = gameData.getPlayerList();
        if (RoomUtils.isVoiceRoom(roomId)) {
            if (!CollectionUtils.isEmpty(playerList) && !gameData.getSelfUid().equals(uid)) {
                List<String> playerUidList = playerList.stream().map(SudGamePlayerData::getUid).collect(Collectors.toList());
                if (playerUidList.contains(uid)) {
                    logger.info("has joined the game. uid={} roomId={}", uid, roomId);
                    throw new GameException(GameHttpCode.YOU_HAVE_JOINED_THE_GAME, gameDataTOVO(gameData));
                }
            }
        } else {
            // 游戏房游戏创建者也可以退出再加入
            if (!CollectionUtils.isEmpty(playerList)) {
                List<String> playerUidList = playerList.stream().map(SudGamePlayerData::getUid).collect(Collectors.toList());
                if (playerUidList.contains(uid)) {
                    logger.info("has joined the game. uid={} roomId={}", uid, roomId);
                    throw new GameException(GameHttpCode.YOU_HAVE_JOINED_THE_GAME, gameDataTOVO(gameData));
                }
            } else {
                if (robot == 1) {
                    // 机器人不能加入没有用户的游戏
                    logger.info("robot can not joined the no user game. uid={} roomId={}", uid, roomId);
                    throw new GameException(GameHttpCode.GAME_RUNNING, gameDataTOVO(gameData));
                }
            }
        }
        // 加入游戏的校验
        if (gameData.getPlayerList().size() >= gameData.getPlayerNumber()) {
            // 游戏人数已满无法加入
            logger.info("game play is full. roomId={} uid={}", roomId, uid);
            throw new GameException(GameHttpCode.PLAYERS_NUM_IS_FULL, gameDataTOVO(gameData));
        }

        // 谁是卧底游戏需判断是否在麦位上
        if (gameData.getGameType() == SudGameConstant.WOISSPY_GAME) {
            RoomMicListVo roomMicVO = roomMicRedis.getRoomMicFromRedis(roomId);
            if (roomMicVO == null || CollectionUtils.isEmpty(roomMicVO.getList()) || roomMicVO.getList().stream().noneMatch(item -> item.getStatus() == 1 && item.getUser().getAid().equals(uid))){
                throw new GameException(GameHttpCode.PLEASE_UP_MIC_FIRST, gameDataTOVO(gameData));
            }
        }
    }

    /**
     * 加入游戏
     */
    public CreateSudGameVO playerJoinGame(SudGameDTO reqDTO, SudGameData data, ActorData actorData) {
        joinSudGame(reqDTO.getUid(), reqDTO.getRoomId(), data, reqDTO, false);
        // 更新游戏信息
        SudGamePlayerData playerData = buildSudGamePlayerData(actorData);
        List<SudGamePlayerData> playerList = data.getPlayerList();
        boolean isGameRoom = RoomUtils.isGameRoom(data.getRoomId());
        if (isGameRoom) {
            if (data.getStatus() == 0 || CollectionUtils.isEmpty(playerList)) {
                data.setStatus(SudGameConstant.GAME_MATCHING);
            }
        } else {
            data.setStatus(SudGameConstant.GAME_MATCHING);
        }

        if (CollectionUtils.isEmpty(playerList)) {
            playerList = new ArrayList<>();
        }
        Set<Integer> indexSet = CollectionUtil.listToPropertySet(playerList, SudGamePlayerData::getIndex);
        if (null != reqDTO.getIndex() && !indexSet.contains(reqDTO.getIndex())) {
            // 优先处理客户端传值
            playerData.setIndex(reqDTO.getIndex());
        } else {
            // 从小到大获取index
            for (int i = 0; i < data.getPlayerNumber(); i++) {
                if (!indexSet.contains(i)) {
                    playerData.setIndex(i);
                    break;
                }
            }
        }
        playerList.add(playerData);
        if (isGameRoom) {
            List<String> uidList = playerList.stream().map(SudGamePlayerData::getUid).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(uidList)) {
                // 更新队长
                if (!uidList.contains(data.getLeaderUid())) {
                    data.setLeaderUid(uidList.get(0));
                }
            }
            if (playerList.size() == data.getPlayerNumber()) {
                // 已达到满员了，游戏状态改为不匹配
                data.setStatus(SudGameConstant.GAME_MATCHING_PAUSE);
            }
        }

        data.setPlayerList(playerList);
        sudGameDao.save(data);
        // 更新redis里的游戏信息
        SudGameInfo gameInfo = new SudGameInfo();
        BeanUtils.copyProperties(data, gameInfo);
        gameInfo.setGameId(data.getGameId().toString());
        sudGameRedis.updateSudGameInfo(gameInfo);
        sudGameRedis.savePlayerData(reqDTO.getUid(), data.getGameId().toString());
        logger.info("join sud game. uid={} gameId={} gameType={}", reqDTO.getUid(), gameInfo.getGameId(), gameInfo.getGameType());
        sendJoinGameMsg(data, actorData);
        if (isGameRoom) {
            sendMicChange(reqDTO.getRoomId(), reqDTO.getUid());
        }
        return gameChange(data);
    }

    /**
     * 开始游戏
     */
    public CreateSudGameVO startGame(SudGameDTO reqDTO) {
        String uid = reqDTO.getUid();
        String roomId = reqDTO.getRoomId();
        SudGameData data = sudGameDao.findData(reqDTO.getGameId());
        if (data == null || data.getStatus() == SudGameConstant.GAME_CLOSED || data.getStatus() == SudGameConstant.GAME_FINISH) {
            throw new GameException(GameHttpCode.GAME_CLOSED);
        }
        int playerNum = data.getPlayerList().size();
        if (SudGameConstant.LUDO_GAME == data.getGameType() || SudGameConstant.DOMINO_GAME == data.getGameType()) {
            if (playerNum < 2 || playerNum > 4) {
                // ludo游戏大于4或小于2人无法开始
                logger.error("wrong number of players.uid={} roomId={} gameType={} playerNumber={}", uid, roomId, data.getGameType(), playerNum);
                throw new GameException(GameHttpCode.WRONG_NUMBER_OF_PLAYERS);
            }
        } else if (SudGameConstant.CARROM_POOL_GAME == data.getGameType() && 3 == data.getRule().getIntValue("mode_ex") && playerNum < 4) {
            // 克罗姆 2v2需要4个玩家
            throw new GameException(GameHttpCode.WRONG_NUMBER_OF_PLAYERS);
        } else if (data.getGameType() == SudGameConstant.WOISSPY_GAME && (playerNum < 4 || playerNum > 9)) {
            logger.error("who is spy wrong number of players.uid={} roomId={} gameType={} playerNumber={}", uid, roomId, data.getGameType(), playerNum);
            throw new GameException(GameHttpCode.WRONG_NUMBER_OF_PLAYERS);
        } else {
            if (data.getGameType() != SudGameConstant.WOISSPY_GAME && (playerNum < 2 || playerNum > 6)) {
                // UMO和Monster crush游戏大于6或小于2人无法开始
                logger.error("wrong number of players.uid={} roomId={} gameType={} playerNumber={}", uid, roomId, data.getGameType(), playerNum);
                throw new GameException(GameHttpCode.WRONG_NUMBER_OF_PLAYERS);
            }
        }
        if (data.getGameType() == SudGameConstant.WOISSPY_GAME) {
            whoIsSpyStartMicStatus(data);
        }
        logger.info("prepare startGame gameId:{}  data:{}", data.getGameId().toString(), JSONObject.toJSONString(data));
       // if (RoomUtils.isGameRoom(roomId)) {
       //     if (data.getStatus() == SudGameConstant.GAME_PROCESSING) {
       //         throw new GameException(GameHttpCode.GAME_START_ERROR);
       //     }
       // }

        startSudGame(data);
        // 补充玩家基本信息
        setPlayersBasicInfo(data);
        data.setStartTime(DateHelper.getNowSeconds());
        data.setStatus(SudGameConstant.GAME_PROCESSING);
        data.setTotalCurrency(playerNum * data.getCurrency());
        // 更新游戏信息
        sudGameDao.save(data);
        // 更新redis里的游戏相关信息
        SudGameInfo gameInfo = new SudGameInfo();
        BeanUtils.copyProperties(data, gameInfo);
        gameInfo.setGameId(data.getGameId().toString());
        sudGameRedis.updateSudGameInfo(gameInfo);
        gameMap.put(data.getGameId().toString(), gameInfo);

        logger.info("start sud game. uid={} gameId={} gameType={}", uid, gameInfo.getGameId(), gameInfo.getGameType());
        return gameChange(data);
    }

    private void whoIsSpyStartMicStatus(SudGameData gameInfo){
        try {
            String roomId = gameInfo.getRoomId();
            RoomMicListVo roomMicVO = roomMicRedis.getRoomMicFromRedis(roomId);
            if (roomMicVO == null || CollectionUtils.isEmpty(roomMicVO.getList())) {
                return;
            }
            sudGameRedis.setWhoIsSpyMicStatus(roomId, JSONObject.toJSONString(roomMicVO));
            for (RoomMicInfoObject roomMicInfoObject : roomMicVO.getList()) {
                int muteStatus = roomMicInfoObject.getMute();
                MuteMicDTO muteMicDTO = new MuteMicDTO();
                muteMicDTO.setIndex(roomMicInfoObject.getIndex());
                muteMicDTO.setRoomId(roomId);
                muteMicDTO.setUid(RoomUtils.getRoomHostId(roomId));
                if (muteStatus == 0) {
                    muteMicDTO.setOpType(1);
                    iRoomService.muteMic(muteMicDTO);
                }
            }
        }catch (Exception e){
            logger.error("operationMicStatus e={}", e.getMessage(), e);
        }
    }


    private GetUserInfoVO getUserInfoVO(String uid, int index) {
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        return GetUserInfoVO.builder()
                .uid(actorData.getUid())
                .isAi(actorData.getRobot() == 1 ? 1 : 0)
                .aiLevel(3)
                .nickName(actorData.getName())
                .gender(actorData.getFb_gender() == 1 ? "male" : "female,")
                .avatarUrl(ImageUrlGenerator.generateSudGameUserUrl(actorData.getHead(), vipInfoDao.getIntVipLevelFromCache(actorData.getUid())))
                .index(index)
                .build();
    }

    private void startSudGame(SudGameData game) {
        String gameId = game.getGameId().toString();
        List<GetUserInfoVO> userInfos = new ArrayList<>();
        for (SudGamePlayerData playerData : game.getPlayerList()) {
            GetUserInfoVO userInfoVO = getUserInfoVO(playerData.getUid(), playerData.getIndex());
            if (game.getGameType() == SudGameConstant.CARROM_POOL_GAME) {
                userInfoVO.setAiLevel(2);
            }

            if (game.getGameType() == SudGameConstant.BILLIARD_GAME) {
                userInfoVO.setAiLevel(1);
            }
            if (game.getGameType() == SudGameConstant.JACKAROO_GAME) {
                userInfoVO.setAiLevel(2);
            }

            userInfos.add(userInfoVO);
        }
        userInfos.sort(Comparator.comparing(GetUserInfoVO::getIsAi).thenComparingInt(GetUserInfoVO::getIndex));
        game.getPlayerList().sort(Comparator.comparing(SudGamePlayerData::getRobot).thenComparingInt(SudGamePlayerData::getIndex));
        ApiResult<BaseVO<?>> apiResult = sudGameApi.pushEvent(EventDTO.QuickStartEvent(sudGameConfig.getGameIdByGameType(game.getGameType()), gameId, userInfos, game.getRule()));
        if (apiResult.isError()) {
            logger.error("start game error, roomId={} gameId={} msg={}", game.getRoomId(), gameId, apiResult.getData());
            throw new GameException(GameHttpCode.FAILURE, gameDataTOVO(game));
        }
    }

    public void sendGameResultMsg(String roomId, MarsServerMsg msg, boolean ack, int gameType) {
        int onlineVersion = sudGameConfig.getSudGameInfoMap().get(gameType).getOnlineVersion();
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                roomWebSender.sendRoomWebMsg(roomId, null, msg, ack, onlineVersion);
            }
        });
    }

    public CreateSudGameVO gameDataTOVO(SudGameData gameData) {
        CreateSudGameVO vo = new CreateSudGameVO();
        BeanUtils.copyProperties(gameData, vo);
        vo.setGameId(gameData.getGameId().toString());
        return vo;
    }

    public CreateSudGameVO gameChange(SudGameData gameData) {
        CreateSudGameVO vo = gameDataTOVO(gameData);
        int onlineVersion = sudGameConfig.getSudGameInfoMap().get(gameData.getGameType()).getOnlineVersion();
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                roomWebSender.sendRoomWebMsg(gameData.getRoomId(), null, vo.toMarsMsg(), true, onlineVersion);
            }
        });
        return vo;
    }

    /**
     * 创建游戏埋点
     */
    private void doReportEvent(SudGameData gameData) {
        CreateGameLogEvent event = new CreateGameLogEvent();
        event.setUid(gameData.getSelfUid());
        event.setIs_robot(0);
        event.setRoom_id(gameData.getRoomId());
        event.setGame_id(gameData.getGameId().toString());
        event.setGame_type(getEventGameType(gameData.getGameType()));
        event.setCost_type(gameData.getCurrency() == 0 ? 0 : (gameData.getCurrencyType() == COIN ? 1 : 2));
        event.setCost_number(gameData.getCurrency());
        event.setCtime(DateHelper.getNowSeconds());
        eventReport.track(new com.quhong.analysis.EventDTO(event));
    }

    public void asyncPushSudEvent(EventDTO dto) {
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                sudGameApi.pushEvent(dto);
            }
        });
    }

    /**
     * 退出游戏或踢出游戏
     * 玩家手动退出时aid和uid相同
     */
    public CreateSudGameVO quitGame(SudGameDTO reqDTO) {
        String uid = reqDTO.getUid();
        String aid = reqDTO.getAid();
        SudGameData gameData = sudGameDao.findData(reqDTO.getGameId());
        if (null == gameData) {
            logger.info("cannot find gameData gameId={}", reqDTO.getGameId());
            throw new GameException(GameHttpCode.FAILURE);
        }
        boolean isGameRoom = RoomUtils.isGameRoom(gameData.getRoomId());
        if (isGameRoom && !uid.equals(aid)) {
            // 游戏房不能主动请别人离开游戏
            logger.info("game room cannot quit game other gameId={} roomId={} uid={} aid={}",
                    reqDTO.getGameId(), reqDTO.getRoomId(), uid, aid);
            throw new GameException(HttpCode.AUTH_ERROR);
        }
        if (gameData.getStatus() == SudGameConstant.GAME_PROCESSING) {
            escapeGame(gameData, uid, reqDTO.getQuitType() <= 0 ? 1 : reqDTO.getQuitType());
        } else if (GAME_MATCHING_LIST.contains(gameData.getStatus())) {
            // 有入场费的游戏需要返回入场费
            quitAndReturnCoin(uid, aid, gameData);
        }
        CreateSudGameVO createSudGameVO = gameChange(gameData);
        createSudGameVO.setHandleType(2);
        return createSudGameVO;
    }

    /**
     * 游戏中逃跑
     *
     * @param quitType 1主动逃跑 2被房间管理员踢出房间逃跑
     */
    public void escapeGame(SudGameData gameData, String uid, int quitType) {
        for (SudGamePlayerData playerData : gameData.getPlayerList()) {
            if (playerData.getUid().equals(uid) && playerData.getIsQuit() <= 0) {
                asyncPushSudEvent(EventDTO.GameEndEvent(sudGameConfig.getGameIdByGameType(gameData.getGameType()), gameData.getGameId().toString(), uid));
                playerData.setIsQuit(quitType);
                // 逃跑后可以去别的房间玩游戏
                sudGameRedis.removePlayerData(uid);
                sudGameDao.save(gameData);
                logger.info("escapeGame success uid:{} roomId:{} gameId:{} quitType:{}",
                        uid, gameData.getRoomId(), gameData.getGameId().toString(), quitType);
                break;
            }
        }
    }


    /**
     * 游戏房间
     * 测试使用-sud游戏房间清理
     */
    public void sudGameRoomClear(SudGameDTO req) {
        String roomId = req.getRoomId();
        if (StringUtils.isEmpty(roomId)) {
            throw new GameException(HttpCode.PARAM_ERROR);
        }
        SudGameSimpleInfo sudGameInfo = sudGameRedis.getSudGameSimpleInfo(roomId);
        if (sudGameInfo == null || sudGameInfo.getGameType() == 0) {
            logger.info("sud game not found roomId={}", roomId);
            throw new GameException(HttpCode.PARAM_ERROR);
        }
        asyncPushSudEvent(EventDTO.RoomClearEvent(sudGameConfig.getGameIdByGameType(sudGameInfo.getGameType()), sudGameInfo.getGameId()));
    }

    public void quitAndReturnCoin(String uid, String aid, SudGameData gameData) {
        boolean isGameRoom = RoomUtils.isGameRoom(gameData.getRoomId());
        // 主动退出
        if (uid.equals(aid)) {
            if (gameData.getSelfUid().equals(uid)) {
                if (isGameRoom) {
                    if (gameData.getPlayerList().stream().anyMatch(a -> a.getUid().equals(aid))) {
                        // 非游戏创建者退出游戏
                        quitOrKickOut(uid, gameData);
                        // 返回入场费
                        returnGameCurrency(gameData, uid);

                        sendQuitGameMsg(actorDao.getActorDataFromCache(uid), gameData.getRoomId());
                    } else {
                        logger.error("repeat return game fee!!! uid={}", aid);
                    }
                } else {
                    // 游戏创建者退出游戏，解散游戏，并返还游戏币
                    // 解散游戏
                    removeGame(gameData);
                }
            } else {
                if (gameData.getPlayerList().stream().anyMatch(a -> a.getUid().equals(aid))) {
                    // 非游戏创建者退出游戏
                    quitOrKickOut(uid, gameData);
                    // 返回入场费
                    returnGameCurrency(gameData, uid);
                    sendQuitGameMsg(actorDao.getActorDataFromCache(uid), gameData.getRoomId());
                } else {
                    logger.error("repeat return game fee!!! uid={}", aid);
                }
            }
        } else {
            // 被踢(被游戏创建者踢出游戏)
            if (!gameData.getSelfUid().equals(uid)) {
                if (isGameRoom) {
                    if (gameData.getPlayerList().stream().anyMatch(a -> a.getUid().equals(aid))) {
                        quitOrKickOut(aid, gameData);
                        // 返回入场费
                        returnGameCurrency(gameData, aid);
                        // 发送玩家被踢消息
                        sendKickOutMsg(gameData.getRoomId(), uid, aid);
                        sendQuitGameMsg(actorDao.getActorDataFromCache(aid), gameData.getRoomId());
                    } else {
                        logger.error("repeat return game fee!!! uid={}", aid);
                    }
                } else {
                    logger.info("cannot kick out player. no right to operate. uid={} aid={}", uid, aid);
                    throw new GameException(HttpCode.AUTH_ERROR);
                }
            }
            if (gameData.getPlayerList().stream().anyMatch(a -> a.getUid().equals(aid))) {
                quitOrKickOut(aid, gameData);
                // 返回入场费
                returnGameCurrency(gameData, aid);
                // 发送玩家被踢消息
                sendKickOutMsg(gameData.getRoomId(), uid, aid);
                sendQuitGameMsg(actorDao.getActorDataFromCache(aid), gameData.getRoomId());
            } else {
                logger.error("repeat return game fee!!! uid={}", aid);
            }
        }
    }

    public SudGameData checkGame(String roomId) {
        String gameId = sudGameRedis.getGameIdByRoomId(roomId);
        if (StringUtils.isEmpty(gameId)) {
            return null;
        }
        return sudGameDao.findData(gameId);
    }

    /**
     * 正常结束游戏
     */
    public void finishGame(SudGameData data) {
        gameMap.remove(data.getGameId().toString());
        String gameId = data.getGameId().toString();
        data.setStatus(SudGameConstant.GAME_FINISH);
        data.setEndTime(DateHelper.getNowSeconds());
        sudGameDao.save(data);
        // 删除redis里的游戏信息
        sudGameRedis.removeGameInfo(gameId, data.getRoomId());
        data.getPlayerList().forEach(k -> {
            // 移除玩家在游戏的redis
            sudGameRedis.removePlayerData(k.getUid());
        });
        gameChange(data);
        if (RoomUtils.isGameRoom(data.getRoomId())) {
            sendMicChange(data.getRoomId(), data.getLeaderUid());
        }
        logger.info("finish sud game. gameId={} gameType={}", data.getGameId(), data.getGameType());
    }

    /**
     * 设置玩家基本信息
     */
    private void setPlayersBasicInfo(SudGameData data) {
        if (CollectionUtils.isEmpty(data.getPlayerList())) {
            return;
        }
        for (SudGamePlayerData playerData : data.getPlayerList()) {
            ActorData actorData = actorDao.getActorDataFromCache(playerData.getUid());
            if (actorData == null) {
                logger.error("Not find player info. uid={}", playerData.getUid());
                continue;
            }
            playerData.setName(actorData.getName());
            playerData.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
            playerData.setRid(actorData.getRid());
            playerData.setGender(actorData.getFb_gender());
//            if (SudGameConstant.BILLIARD_GAME == data.getGameType()) {
                commonTaskService.sendCommonTaskMq
                        (new CommonMqTopicData(playerData.getUid(), data.getRoomId(), "", "",
                                CommonMqTaskConstant.PLAY_SUD_GAME, data.getGameType()));
//            }
            gameRoomRedis.updateLastPlayGameTime(playerData.getUid(), String.valueOf(data.getGameType()));
        }
    }

    /**
     * 构建游戏信息
     */
    public SudGameData buildSudGameData(CreateSudGameDTO reqDTO, RoomActorDetailData actorData) {
        SudGameData data = new SudGameData();
        data.setGameType(reqDTO.getGameType());
        data.setGameName(sudGameConfig.getGameNameByGameType(reqDTO.getGameType(), SLangType.ENGLISH));
        data.setGameNameAr(sudGameConfig.getGameNameByGameType(reqDTO.getGameType(), SLangType.ARABIC));
        // UMO和消消乐游戏不支持设置玩家数量(2-6)
        data.setPlayerNumber(reqDTO.getGameType() == SudGameConstant.UMO_GAME ||
                reqDTO.getGameType() == SudGameConstant.MONSTER_CRUSH_GAME ? 6 : reqDTO.getPlayerNumber());
        data.setRule(reqDTO.getRule());
        data.setStatus(SudGameConstant.GAME_MATCHING);
        data.setRoomId(reqDTO.getRoomId());
        data.setSelfUid(reqDTO.getUid());
        data.setCurrencyType(reqDTO.getCurrencyType());
        if (RoomUtils.isGameRoom(reqDTO.getRoomId())) {
            data.setCurrency(reqDTO.getCurrency());
        } else {
            if (AppVersionUtils.versionCheck(850, reqDTO)) {
                List<Integer> newCurrencyList = sudGameConfig.getNewCurrencyList(reqDTO.getGameType(), reqDTO.getCurrencyType());
                if (!newCurrencyList.contains(reqDTO.getCurrency()) || reqDTO.getCurrency() < 0) {
                    logger.error("currency param error. currencyType={} currency={}", reqDTO.getCurrencyType(), reqDTO.getCurrency());
                    throw new GameException(GameHttpCode.PARAM_ERROR);
                }
                data.setCurrency(reqDTO.getCurrency());
            } else {
                data.setCurrency(sudGameConfig.getCurrencyByGameTypeAndId(reqDTO.getGameType(), reqDTO.getCurrencyId()));
            }
        }
        data.setCreateTime(DateHelper.getNowSeconds());
        List<SudGamePlayerData> playerList = new ArrayList<>();
        playerList.add(buildSudGamePlayerData(actorData));
        data.setPlayerList(playerList);
        data.setGameRoomType(RoomUtils.isGameRoom(reqDTO.getRoomId()) ? SudGameConstant.GAME_ROOM : SudGameConstant.VOICE_ROOM);
        return data;
    }

    /**
     * 构建玩家信息
     */
    private SudGamePlayerData buildSudGamePlayerData(ActorData actorData) {
        SudGamePlayerData playerData = new SudGamePlayerData();
        playerData.setUid(actorData.getUid());
        playerData.setName(actorData.getName());
        playerData.setGender(actorData.getFb_gender());
        playerData.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead(), vipInfoDao.getIntVipLevelFromCache(actorData.getUid())));
        playerData.setRid(actorData.getRid());
        playerData.setRobot(actorData.getRobot() == 1 ? 1 : 0);
        return playerData;
    }

    private SudGamePlayerData buildSudGamePlayerData(RoomActorDetailData actorData) {
        SudGamePlayerData playerData = new SudGamePlayerData();
        playerData.setUid(actorData.getAid());
        playerData.setName(actorData.getName());
        playerData.setGender(actorData.getGender());
        playerData.setHead(actorData.getHead());
        playerData.setRid(actorData.getRid());
        playerData.setRobot(0);
        playerData.setIndex(0);
        return playerData;
    }

    /**
     * 一局游戏结束，更新游戏状态
     */
    public void updateGameStatus(String gameId) {
        // 更新游戏信息
        SudGameData data = sudGameDao.findData(gameId);
        data.setStatus(SudGameConstant.GAME_MATCHING);
        sudGameDao.save(data);
        // 更新redis里的游戏信息
        SudGameInfo gameInfo = new SudGameInfo();
        BeanUtils.copyProperties(data, gameInfo);
        gameInfo.setGameId(data.getGameId().toString());
        sudGameRedis.updateSudGameInfo(gameInfo);
        gameMap.put(data.getGameId().toString(), gameInfo);
    }

    /**
     * 退出游戏或踢出游戏，并退还游戏币
     */
    private boolean quitOrKickOut(String uid, SudGameData gameData) {
        boolean isKick = gameData.getPlayerList().removeIf(playerData -> playerData.getUid().equals(uid));
        if (RoomUtils.isGameRoom(gameData.getRoomId())) {
            // 更新队长或者游戏状态
            List<String> uidList = gameData.getPlayerList().stream().map(SudGamePlayerData::getUid).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(uidList)) {
                //当局没有玩家了
                gameData.setLeaderUid(SudGameConstant.EMPTY_LEADER_UID);
                if (GAME_MATCHING_LIST.contains(gameData.getStatus())) {
                    gameData.setStatus(SudGameConstant.GAME_MATCHING_PAUSE);
                }
//                gameChange(gameData);
            } else {
                if (uid.equals(gameData.getLeaderUid())) {
                    // 队长离开了
                    String newLeader = dealNewLeaderUid(gameData, uidList);
                    gameData.setLeaderUid(newLeader);
                    if (GAME_MATCHING_LIST.contains(gameData.getStatus())) {
                        gameData.setStatus(SudGameConstant.GAME_MATCHING_PAUSE);
                    }
//                    gameChange(gameData);
                }
            }
        }
        sudGameDao.save(gameData);
        // 更新redis里的游戏信息
        SudGameInfo gameInfo = new SudGameInfo();
        BeanUtils.copyProperties(gameData, gameInfo);
        gameInfo.setGameId(gameData.getGameId().toString());
        sudGameRedis.updateSudGameInfo(gameInfo);
        sudGameRedis.removePlayerData(uid);
        if (RoomUtils.isGameRoom(gameData.getRoomId())) {
            sendMicChange(gameData.getRoomId(), uid);
        }
        logger.info("quit sud game. uid={} gameId={} gameType={}", uid, gameInfo.getGameId(), gameInfo.getGameType());
        return isKick;
    }

    private String dealNewLeaderUid(SudGameData gameData, List<String> uidList) {
        List<SudGamePlayerData> humanList = gameData.getPlayerList().stream().filter(item -> item.getRobot() == 0).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(humanList)) {
            return humanList.get(0).getUid();
        } else {
            return uidList.get(0);
            // 剩下的全是机器人,把机器人全部踢出去
//            Iterator<SudGamePlayerData> iterator = gameData.getPlayerList().iterator();
//            while (iterator.hasNext()) {
//                SudGamePlayerData playerData = iterator.next();
//                String aid = playerData.getUid();
//                sudGameRedis.removePlayerData(aid);
//                // 返回入场费
//                returnGameCurrency(gameData, aid);
//                sendQuitGameMsg(actorDao.getActorDataFromCache(aid), gameData.getRoomId());
//                iterator.remove();
//            }
        }

    }

    /**
     * 发送玩家被踢消息
     */
    private void sendKickOutMsg(String roomId, String uid, String aid) {
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                RoomLudoKitOutMsg msg = new RoomLudoKitOutMsg();
                msg.setAid(uid);
                roomWebSender.sendPlayerWebMsg(roomId, uid, aid, msg, true);
            }
        });
    }

    /**
     * Ludo创建游戏者解散游戏
     */
    private void removeGame(SudGameData gameData) {
        String uid = gameData.getSelfUid();
        String gameId = gameData.getGameId().toString();
        String roomId = gameData.getRoomId();
        for (SudGamePlayerData playerData : gameData.getPlayerList()) {
            // 返回入场费
            returnGameCurrency(gameData, playerData.getUid());
            sudGameRedis.removePlayerData(playerData.getUid());
        }
        gameData.setStatus(SudGameConstant.GAME_CLOSED);
        sudGameDao.save(gameData);
        SudGameInfo gameInfo = new SudGameInfo();
        BeanUtils.copyProperties(gameData, gameInfo);
        gameInfo.setGameId(gameId);
        // 删除redis里的游戏信息
        sudGameRedis.removeGameInfo(gameId, roomId);
        gameMap.remove(gameId);
        // 结束游戏消息
        RoomActorDetailData actorData = roomActorCache.getData(roomId, uid, false);
        sendRoomSudGameOperateMsg(actorData, gameInfo, SudGameConstant.GAME_CLOSED_MSG);
        logger.info("end sud game. uid={} leaderUid={}  gameId={} gameType={}", uid, gameData.getLeaderUid(), gameId, gameData.getGameType());
    }

    /**
     * 返回入场费
     */
    public void returnGameCurrency(SudGameData gameInfo, String uid) {
        // 返还游戏币
        int currency = gameInfo.getCurrency();
        if (currency == 0) {
            return;
        }
        SudGameConfigInfo gameConfigInfo = sudGameConfig.getSudGameInfoMap().get(gameInfo.getGameType());
        if (COIN == gameInfo.getCurrencyType()) {
            boolean success = heartRecordDao.changeHeart(uid, currency, gameConfigInfo.getFeeReturnTitle(), gameConfigInfo.getFeeReturnDesc());
            if (success) {
                logger.info("sud game reward coin return uid={} coin={} gameId={}", uid, currency, gameInfo.getGameId());
            } else {
                logger.error("sud game reward coin return error uid={} coin={} gameId={}", uid, currency, gameInfo.getGameId());
            }
        } else if (DIAMOND == gameInfo.getCurrencyType()) {
            if (sudGameRedis.incPrizePoolBeans(gameInfo.getGameId().toString(), -currency) < 0) {
                sudGameRedis.setAbnormalUser(gameInfo.getGameType(), uid);
                logger.error("Not enough diamonds in the prize pool. gameId={} uid={} change={}", gameInfo.getGameId(), uid, currency);
                return;
            }
            MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
            moneyDetailReq.setRandomId();
            moneyDetailReq.setUid(uid);
            moneyDetailReq.setAtype(gameConfigInfo.getFeeReturnActType());
            moneyDetailReq.setChanged(currency);
            moneyDetailReq.setTitle(gameConfigInfo.getFeeReturnTitle());
            moneyDetailReq.setDesc(gameConfigInfo.getFeeReturnDesc());
            moneyDetailReq.setRoomId(gameInfo.getRoomId());
            mqSenderService.asyncChargeDiamonds(moneyDetailReq);
            gameDiamondAlert(gameInfo.getGameId().toString(), gameInfo.getGameType(), currency);
            logger.info("sud game reward diamond return uid={} diamond={} gameId={}", uid, currency, gameInfo.getGameId());
        }
    }

    public int getEventGameType(int gameType) {
        if (gameType == SudGameConstant.UMO_GAME) {
            return EventGameTypeConstant.UMO;
        }
        if (gameType == SudGameConstant.MONSTER_CRUSH_GAME) {
            return EventGameTypeConstant.MONSTER_CRUSH;
        }
        if (gameType == SudGameConstant.DOMINO_GAME) {
            return EventGameTypeConstant.DOMINO;
        }
        if (gameType == SudGameConstant.CARROM_POOL_GAME) {
            return EventGameTypeConstant.CARROM_POOL;
        }
        if (gameType == SudGameConstant.BILLIARD_GAME) {
            return EventGameTypeConstant.BILLIARD_POOL;
        }
        if (gameType == SudGameConstant.JACKAROO_GAME) {
            return EventGameTypeConstant.JACKAROO_POOL;
        }
        if (gameType == SudGameConstant.BALOOT_GAME) {
            return EventGameTypeConstant.BALOOT_POOL;
        }
        if (gameType == SudGameConstant.WOISSPY_GAME) {
            return EventGameTypeConstant.WOISSPY_POOL;
        }
        return EventGameTypeConstant.LUDO;
    }

    public SudCode getGameCode(SudGameDTO reqDTO) {
        // 获取短期令牌Code，默认时长2小时
        return sudMGPAuth.getCode(reqDTO.getUid());
    }

    public String getAppServiceSignature() {
        String data = sudGameConfig.getAppId();
        byte[] key = sudGameConfig.getAppSecret().getBytes();
        HMac mac = new HMac(HmacAlgorithm.HmacMD5, key);
        return mac.digestHex(data);
    }

    public String getAuthorization(String body) {
        // 应用ID
        String appId = sudGameConfig.getAppId();
        // 请求时间戳（发送请求的时间戳）
        String timestamp = "" + System.currentTimeMillis();
        // 随机字符串 (自定义随机字符串)
        String nonce = "youstar";
        // 签名串
        String signContent = String.format("%s\n%s\n%s\n%s\n", appId, timestamp, nonce, body);
        // 签名值
        HMac hMac = new HMac(HmacAlgorithm.HmacSHA1, sudGameConfig.getAppSecret().getBytes());
        String signature = hMac.digestHex(signContent);
        return String.format("Sud-Auth app_id=\"%s\",timestamp=\"%s\",nonce=\"%s\",signature=\"%s\"",
                appId, timestamp, nonce, signature);
    }

    public String addAi(SudGameDTO req) {
        SudGameData game = sudGameDao.findData(req.getGameId());
        ActorData actorData = actorDao.getActorDataFromCache(req.getUid());
        if (null == game || null == actorData || actorData.getRobot() != 1 || SudGameConstant.GAME_MATCHING != game.getStatus()) {
            return "false";
        }
        ApiResult<BaseVO<?>> apiResult = sudGameApi.pushEvent(EventDTO.addAi(sudGameConfig.getGameIdByGameType(game.getGameType()), req.getGameId(), actorData));
        if (apiResult.isError()) {
            logger.error("add ai error, roomId={} gameId={} msg={}", game.getRoomId(), req.getGameId(), apiResult.getData());
            return "false";
        }
        sendJoinGameMsg(game, actorData);
        return "true";
    }

    private void sendJoinGameMsg(SudGameData game, ActorData actorData) {
        RoomNotificationMsg msg = new RoomNotificationMsg();
        msg.setUid(actorData.getUid());
        msg.setUser_name(actorData.getName());
        msg.setUser_head(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
        msg.setText(String.format(JOIN_GAME_MSG, actorData.getName(), game.getGameName()));
        msg.setText_ar(String.format(JOIN_GAME_MSG_AR, actorData.getName(), game.getGameNameAr()));
        List<HighlightTextObject> list = new ArrayList<>();
        HighlightTextObject object = new HighlightTextObject();
        object.setText(actorData.getName());
        object.setHighlightColor("#FFE200");
        list.add(object);
        msg.setHighlight_text(list);
        msg.setHighlight_text_ar(list);
        if (RoomUtils.isVoiceRoom(game.getRoomId())) {
            msg.setHide_head(1);
        }
        int onlineVersion = sudGameConfig.getSudGameInfoMap().get(game.getGameType()).getOnlineVersion();
        msg.setGame_type(GAME_TYPE_JOIN_SUD_GAME);
        roomWebSender.sendRoomWebMsg(game.getRoomId(), "", msg, false, onlineVersion);
    }

    private void sendQuitGameMsg(ActorData actorData, String roomId) {
        RoomNotificationMsg msg = new RoomNotificationMsg();
        msg.setUid(actorData.getUid());
        msg.setUser_name(actorData.getName());
        msg.setUser_head(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
        msg.setText(String.format(QUIT_GAME_MSG, actorData.getName()));
        msg.setText_ar(String.format(QUIT_GAME_MSG_AR, actorData.getName()));
        List<HighlightTextObject> list = new ArrayList<>();
        HighlightTextObject object = new HighlightTextObject();
        object.setText(actorData.getName());
        object.setHighlightColor("#FFE200");
        list.add(object);
        msg.setHighlight_text(list);
        msg.setHighlight_text_ar(list);
//        msg.setHide_head(1);
        msg.setGame_type(GAME_TYPE_QUITE_SUD_GAME);
        roomWebSender.sendRoomWebMsg(roomId, "", msg, false);
    }

    public MatchingVO matching(SudGameDTO req) {
        int gameType = req.getGameType();
        String uid = req.getUid();
        gameRoomRedis.updateMatchGameTime(uid, String.valueOf(gameType));
        if (gameType == 1) {
            return turntableService.matching(uid);
        } else if (gameType == 100) {
            return truthDareV2Service.matching(uid);
        }
        // 校验玩家是否有未退出的游戏
        String inGameId = sudGameRedis.getPlayerData(uid);
        if (null != inGameId) {
            SudGameInfo sudGameInfo = gameMap.get(inGameId);
            if (null == sudGameInfo) {
                // 游戏不存在，是脏数据，需要删除
                sudGameRedis.removePlayerData(req.getUid());
                logger.error("clear dirty data uid={} inGameId={}", req.getUid(), inGameId);
            } else if (sudGameInfo.getStatus() == SudGameConstant.GAME_MATCHING && ObjectUtils.isEmpty(roomPlayerRedis.getActorRoomStatus(req.getUid()))) {
                leaveRoomQuiteGame(req.getUid(), inGameId);
            } else {
                logger.info("player has in game uid={}", req.getUid());
                String gameName = sudGameConfig.getGameNameByGameType(sudGameInfo.getGameType(), req.getSlang());
                throw new GameException(GameHttpCode.PLAYER_IN_GAME_J, new Object[]{gameName});
            }
        }
        MatchingVO vo = new MatchingVO();
        List<SudGameData> matchingByGameType = sudGameDao.findMatchingByGameType(req.getGameType(), SudGameConstant.VOICE_ROOM);
        Collections.shuffle(matchingByGameType);
        for (SudGameData sudGameData : matchingByGameType) {
            MongoRoomData roomData = mongoRoomDao.findData(sudGameData.getRoomId());
            if (null != roomData && StringUtils.isEmpty(roomData.getPwd())
                    && !roomBlacklistDao.isBlock(sudGameData.getRoomId(), req.getUid())
                    && !roomKickRedis.isKick(sudGameData.getRoomId(), req.getUid())) {
                vo.setMatchedPlayers(sudGameData.getPlayerList().stream().map(SudGamePlayerData::getHead).collect(Collectors.toSet()));
                vo.setRoomId(sudGameData.getRoomId());
                vo.setGameId(sudGameData.getGameId().toString());
                break;
            }
        }
        vo.setRecentlyPlayers(sudGameDao.findRecentlyPlayer(req.getUid(), req.getGameType()));
        return vo;
    }


    public void gameDiamondAlert(String gameId, int gameType, int changed) {
        try {
            // 单款游戏平台亏损10000钻时触发告警，每增加1000触发1次。
            int platformBeansChange = sudGameRedis.incSudGameBeansChangeSum(gameType, changed);
            if (platformBeansChange >= PLATFORM_WARNING_LINE) {
                int warnLevel = (platformBeansChange - PLATFORM_WARNING_LINE) / 1000 + 1;
                int oldWarnLevel = sudGameRedis.getSudGameWarnLevel(gameType);
                if (warnLevel > oldWarnLevel) {
                    StringBuilder desc = new StringBuilder(String.format("平台亏损钻石达%s钻\n", platformBeansChange));
                    String gameName = sudGameConfig.getSudGameInfoMap().get(gameType).getName();
                    Set<String> abnormalUserSet = sudGameRedis.getAbnormalUser(gameType);
                    for (String aid : abnormalUserSet) {
                        ActorData actorData = actorDao.getActorDataFromCache(aid);
                        desc.append(String.format("ID：%s用户%s游戏结算异常;\n", actorData != null ? actorData.getRid() : aid, gameName));
                    }
                    noticeWarn(gameName, desc.toString());
                    sudGameRedis.setSudGameWarnLevel(gameType, warnLevel);
                }
            }
        } catch (Exception e) {
            logger.error("gameDiamondAlert error. gameId={} gameType={} changed={} {}", gameId, gameType, changed, e.getMessage(), e);
        }
    }

    public void noticeWarn(String gameName, String desc) {
        String content = gameName + "游戏结算异常告警 \n"
                + ">告警名: 平台游戏钻石亏损 \n"
                + ">所属项目: Youstar \n"
                + ">游戏项目: " + gameName + " \n"
                + ">预警详情: " + desc + " \n"
                + ">处理人: @赖勇奇 @谢建良";
        monitorSender.customMarkdown(ServerConfig.isProduct() ? MonitorWarnName.WARN_SUD_GAME_PLAY : MonitorWarnName.WARN_EXCEPTION, content);
    }


    /**
     * 扣费事件
     */
    public void creatOrder(SudGameCreateOrderDTO reqDTO) {
        String uid = reqDTO.getUid();
        String roomId = reqDTO.getRoomId();
        SudGameData data = sudGameDao.findData(reqDTO.getGameId());
        if (data == null || data.getStatus() == SudGameConstant.GAME_CLOSED || data.getStatus() == SudGameConstant.GAME_FINISH) {
            throw new GameException(GameHttpCode.GAME_CLOSED);
        }
        if (SudGameConstant.LUDO_GAME == data.getGameType() || SudGameConstant.UMO_GAME == data.getGameType()) {
            SudGameConfigInfo gameConfigInfo = sudGameConfig.getSudGameInfoMap().get(data.getGameType());
            String payLoad = reqDTO.getPayload();
            Integer seq = null;
            JSONObject jsonObject = null;
            if (!StringUtils.isEmpty(payLoad)) {
                jsonObject = JSONObject.parseObject(payLoad);
                seq = jsonObject.getInteger("seq");
            }
            int add = seq != null ? 2 * seq : 0;
            int currency = RoomUtils.isGameRoom(roomId) ? 0 :
                    SudGameConstant.UMO_GAME == data.getGameType() ? UMO_CREATE_FEE : LUDO_CREATE_FEE;
//            if (DIAMOND == data.getCurrencyType()) {
            if (currency > 0) {
                MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
                moneyDetailReq.setRandomId();
                moneyDetailReq.setUid(uid);
                moneyDetailReq.setAtype(gameConfigInfo.getFeeCreateActType());
                moneyDetailReq.setChanged(-currency);
                moneyDetailReq.setTitle(gameConfigInfo.getFeeCreateTitle());
                moneyDetailReq.setDesc(gameConfigInfo.getFeeCreateDesc());
                moneyDetailReq.setRoomId(roomId);
                ApiResult<String> result = dataCenterService.reduceBeans(moneyDetailReq);
                if (result.isOk()) {
                    CreateOrderDTO cDto = CreateOrderDTO.getSudCreateOrderDTO(sudGameConfig.getGameIdByGameType(data.getGameType())
                            , reqDTO.getGameId(), reqDTO.getCmd(), reqDTO.getFromUid(), reqDTO.getToUid(), reqDTO.getValue(), jsonObject);
                    ApiResult<BaseVO<?>> apiResult = sudGameApi.createOrder(cDto);
                    if (apiResult.isError()) {
                        logger.error("create order error, roomId={} gameId={} msg={}", data.getRoomId(), reqDTO.getGameId(), apiResult.getData());
                        throw new GameException(GameHttpCode.FAILURE);
                    }
                    logger.info("sud game create order success diamond uid={} diamond={} gameId={}", uid, currency, reqDTO.getGameId());
                } else {
                    logger.info("sud game create order not enough diamond  uid={} diamond={} gameId={}", uid, currency, reqDTO.getGameId());
                    throw new GameException(GameHttpCode.DIAMOND_NOT_ENOUGH);
                }
//            } else {
//                logger.error("coin not support uid={} roomId={} gameType={} playerNumber={}", uid, roomId, data.getGameType(), playerNum);
//                throw new GameException(GameHttpCode.PARAM_ERROR);
//            }
            } else {
                CreateOrderDTO cDto = CreateOrderDTO.getSudCreateOrderDTO(sudGameConfig.getGameIdByGameType(data.getGameType())
                        , reqDTO.getGameId(), reqDTO.getCmd(), reqDTO.getFromUid(), reqDTO.getToUid(), reqDTO.getValue(), jsonObject);
                ApiResult<BaseVO<?>> apiResult = sudGameApi.createOrder(cDto);
                if (apiResult.isError()) {
                    logger.error("no fee create order error, roomId={} gameId={} msg={}", data.getRoomId(), reqDTO.getGameId(), apiResult.getData());
                    throw new GameException(GameHttpCode.FAILURE);
                }
                logger.info("no fee sud game create order success diamond uid={} diamond={} gameId={}", uid, currency, reqDTO.getGameId());
            }
        } else {
            logger.error("sud game create order diamond error not support game type uid={} roomId={} gameType={} ", uid, roomId, data.getGameType());
            throw new GameException(GameHttpCode.PARAM_ERROR);
        }
//        logger.info("create order game success. uid={} gameId={} gameType={}", uid, reqDTO.getGameId(), data.getGameType());
    }


    /**
     * 游戏房创建游戏
     */
    public CreateSudGameV2VO createOrJoin(CreateSudGameDTO reqDTO) {
        reqDTO.setCurrencyType(COIN);//目前都为金币局
        String uid = reqDTO.getUid();
        String roomId = reqDTO.getRoomId();
        String roomHostId = RoomUtils.getRoomHostId(roomId);
        int enterRoomType = reqDTO.getEnterRoomType();
        int gameType = reqDTO.getGameType();
        int currency = reqDTO.getCurrency();
        if (reqDTO.getCurrency() <= 0) {
            logger.error("coin param error, uid ={} currency={}", uid, reqDTO.getCurrency());
            throw new GameException(GameHttpCode.PARAM_ERROR);
        }
        if (SudGameConstant.CARROM_POOL_GAME == reqDTO.getGameType()) {
            if (!reqDTO.getRule().containsKey("mode_ex")) {
                logger.error("can not find mode_ex, uid ={}", uid);
                throw new GameException(GameHttpCode.PARAM_ERROR);
            }
            reqDTO.setPlayerNumber(3 == reqDTO.getRule().getIntValue("mode_ex") ? 4 : 2);
        }
        ActorData myActorData = actorDao.getActorData(uid);
        if (myActorData == null) {
            logger.error("can not find actor data, uid ={}", uid);
            throw new GameException(GameHttpCode.USER_NOT_EXISTING);
        }
//        int gold = myActorData.getHeartGot();
//        if (gold < currency) {
//            throw new GameException(GameHttpCode.NOT_ENOUGH_COIN);
//        }

        RoomActorDetailData actorData = roomActorCache.getData(roomId, uid, false);
        if (actorData == null) {
            logger.error("can not find room actor data, uid ={}", uid);
            throw new GameException(GameHttpCode.USER_NOT_EXISTING);
        }
        String gameIdType = sudGameRedis.getGameIdAndTypeByRoomId(roomId);
        if (StringUtils.isEmpty(gameIdType)) {
            // 房间没有任何游戏
            if (enterRoomType == 1 || enterRoomType == 2 || enterRoomType == 4 || enterRoomType == 5) {
                // create new game
                return toCreateSudGameV2VO(createGameByLeader(reqDTO, actorData));
            } else {
                // 邀请进房，不存在游戏，则不加入不创建游戏
                logger.error("invite to room not game, uid ={}", uid);
                throw new GameException(GameHttpCode.GAME_ROOM_NOT_HAVE_GAME);
            }
        } else {
            String[] split = gameIdType.split("-");
            String gameId = split[0];
            int runningGameType = Integer.parseInt(split[1]);
            SudGameConfigInfo gameConfigInfo = sudGameConfig.getSudGameInfoMap().get(runningGameType);
            // join runnin game
            SudGameData data = sudGameDao.findData(gameId);
            if (data == null || data.getStatus() == SudGameConstant.GAME_CLOSED
                    || data.getStatus() == SudGameConstant.GAME_FINISH) {
                if (enterRoomType == 1 || enterRoomType == 2 || enterRoomType == 4 || enterRoomType == 5) {
                    // create new game
                    return toCreateSudGameV2VO(createGameByLeader(reqDTO, actorData));
                } else {
                    // 邀请进房，不存在游戏，则不加入不创建游戏
                    logger.error("invite to room not game, uid ={}", uid);
                    throw new GameException(GameHttpCode.GAME_ROOM_NOT_HAVE_GAME);
                }
            } else if (data.getStatus() == SudGameConstant.GAME_PROCESSING) {
                // view game
                return toCreateSudGameV2VO(gameDataTOVO(data));
            } else {
                // 匹配中，匹配暂停中
                if (roomHostId.equals(uid)) {
                    if (enterRoomType == 1) {
//                    //房主创建进房，切换了游戏类型或入场费，这里还有可能改了规则，暂时先不考虑
                        if (runningGameType != gameType || currency != data.getCurrency()) {
                            preCheckCoin(reqDTO, myActorData, data);
                            // 结束老的游戏创建新的
                            removeGame(data);
                            return toCreateSudGameV2VO(createGameByLeader(reqDTO, actorData));
                        } else if (reqDTO.getPlayerNumber() < data.getPlayerNumber()) {
                            // && data.getPlayerList().size() > reqDTO.getPlayerNumber()
                            preCheckCoin(reqDTO, myActorData, data);
                            // 坑位减少，玩家人数大于坑位
                            removeGame(data);
                            return toCreateSudGameV2VO(createGameByLeader(reqDTO, actorData));
                        } else {
                            //游戏规则更改或者人数不变或者变多了
                            data.setRule(reqDTO.getRule());
                            data.setPlayerNumber(reqDTO.getPlayerNumber());
//                            if (reqDTO.getPlayerNumber() < data.getPlayerNumber() && SudGameConstant.CARROM_POOL_GAME == gameType) {
//                                List<SudGamePlayerData> playerList = data.getPlayerList();
//                                if (!CollectionUtils.isEmpty(playerList)) {
//                                    for (int i = 0; i < playerList.size(); i++) {
//                                        playerList.get(i).setIndex(i);
//                                    }
//                                }
//                            }
                            sudGameDao.save(data);
                            // 更新redis里的游戏信息,只改规则这里可以不改
                            SudGameInfo gameInfo = new SudGameInfo();
                            BeanUtils.copyProperties(data, gameInfo);
                            gameInfo.setGameId(data.getGameId().toString());
                            sudGameRedis.updateSudGameInfo(gameInfo);
                            gameChange(data);
                            logger.info("roomHost enter room change game success uid:{} roomId:{} gameId:{}"
                                    , reqDTO.getUid(), reqDTO.getRoomId(), gameInfo.getGameId());
                        }

                        // 结束老的游戏创建新的
//                        removeGame(data);
//                        return toCreateSudGameV2VO(createGameByLeader(reqDTO, actorData));
                    }

                } else if (data.getLeaderUid().equals(uid)) {
                    // 队长改游戏，web端判断游戏规则，这里只判断金额
                    if (currency != data.getCurrency()) {
                        preCheckCoin(reqDTO, myActorData, data);
                        // 结束老的游戏创建新的
                        removeGame(data);
                        return toCreateSudGameV2VO(createGameByLeader(reqDTO, actorData));
                    } else if (reqDTO.getPlayerNumber() < data.getPlayerNumber()) {
                        // && data.getPlayerList().size() > reqDTO.getPlayerNumber()
                        preCheckCoin(reqDTO, myActorData, data);
                        // 坑位减少，玩家人数大于坑位
                        removeGame(data);
                        return toCreateSudGameV2VO(createGameByLeader(reqDTO, actorData));
                    } else {
                        //游戏规则更改或者坑位不变或者变多了
                        data.setRule(reqDTO.getRule());
                        data.setPlayerNumber(reqDTO.getPlayerNumber());
//                        if (reqDTO.getPlayerNumber() < data.getPlayerNumber() && SudGameConstant.CARROM_POOL_GAME == gameType) {
//                            List<SudGamePlayerData> playerList = data.getPlayerList();
//                            if (!CollectionUtils.isEmpty(playerList)) {
//                                for (int i = 0; i < playerList.size(); i++) {
//                                    playerList.get(i).setIndex(i);
//                                }
//                            }
//                        }
                        sudGameDao.save(data);
                        // 更新redis里的游戏信息,只改规则这里可以不改
                        SudGameInfo gameInfo = new SudGameInfo();
                        BeanUtils.copyProperties(data, gameInfo);
                        gameInfo.setGameId(data.getGameId().toString());
                        sudGameRedis.updateSudGameInfo(gameInfo);
                        gameChange(data);

                        logger.info("leader change game success uid:{} roomId:{} gameId:{}"
                                , reqDTO.getUid(), reqDTO.getRoomId(), gameInfo.getGameId());
                    }
                }

                List<SudGamePlayerData> playerList = data.getPlayerList();
                if (!CollectionUtils.isEmpty(playerList)) {
                    List<String> playerUidList = playerList.stream().map(SudGamePlayerData::getUid).collect(Collectors.toList());
                    if (playerUidList.contains(uid)) {
                        logger.info("has joined the game. uid={} roomId={} gameId={}", uid, roomId, gameId);
                        return toCreateSudGameV2VO(gameDataTOVO(data));
                    }
                    if (playerList.size() == data.getPlayerNumber()) {
                        logger.info("game reach max={} uid={} roomId={} gameId={}", data.getPlayerNumber(), uid, roomId, gameId);
                        return toCreateSudGameV2VO(gameDataTOVO(data));
                    }
                }
                // 加入游戏
                SudGameDTO dto = new SudGameDTO();
                dto.setUid(reqDTO.getUid());
                dto.setAid(reqDTO.getUid());
                dto.setRoomId(reqDTO.getRoomId());
                dto.setIndex(reqDTO.getIndex());
                dto.setGameId(reqDTO.getGameId());
                dto.setGameType(reqDTO.getGameType());
                return toCreateSudGameV2VO(playerJoinGame(dto, data, myActorData));
            }
        }
    }


    private void preCheckCoin(CreateSudGameDTO reqDTO, ActorData actorData, SudGameData gameData) {
        int gold = actorData.getHeartGot();
        if (COIN == reqDTO.getCurrencyType() && gold < reqDTO.getCurrency()) {
            logger.info("preCheckCoin player not enough heart. uid={} roomId={} change={}", reqDTO.getUid(),
                    reqDTO.getRoomId(), reqDTO.getCurrency());
            throw new GameException(GameHttpCode.NOT_ENOUGH_COIN, toCreateSudGameV2VO(gameDataTOVO(gameData)));
        }
    }

    public CreateSudGameV2VO toCreateSudGameV2VO(CreateSudGameVO oldVO) {
        CreateSudGameV2VO vo = new CreateSudGameV2VO();
        BeanUtils.copyProperties(oldVO, vo);
        setGameConfigInfo(vo.getGameType(), vo);
        vo.setHandleType(1);
        return vo;
    }


    public CreateSudGameVO createGameByLeader(CreateSudGameDTO reqDTO, RoomActorDetailData actorData) {
        String uid = reqDTO.getUid();
        String roomId = reqDTO.getRoomId();
//        if (reqDTO.getCurrency() <= 0) {
//            logger.error("not enough coin, uid ={} currency={}", uid, reqDTO.getCurrency());
//            throw new GameException(GameHttpCode.NOT_ENOUGH_COIN);
//        }
//        if (SudGameConstant.CARROM_POOL_GAME == reqDTO.getGameType()) {
//            if (!reqDTO.getRule().containsKey("mode_ex")) {
//                logger.error("can not find mode_ex, uid ={}", uid);
//                throw new GameException(GameHttpCode.PARAM_ERROR);
//            }
//            reqDTO.setPlayerNumber(3 == reqDTO.getRule().getIntValue("mode_ex") ? 4 : 2);
//        }
        // 校验创建游戏的权限
        SudGameData gameData = buildSudGameData(reqDTO, actorData);
        gameData.setGameId(new ObjectId());
        joinSudGame(uid, roomId, gameData, reqDTO, true);
        // 创建游戏
        gameData.setLeaderUid(uid);
        SudGameData data = sudGameDao.save(gameData);
        // 往redis里存入游戏相关信息
        SudGameInfo gameInfo = new SudGameInfo();
        BeanUtils.copyProperties(data, gameInfo);
        gameInfo.setGameId(data.getGameId().toString());
        sudGameRedis.saveSudGameInfo(gameInfo);
        gameMap.put(gameInfo.getGameId(), gameInfo);
        sudGameRedis.savePlayerData(uid, data.getGameId().toString());
        // 发送创建游戏消息
        sendRoomSudGameOperateMsg(actorData, gameInfo, SudGameConstant.GAME_CREATE_MSG);
        logger.info("create sud game.creator={} gameId={} gameType={}", gameInfo.getSelfUid(), gameInfo.getGameId(), gameInfo.getGameType());
        // 用户创建游戏时上报数数
        doReportEvent(gameData);
        sendMicChange(reqDTO.getRoomId(), reqDTO.getUid());
        sendJoinGameMsg(data, actorDao.getActorDataFromCache(uid));
        return gameChange(data);
//                vo.setCreateStatus(1);
    }

    public void setGameConfigInfo(int gameType, CreateSudGameV2VO checkVO) {
        SudGameConfigInfo sudGameConfigInfo = sudGameConfig.getSudGameInfoMap().get(gameType);
        checkVO.getGameConfig().setCoinFeeList(sudGameConfigInfo.getGameRoomCoinFeeList()); // v8.59 新版本金币费用列表
        checkVO.getGameConfig().setDiamondFeeList(sudGameConfigInfo.getDiamondFeeList()); // v8.50 新版本钻石费用列表
        checkVO.getGameConfig().setSudGameId(sudGameConfigInfo.getGameId());
        if (gameType == SudGameConstant.LUDO_GAME) {
            // Ludo的游戏配置
        } else if (gameType == SudGameConstant.UMO_GAME) {
            // UMO的游戏配置
        } else if (gameType == SudGameConstant.MONSTER_CRUSH_GAME) {
            // Monster Crush的游戏配置
//            checkVO.getGameConfig().setCurrencyInfoList(new ArrayList<>(sudGameConfigInfo.getCurrencyInfoMap().values()));
            checkVO.getGameConfig().setRuleUrl("https://static.youstar.live/gameplay/");
        } else if (gameType == SudGameConstant.DOMINO_GAME) {
            // Domino的游戏配置
            checkVO.getGameConfig().setRuleUrl("https://static.youstar.live/gp_rule/");
        } else {
            checkVO.getGameConfig().setRuleUrl("https://static.youstar.live/game_rule" + gameType + "/");
        }
    }

    public void sendMicChange(String roomId, String fromUid) {
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                String path = "mic_change";
                Map<String, String> params = new HashMap<>();
                params.put("room_id", roomId);
                params.put("from_uid", fromUid);
                roomMicDao.incVersion(roomId);
                roomWebSender.sendPost(path, roomId, params);
            }
        });
    }


    private void handleWhoIsSpy(String roomId, String uid, CommonMqTopicData.MicInfo micInfo){
        // downType 下麦操作类型：1为主动下麦，2为房主踢掉下麦，3为管理员踢掉下麦，4为离开房间下麦，5切换live模式时被踢下麦，7切换麦位下麦
        String gameId = sudGameRedis.getGameIdByRoomId(roomId);
        int downType = micInfo.getDownType();
        int index = micInfo.getIndex();
        if (StringUtils.isEmpty(gameId)) {
            return;
        }
        SudGameData gameData = sudGameDao.findData(gameId);
        if (null == gameData) {
            return;
        }
        if (gameData.getGameType() != SudGameConstant.WOISSPY_GAME) {
            return;
        }
        if (CollectionUtils.isEmpty(gameData.getPlayerList())){
            return;
        }
        if (downType == 7){
            return;
        }

        int quitType = downType == 4 ? 2 : 1;
        logger.info("who is spy game. uid={} roomId={} gameId={} downType={} quitType={} index={}", uid, roomId, gameId, downType, quitType, index);
        SudGameDTO reqDTO = new SudGameDTO();
        reqDTO.setUid(uid);
        reqDTO.setAid(uid);
        reqDTO.setGameId(gameId);
        reqDTO.setRoomId(roomId);
        reqDTO.setQuitType(quitType);
        quitGame(reqDTO);

        // 用户下麦, 麦位静音
        MuteMicDTO muteMicDTO = new MuteMicDTO();
        muteMicDTO.setIndex(index);
        muteMicDTO.setRoomId(roomId);
        muteMicDTO.setUid(RoomUtils.getRoomHostId(roomId));
        muteMicDTO.setOpType(1);
        muteMicDTO.setOriginType(1);
        iRoomService.muteMic(muteMicDTO);
    }

    @Override
    public void processHandle(CommonMqTopicData mqData) {
        String uid = mqData.getUid();
        String roomId = mqData.getRoomId();
        String item = mqData.getItem();
        int downType = mqData.getValue();

        if (CommonMqTaskConstant.USER_DOWN_MIC.equals(item)) {
            CommonMqTopicData.MicInfo micInfo = JSONObject.parseObject(mqData.getJsonData(), CommonMqTopicData.MicInfo.class);
            handleWhoIsSpy(roomId, uid, micInfo);
        }
    }
}

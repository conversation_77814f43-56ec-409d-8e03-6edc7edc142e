package com.quhong.controllers;

import com.alibaba.fastjson.JSON;
import com.quhong.data.HttpMsgData;
import com.quhong.enums.HttpCode;
import com.quhong.handler.BaseController;
import com.quhong.handler.HttpEnvData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/room/")
public class RoomHandler extends BaseController {
    private static final Logger logger = LoggerFactory.getLogger(RoomHandler.class);

    @RequestMapping("send_msg")
    public String receiveMsg(@RequestBody String body) {
        try {
            HttpEnvData envData = new HttpEnvData();
            logger.info("receive msg from python server. {}", body);
            HttpMsgData msgData = JSON.parseObject(body, HttpMsgData.class);
            logger.info("recevie msg data. msgType={} fromUid={} content={}", msgData.getMsgType(), msgData.getFromUid(), msgData.getContent());
            return createResult(envData, HttpCode.SUCCESS, null);
        }catch (Exception e){
            logger.error(e.getMessage() ,e);
        }
        HttpEnvData envData = new HttpEnvData();
        return createError(envData, HttpCode.SERVER_ERROR);
    }
}

package com.quhong.controllers;

import com.quhong.data.dto.BigBossDTO;
import com.quhong.data.vo.DailyRechargeRankingVO;
import com.quhong.enums.HttpCode;
import com.quhong.handler.H5Controller;
import com.quhong.service.BigBossService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2022/11/16
 */
@RestController
@RequestMapping("${baseUrl}")
public class BigBossController extends H5Controller {

    private static final Logger logger = LoggerFactory.getLogger(BigBossController.class);

    @Resource
    private BigBossService bigBossService;

    /**
     * 每日充值榜单
     */
    @RequestMapping(value = "daily_recharge_ranking", method = RequestMethod.POST)
    private String dailyRechargeRanking(@RequestBody BigBossDTO dto) {
        long time = System.currentTimeMillis();
        logger.info("get daily recharge ranking. uid={} n={} bearDate={}", dto.getUid(), dto.getN(), dto.getBear_date());
        DailyRechargeRankingVO vo = bigBossService.dailyRechargeRanking(dto);
        logger.info("get daily recharge ranking. cost={}", System.currentTimeMillis() - time);
        return createResult(HttpCode.SUCCESS, vo);
    }

    @RequestMapping(value = "setNotHide", method = RequestMethod.POST)
    private String setNotHide(@RequestBody BigBossDTO dto) {
        DailyRechargeRankingVO vo = bigBossService.setNotHide(dto);
        return createResult(HttpCode.SUCCESS, vo);
    }

}

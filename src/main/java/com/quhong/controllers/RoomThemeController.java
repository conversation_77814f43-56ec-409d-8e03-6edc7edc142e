package com.quhong.controllers;

import com.quhong.data.dto.ThemeDTO;
import com.quhong.data.vo.*;
import com.quhong.enums.HttpCode;
import com.quhong.handler.WebController;
import com.quhong.service.RoomThemeService;
import com.quhong.utils.RequestUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 房间主题
 *
 * <AUTHOR>
 * @date 2022/9/22
 */
@RestController
@RequestMapping(value = "${baseUrl}")
public class RoomThemeController extends WebController {

    private static final Logger logger = LoggerFactory.getLogger(RoomThemeController.class);

    @Resource
    private RoomThemeService roomThemeService;

    /**
     * 房间主题展示
     */
    @RequestMapping("theme")
    public String getThemeList(HttpServletRequest request) {
        long millis = System.currentTimeMillis();
        ThemeDTO req = RequestUtils.getSendData(request, ThemeDTO.class);
        RoomThemeVO vo = roomThemeService.getThemeList(req);
        logger.info("get room theme list. uid={} timeMillis={}", req.getUid(), System.currentTimeMillis() - millis);
        return createResult(req, HttpCode.SUCCESS, vo);
    }

    /**
     * 房间背景商店
     */
    @RequestMapping("room_background/store")
    public String getRoomBackgroundStore(HttpServletRequest request) {
        long millis = System.currentTimeMillis();
        ThemeDTO req = RequestUtils.getSendData(request, ThemeDTO.class);
        BackgroundStoreVO vo = roomThemeService.getRoomBackgroundStore(req);
        logger.info("get room background store . uid={} tabId={} page={} timeMillis={}", req.getUid(), req.getTabId(), req.getPage(), System.currentTimeMillis() - millis);
        return createResult(req, HttpCode.SUCCESS, vo);
    }

    /**
     * 购买房间背景
     */
    @RequestMapping("room_background/buy")
    public String buyRoomBackground(HttpServletRequest request) {
        ThemeDTO req = RequestUtils.getSendData(request, ThemeDTO.class);
        logger.info("buy room background. uid={} roomId={} id={} ", req.getUid(), req.getRoomId(), req.getId());
        if (StringUtils.isEmpty(req.getRoomId()) || req.getRoomId().length() < 2) {
            return createResult(req, HttpCode.PARAM_ERROR, null);
        }
        roomThemeService.buyRoomBackground(req);
        return createResult(req, HttpCode.SUCCESS, null);
    }

    /**
     * 我的房间背景
     */
    @RequestMapping("room_background/mine")
    public String getMineRoomBackground(HttpServletRequest request) {
        long millis = System.currentTimeMillis();
        ThemeDTO req = RequestUtils.getSendData(request, ThemeDTO.class);
        if (StringUtils.isEmpty(req.getRoomId()) || req.getRoomId().length() < 2) {
            return createResult(req, HttpCode.PARAM_ERROR, null);
        }
        MineBackgroundVO vo = roomThemeService.getMineRoomBackground(req);
        logger.info("get mine room background . uid={} bgType={} page={} timeMillis={}", req.getUid(), req.getBgType(), req.getPage(), System.currentTimeMillis() - millis);
        return createResult(req, HttpCode.SUCCESS, vo);
    }

    /**
     * 删除自己上传的房间背景
     */
    @RequestMapping("upload_background/delete")
    public String deleteUploadBackground(HttpServletRequest request) {
        ThemeDTO req = RequestUtils.getSendData(request, ThemeDTO.class);
        logger.info("delete mine upload room background . uid={} roomId={} id={}", req.getUid(), req.getRoomId(), req.getId());
        if (StringUtils.isEmpty(req.getUid()) || StringUtils.isEmpty(req.getRoomId()) || req.getRoomId().length() < 2) {
            return createResult(req, HttpCode.PARAM_ERROR, null);
        }
        return createResult(req, HttpCode.SUCCESS, roomThemeService.deleteUploadBackground(req));
    }

    /**
     * 获取房间背景商店tabs
     */
    @RequestMapping("room_background/store_tabs")
    public String getBgStoreTabs(HttpServletRequest request) {
        long millis = System.currentTimeMillis();
        ThemeDTO req = RequestUtils.getSendData(request, ThemeDTO.class);
        BgStoreTabsVO vo = roomThemeService.getBgStoreTabs();
        logger.info("get room background store tabs. uid={} timeMillis={}", req.getUid(), System.currentTimeMillis() - millis);
        return createResult(req, HttpCode.SUCCESS, vo);
    }
}

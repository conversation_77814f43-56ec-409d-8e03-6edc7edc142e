package com.quhong.controllers;

import com.quhong.constant.RoomListConstant;
import com.quhong.data.dto.*;
import com.quhong.data.vo.*;
import com.quhong.enums.HttpCode;
import com.quhong.handler.HttpEnvData;
import com.quhong.handler.WebController;
import com.quhong.intercepters.UserInterceptor;
import com.quhong.service.HomeRankListService;
import com.quhong.service.RoomListService;
import com.quhong.service.RoomRecommendService;
import com.quhong.utils.RequestUtils;
import com.quhong.vo.PageVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;


@RestController
@RequestMapping(value = "${baseUrl}")
public class RoomListController extends WebController {
    private static final Logger logger = LoggerFactory.getLogger(RoomListController.class);

    @Resource
    private RoomListService roomListService;

    @Resource
    private HomeRankListService homeRankListService;
    @Resource
    private RoomRecommendService roomRecommendService;

    /**
     * popular首页列表
     */
    @RequestMapping("popularList")
    private String popularList(HttpServletRequest request) {
        PartyListDTO req = RequestUtils.getSendData(request, PartyListDTO.class);
        long timeMillis = System.currentTimeMillis();
        ListVO popularList = roomListService.getPopularList(req);
        long cost = System.currentTimeMillis() - timeMillis;
        logger.info("popularList uid={} page={} size={} nextUrl={} cost={}",
                req.getUid(), req.getPage(), popularList.getList().size(), popularList.getNextUrl(), cost);
        return createResult(req, HttpCode.SUCCESS, popularList);
    }

    /**
     * popular首页列表（访客模式）
     */
    @RequestMapping(UserInterceptor.VISITOR + "popularList")
    private String popularListVisitor(HttpServletRequest request) {
        PartyListDTO req = RequestUtils.getSendData(request, PartyListDTO.class);
        long timeMillis = System.currentTimeMillis();
        ListVO popularList = roomListService.getPopularList(req);
        long cost = System.currentTimeMillis() - timeMillis;
        logger.info("visitor popularList page={} size={} nextUrl={} cost={}",
                req.getPage(), popularList.getList().size(), popularList.getNextUrl(), cost);
        return createResult(req, HttpCode.SUCCESS, popularList);
    }

    /**
     * forYou列表
     */
    @RequestMapping("forYouList")
    private String forYouList(HttpServletRequest request) {
        long timeMillis = System.currentTimeMillis();
        PartyListDTO req = RequestUtils.getSendData(request, PartyListDTO.class);
        ListVO forYouList = roomListService.getForYouList(req);
        long cost = System.currentTimeMillis() - timeMillis;
        logger.info("get forYouList uid={} cost={}", req.getUid(), cost);
        return createResult(req, HttpCode.SUCCESS, forYouList);
    }


    /**
     * forYou列表（访客模式）
     */
    @RequestMapping(UserInterceptor.VISITOR + "forYouList")
    private String forYouListVisitor(HttpServletRequest request) {
        long timeMillis = System.currentTimeMillis();
        PartyListDTO req = RequestUtils.getSendData(request, PartyListDTO.class);
        ListVO forYouList = roomListService.getForYouList(req);
        long cost = System.currentTimeMillis() - timeMillis;
        logger.info("get forYouListVisitor uid={} cost={}", req.getUid(), cost);
        return createResult(req, HttpCode.SUCCESS, forYouList);
    }

    /**
     * country列表
     */
    @RequestMapping("countryList")
    private String countryList(HttpServletRequest request) {
        long timeMillis = System.currentTimeMillis();
        PartyListDTO req = RequestUtils.getSendData(request, PartyListDTO.class);
        CountryVO countryList = roomListService.getCountryList(req);
        long cost = System.currentTimeMillis() - timeMillis;
        logger.info("get countryList uid={} countryCode={} page={} cost={}", req.getUid(), req.getCountryCode(), req.getPage(), cost);
        return createResult(req, HttpCode.SUCCESS, countryList);
    }

    /**
     * country列表（访客模式）
     */
    @RequestMapping(UserInterceptor.VISITOR + "countryList")
    private String countryListVisitor(HttpServletRequest request) {
        long timeMillis = System.currentTimeMillis();
        PartyListDTO req = RequestUtils.getSendData(request, PartyListDTO.class);
        CountryVO countryList = roomListService.getCountryList(req);
        long cost = System.currentTimeMillis() - timeMillis;
        logger.info("get countryListVisitor uid={} countryCode={} page={} cost={}", req.getUid(), req.getCountryCode(), req.getPage(), cost);
        return createResult(req, HttpCode.SUCCESS, countryList);
    }

    /**
     * join列表
     */
    @RequestMapping("joinList")
    private String joinList(HttpServletRequest request) {
        PartyListDTO req = RequestUtils.getSendData(request, PartyListDTO.class);
        if (req.getPage() <= 0) {
            req.setPage(1);
        }
        long timeMillis = System.currentTimeMillis();
        ListVO listVO = roomListService.joinList(req);
        long cost = System.currentTimeMillis() - timeMillis;
        logger.info("join list uid={} cost={}", req.getUid(), cost);
        return createResult(req, HttpCode.SUCCESS, listVO);
    }

    /**
     * follow列表
     */
    @RequestMapping("followList")
    private String followList(HttpServletRequest request) {
        PartyListDTO req = RequestUtils.getSendData(request, PartyListDTO.class);
        if (req.getPage() <= 0) {
            req.setPage(1);
        }
        long timeMillis = System.currentTimeMillis();
        ListVO listVO = roomListService.followList(req);
        long cost = System.currentTimeMillis() - timeMillis;
        logger.info("follow list uid={} cost={}", req.getUid(), cost);
        return createResult(req, HttpCode.SUCCESS, listVO);
    }

    /**
     * recently列表
     */
    @RequestMapping("recentlyList")
    private String recentlyList(HttpServletRequest request) {
        PartyListDTO req = RequestUtils.getSendData(request, PartyListDTO.class);
        if (req.getPage() <= 0) {
            req.setPage(1);
        }
        long timeMillis = System.currentTimeMillis();
        ListVO listVO = roomListService.recentlyList(req);
        long cost = System.currentTimeMillis() - timeMillis;
        logger.info("recently list uid={} cost={}", req.getUid(), cost);
        return createResult(req, HttpCode.SUCCESS, listVO);
    }

    /**
     * game列表
     */
    @RequestMapping("gameList")
    private String gameList(HttpServletRequest request) {
        PartyListDTO req = RequestUtils.getSendData(request, PartyListDTO.class);
        long timeMillis = System.currentTimeMillis();
        ListVO listVO = roomListService.gameList(req);
        long cost = System.currentTimeMillis() - timeMillis;
        logger.info("gameList uid={} gameType={} cost={}", req.getUid(), req.getGameType(), cost);
        return createResult(req, HttpCode.SUCCESS, listVO);
    }

    /**
     * game列表(访客模式)
     */
    @RequestMapping(UserInterceptor.VISITOR + "gameList")
    private String gameListVisitor(HttpServletRequest request) {
        PartyListDTO req = RequestUtils.getSendData(request, PartyListDTO.class);
        long timeMillis = System.currentTimeMillis();
        ListVO listVO = roomListService.gameList(req);
        long cost = System.currentTimeMillis() - timeMillis;
        logger.info("gameListVisitor cost={}", cost);
        return createResult(req, HttpCode.SUCCESS, listVO);
    }

    /**
     * 新game列表
     */
    @RequestMapping("allGameList")
    private String allGameList(HttpServletRequest request) {
        PartyListDTO req = RequestUtils.getSendData(request, PartyListDTO.class);
        long timeMillis = System.currentTimeMillis();
        AllGameListVO listVO = roomListService.allGameList(req);
        long cost = System.currentTimeMillis() - timeMillis;
        logger.info("allGameList uid={} gameType={} cost={}", req.getUid(), req.getGameType(), cost);
        return createResult(req, HttpCode.SUCCESS, listVO);
    }

    /**
     * 房间活动列表
     */
    @RequestMapping("eventList")
    private String eventList(HttpServletRequest request) {
        PartyListDTO req = RequestUtils.getSendData(request, PartyListDTO.class);
        long timeMillis = System.currentTimeMillis();
        RoomEventListVO listVO = roomListService.eventList(req);
        long cost = System.currentTimeMillis() - timeMillis;
        logger.info("eventList uid={} page={} cost={}", req.getUid(), req.getPage(), cost);
        return createResult(req, HttpCode.SUCCESS, listVO);
    }

    /**
     * 家族房间活动列表
     * familyId参数必填
     */
    @RequestMapping("familyEventList")
    private String familyEventList(HttpServletRequest request) {
        PartyListDTO req = RequestUtils.getSendData(request, PartyListDTO.class);
        long timeMillis = System.currentTimeMillis();
        RoomEventListVO listVO = roomListService.familyEventList(req);
        long cost = System.currentTimeMillis() - timeMillis;
        logger.info("familyEventList uid={} page={} cost={}", req.getUid(), req.getPage(), cost);
        return createResult(req, HttpCode.SUCCESS, listVO);
    }

    /**
     * discover chat 列表
     */
    @RequestMapping("activeChatList")
    private String activeRoomList(HttpServletRequest request) {
        long timeMillis = System.currentTimeMillis();
        PartyListDTO req = RequestUtils.getSendData(request, PartyListDTO.class);
        ListVO forYouList = roomListService.getActiveRoomList(req);
        long cost = System.currentTimeMillis() - timeMillis;
        logger.info("get ActiveRoomList uid={} cost={}", req.getUid(), cost);
        return createResult(req, HttpCode.SUCCESS, forYouList);
    }

    /**
     * 房间内的贡献榜
     */
    @RequestMapping("roomDevoteList")
    private String roomDevoteList(HttpServletRequest request) {
        RoomDevoteListDTO req = RequestUtils.getSendData(request, RoomDevoteListDTO.class);
        if (!req.isParmsValid()) {
            logger.error("roomDevoteList params error req={}", req);
            return createResult(req, HttpCode.PARAM_ERROR, new Object());
        }
        long timeMillis = System.currentTimeMillis();
        RoomDevoteVO roomDevoteVO = roomListService.roomDevoteList(req);
        long cost = System.currentTimeMillis() - timeMillis;
        logger.info("roomDevoteList roomId={} uid={} cost={}", req.getRoomId(), req.getUid(), cost);
        return createResult(req, HttpCode.SUCCESS, roomDevoteVO);
    }

    /**
     * 主页room rank
     */
    @RequestMapping("roomRankList")
    private String roomRankList(HttpServletRequest request) {
        HomeRankListDTO req = RequestUtils.getSendData(request, HomeRankListDTO.class);
        if (!req.isParmsValid()) {
            logger.error("roomRankList params error req={}", req);
            return createResult(req, HttpCode.PARAM_ERROR, new Object());
        }
        long timeMillis = System.currentTimeMillis();
        RankListVO rankListVO = homeRankListService.getRankListVO(RoomListConstant.ROOM_RANK_TYPE, req.getTtype());
        long cost = System.currentTimeMillis() - timeMillis;
        logger.info("roomRankList requestId={} uid={} cost={}", req.getRequestId(), req.getUid(), cost);
        return createResult(req, HttpCode.SUCCESS, rankListVO);
    }

    /**
     * 主页send rank
     */
    @RequestMapping("sendRankList")
    private String sendRankList(HttpServletRequest request) {
        HomeRankListDTO req = RequestUtils.getSendData(request, HomeRankListDTO.class);
        if (!req.isParmsValid()) {
            logger.error("sendRankList params error req={}", req);
            return createResult(req, HttpCode.PARAM_ERROR, new Object());
        }
        long timeMillis = System.currentTimeMillis();
        RankListVO rankListVO = homeRankListService.getRankListVO(RoomListConstant.USER_RANK_SEND_TYPE, req.getTtype());
        long cost = System.currentTimeMillis() - timeMillis;
        logger.info("sendRankList requestId={} uid={} cost={}", req.getRequestId(), req.getUid(), cost);
        return createResult(req, HttpCode.SUCCESS, rankListVO);
    }

    /**
     * 主页receive rank
     */
    @RequestMapping("receiveRankList")
    private String receiveRankList(HttpServletRequest request) {
        HomeRankListDTO req = RequestUtils.getSendData(request, HomeRankListDTO.class);
        if (!req.isParmsValid()) {
            logger.error("receiveRankList params error req={}", req);
            return createResult(req, HttpCode.PARAM_ERROR, new Object());
        }
        long timeMillis = System.currentTimeMillis();
        RankListVO rankListVO = homeRankListService.getRankListVO(RoomListConstant.USER_RANK_RECEIVE_TYPE, req.getTtype());
        long cost = System.currentTimeMillis() - timeMillis;
        logger.info("receiveRankList requestId={} uid={} cost={}", req.getRequestId(), req.getUid(), cost);
        return createResult(req, HttpCode.SUCCESS, rankListVO);
    }

    /**
     * 房间内用户排行榜实时数据
     */
    @RequestMapping("userRoomRankList")
    private String userRoomRankList(HttpServletRequest request) {
        HttpEnvData req = RequestUtils.getSendData(request, HttpEnvData.class);
        long timeMillis = System.currentTimeMillis();
        UserRoomRankTotalVO userRoomRankTotalVO = homeRankListService.getUserRoomRankList(req.getRoomId());
        long cost = System.currentTimeMillis() - timeMillis;
        logger.info("userRoomRankList requestId={} uid={} roomId={} cost={}", req.getRequestId(),
                req.getUid(), req.getRoomId(), cost);
        return createResult(req, HttpCode.SUCCESS, userRoomRankTotalVO);
    }

    /**
     * 7日新用户推荐进房间
     */
    @RequestMapping("newUserRecommend")
    public String getNewUserRecommendInfo(HttpServletRequest request) {
        long millis = System.currentTimeMillis();
        RoomRecommendDTO req = RequestUtils.getSendData(request, RoomRecommendDTO.class);
        NewUserRecommendVO vo = roomRecommendService.getNewUserRecommendInfo(req);
        logger.info("getNewUserRecommendInfo uid={} timeMillis={}", req.getUid(), System.currentTimeMillis() - millis);
        return createResult(req, HttpCode.SUCCESS, vo);
    }

    /**
     * 房间及用户搜索
     */
    @RequestMapping("search")
    private String search(HttpServletRequest request) {
        long millis = System.currentTimeMillis();
        PartyListDTO req = RequestUtils.getSendData(request, PartyListDTO.class);
        SearchVO vo = roomListService.search(req);
        logger.info("room search uid={} key={} timeMillis={}", req.getUid(), req.getKey(), System.currentTimeMillis() - millis);
        return createResult(req, HttpCode.SUCCESS, vo);
    }

    /**
     * 搜索推荐列表
     */
    @RequestMapping("getHotSearchList")
    private String getHotSearchList(HttpServletRequest request) {
        long millis = System.currentTimeMillis();
        HttpEnvData req = RequestUtils.getSendData(request, HttpEnvData.class);
        logger.info("getHotSearchList req={}", req);
        HotSearchHistoryVO vo = roomListService.getHotSearchList(req);
        logger.info("getHotSearchList uid={} timeMillis={}", req.getUid(), System.currentTimeMillis() - millis);
        return createResult(req, HttpCode.SUCCESS, vo);
    }

    /**
     * 家族详情页-家族房间
     */
    @RequestMapping("getFamilyRoomHotList")
    private String getFamilyRoomHotList(HttpServletRequest request) {
        long millis = System.currentTimeMillis();
        FamilyRoomDTO req = RequestUtils.getSendData(request, FamilyRoomDTO.class);
        logger.info("getFamilyRoomHotList req={}", req);
        PageVO<PopularListVO> vo = roomListService.getFamilyRoomList(req);
        logger.info("getFamilyRoomHotList uid={} timeMillis={}", req.getUid(), System.currentTimeMillis() - millis);
        return createResult(req, HttpCode.SUCCESS, vo);
    }

    /**
     * 8.60首页引导弹窗
     */
    @RequestMapping("roomGuideRecommend")
    public String roomGuideRecommend(HttpServletRequest request) {
        long millis = System.currentTimeMillis();
        RoomRecommendDTO req = RequestUtils.getSendData(request, RoomRecommendDTO.class);
        RoomGuideRecommendVO vo = roomRecommendService.roomGuideRecommend(req);
        logger.info("roomGuideRecommend uid={} timeMillis={}", req.getUid(), System.currentTimeMillis() - millis);
        return createResult(req, HttpCode.SUCCESS, vo);
    }

}

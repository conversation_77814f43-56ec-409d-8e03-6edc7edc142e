package com.quhong.controllers;

import com.alibaba.fastjson.JSONObject;
import com.quhong.data.dto.ImageDTO;
import com.quhong.data.dto.TextDTO;
import com.quhong.data.dto.FeginVideoDTO;
import com.quhong.data.vo.DetectVO;
import com.quhong.enums.ApiResult;
import com.quhong.service.DetectService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping(value = "${baseUrl}", produces = MediaType.APPLICATION_JSON_VALUE)
public class DetectController {
    private static final Logger logger = LoggerFactory.getLogger(DetectController.class);

    @Resource
    private DetectService detectService;

    @PostMapping("detectText")
    private ApiResult<DetectVO> detectText(@RequestBody TextDTO dto) {
        logger.info("detectText requestId={} TextDTO={}", dto.getRequestId(), JSONObject.toJSONString(dto));
      return  detectService.detectText(dto);
//        return detectService.detectTextManage(dto);
    }

    @PostMapping("detectImage")
    private ApiResult<DetectVO> detectImage(@RequestBody ImageDTO dto) {
        logger.info("detectImage requestId={} ImageDTO={}", dto.getRequestId(), JSONObject.toJSONString(dto));
        return detectService.detectImage(dto);
//        return detectService.detectImageManage(dto);
    }

    @PostMapping("handleVideoUrl")
    private ApiResult<DetectVO> handleVideoUrl(@RequestBody FeginVideoDTO dto) {
        long millis = System.currentTimeMillis();
        ApiResult<DetectVO> obj = detectService.handleVideoUrl(dto);
        logger.info("handleVideoUrl requestId={} FeginVideoDTO={} cost={}", dto.getRequestId(), dto, System.currentTimeMillis() - millis);
        return obj;
    }

}

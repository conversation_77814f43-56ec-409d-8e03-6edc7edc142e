package com.quhong.controllers;

import com.quhong.data.dto.BannerDTO;
import com.quhong.data.vo.BannerVO;
import com.quhong.data.vo.HomePopupVO;
import com.quhong.data.vo.MeBannerListVO;
import com.quhong.datas.HttpResult;
import com.quhong.enums.HttpCode;
import com.quhong.handler.WebController;
import com.quhong.service.BannerService;
import com.quhong.utils.RequestUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 主页轮播图
 *
 * <AUTHOR>
 * @date 2022/8/25
 */
@RestController
@RequestMapping(value = "${baseUrl}")
public class BannerController extends WebController {

    private static final Logger logger = LoggerFactory.getLogger(BannerController.class);

    @Resource
    private BannerService bannerService;

    /**
     * app主页下发的滚动banner
     */
    @RequestMapping("banner")
    public String getBanner(HttpServletRequest request) {
        long millis = System.currentTimeMillis();
        BannerDTO req = RequestUtils.getSendData(request, BannerDTO.class);
        BannerVO vo = bannerService.getBanner(req);
        logger.info("get banner list. uid={}  timeMillis={}", req.getUid(), System.currentTimeMillis() - millis);
        return createResult(req, HttpCode.SUCCESS, vo);
    }


    /**
     * app主页弹窗
     */
    @RequestMapping("homePopup")
    public String getHomePopup(HttpServletRequest request) {
        long millis = System.currentTimeMillis();
        BannerDTO req = RequestUtils.getSendData(request, BannerDTO.class);
        HomePopupVO vo = bannerService.getPopupBanner(req);
        logger.info("getHomePopup list. uid={}  timeMillis={}", req.getUid(), System.currentTimeMillis() - millis);
        return createResult(req, HttpCode.SUCCESS, vo);
    }

    /**
     * me页面下发的滚动banner
     */
    @RequestMapping("banner/me")
    public String meBanner(HttpServletRequest request) {
        long millis = System.currentTimeMillis();
        BannerDTO req = RequestUtils.getSendData(request, BannerDTO.class);
        MeBannerListVO vo = bannerService.meBanner(req);
        logger.info("meBanner list. uid={}  timeMillis={}", req.getUid(), System.currentTimeMillis() - millis);
        return createResult(req, HttpCode.SUCCESS, vo);
    }

    /**
     * Lucky Game Banner
     */
    @RequestMapping("luckyGameBanner")
    public HttpResult<HomePopupVO> luckyGameBanner(HttpServletRequest request) {
        long millis = System.currentTimeMillis();
        BannerDTO req = RequestUtils.getSendData(request, BannerDTO.class);
        HomePopupVO vo = bannerService.luckyGameBanner(req);
        logger.info("getHomePopup list. uid={}  timeMillis={}", req.getUid(), System.currentTimeMillis() - millis);
        return HttpResult.getOk(vo);
    }
}

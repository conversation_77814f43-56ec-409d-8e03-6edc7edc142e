package com.quhong.executors;

import com.quhong.core.annotation.MsgExecutor;
import com.quhong.core.clusters.balance.HashBalance;
import com.quhong.core.clusters.servers.ClusterServerConnector;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.msg.server.PlayerLoginAck;
import com.quhong.core.msg.server.PlayerLoginMsg;
import com.quhong.data.ActorData;
import com.quhong.datas.PlayerData;
import com.quhong.enums.BaseServerCmd;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.msg.push.ClientLogReportMsg;
import com.quhong.net.cache.ActorMsgCache;
import com.quhong.player.cache.Player;
import com.quhong.player.cache.PlayerCacheMap;
import com.quhong.room.RoomService;
import com.quhong.room.RoomWebSender;
import com.quhong.room.data.RoomActorData;
import com.quhong.room.redis.RoomPlayerRedis;
import com.quhong.room.rooms.Room;
import com.quhong.service.mysql.ClientLogService;
import com.quhong.task.TaskFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;

@MsgExecutor
public class PlayerOnlineExecutor extends BaseServerMsgExecutor<PlayerLoginMsg> {
    private static final Logger logger = LoggerFactory.getLogger(PlayerOnlineExecutor.class);

    @Autowired
    private PlayerCacheMap cacheMap;
    @Autowired
    private ActorDao actorDao;
    @Autowired
    private RoomPlayerRedis roomPlayerRedis;
    @Autowired
    private RoomService roomService;
    @Autowired
    private ActorMsgCache actorMsgCache;
    @Autowired
    private HashBalance hashBalance;
    @Autowired
    private RoomWebSender roomWebSender;
    @Resource
    private ClientLogService clientLogService;

    public PlayerOnlineExecutor() {
        super(BaseServerCmd.PLAYER_LOGIN);
    }

    @Override
    public void execute(ClusterServerConnector connector, PlayerLoginMsg msg) {
        TaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                // 更新playerData的状态
                String uid = msg.getUid();
                Player player = doUpdatePlayerData(uid);
                if (player == null) {
                    return;
                }
                // 发送返回消息
                sendLoginAck(connector, player);
                // 更新用户防线内状态
                updateOnlineImRoom(uid);
                // 看下有无重发消息需要处理
                actorMsgCache.requestUnSendMsg(player, 0);
                // 检查是否需要上报客户端日志
                checkAndSendClientLogReportMsg(connector, player);
            }
        });
    }

    public Player doUpdatePlayerData(String uid) {
        cacheMap.updatePlayer(uid);
        ActorData actorData = actorDao.getActorData(uid);
        if (actorData == null) {
            logger.error("player online error. can not find actor. uid={}", uid);
            return null;
        }
        Player player = cacheMap.getPlayer(uid);
        if (player == null) {
            logger.error("player online error. can not find player. uid={}", uid);
            return null;
        }
        // 更新用户playerData信息
        PlayerData playerData = cacheMap.updatePlayer(uid);
        logger.info("player online. clientSystem={} clientVersion={} rid={} uid={}", playerData.getClientSystem(), playerData.getClientVersion(), actorData.getRid(), actorData.getUid());
        return player;
    }

    private void updateOnlineImRoom(String uid) {
        String roomId = roomPlayerRedis.getActorRoomStatus(uid);
        if (StringUtils.isEmpty(roomId)) {
            return;
        }
        int sererId = hashBalance.selectServerId(roomId);
        if (hashBalance.isMyServer(sererId)) {
            // 如果分布在当前房间
            doUpdateOnlineInRoom(roomId, uid);
        } else {
            // 请求房间所在的roomId
            roomWebSender.playerOnline(roomId, uid);
        }
    }

    public void doUpdateOnlineInRoom(String roomId, String uid) {
        roomService.checkAndEnterRoom(roomId, uid, null, false);
        Room room = roomService.getRoom(roomId);
        RoomActorData roomActorData = room.getActorMap().get(uid);
        if (roomActorData != null) {
            // 清除离线时间
            roomActorData.setOfflineTime(0);
        } else {
            logger.error("actor online. can not find actor in room. roomId={} uid={}", room.getRoomId(), uid);
        }
    }

    /**
     * 发送登录返回消息
     *
     * @param connector
     * @param player
     */
    private void sendLoginAck(ClusterServerConnector connector, Player player) {
        PlayerLoginAck ack = new PlayerLoginAck();
        ack.setUid(player.getUid());
        sendMsg(connector, player.getPlayerData(), ack);
    }

    private void checkAndSendClientLogReportMsg(ClusterServerConnector connector, Player player) {
        int logEndTime = clientLogService.getLogEndTime(player.getUid());
        if (0 != logEndTime) {
            sendMsg(connector, player.getPlayerData(), new ClientLogReportMsg(logEndTime));
        }
    }
}

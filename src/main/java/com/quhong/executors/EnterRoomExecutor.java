package com.quhong.executors;

import com.quhong.core.annotation.MsgExecutor;
import com.quhong.core.clusters.servers.ClusterServerConnector;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.enums.Cmd;
import com.quhong.enums.ResponseCode;
import com.quhong.msg.room.EnterRoomMsg;
import com.quhong.room.processors.EnterRoomProcessor;
import com.quhong.room.rooms.Room;
import org.springframework.beans.factory.annotation.Autowired;

@MsgExecutor
public class EnterRoomExecutor extends RoomExecutor<EnterRoomMsg> {

    @Autowired
    private EnterRoomProcessor enterRoomProcessor;

    public EnterRoomExecutor() {
        super(Cmd.ENTER_ROOM_REQ);
    }

    @Override
    protected void doExecute(ClusterServerConnector connector, Room room, EnterRoomMsg msg) {
        enterRoomProcessor.enterRoom(room, msg);
    }
}

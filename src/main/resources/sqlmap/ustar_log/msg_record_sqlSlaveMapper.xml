<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.quhong.mysql.slave_mapper.ustar_log.SlaveMsgRecordMapper">
    <resultMap id="baseResultMap" type="com.quhong.mysql.data.MysqlMsgRecordData">
    </resultMap>
    <sql id="baseSql">
            msg_index,
            fromUid,
            toUid,
            msg,
            msgInfo,
            msgType,
            from_delete,
            to_delete,
            `timestamp`,
            msg_id,
            from_like,
            to_like,
            status
    </sql>
    <sql id="itemSql">
        #{item.msgIndex},#{item.fromUid},#{item.toUid},#{item.msg},#{item.msgInfo},#{item.msgType},#{item.fromDelete},#{item.toDelete},#{item.timestamp},#{item.msgId},#{item.fromLike},#{item.toLike},#{item.status}
    </sql>
    <select id="getList" resultMap="baseResultMap">
        <foreach collection="suffixList" item="tableSuffix" separator="UNION ALL">
           (select id,<include refid="baseSql"/> from s_msg_record_${tableSuffix} where msg_index=#{msgIndex} and ((fromUid=#{uid} and from_delete = 0) or (toUid=#{uid} and to_delete = 0)))
        </foreach>
        order by `timestamp` desc limit #{startIndex},#{pageSize}
    </select>

</mapper>

package com.quhong.mysql.dao;

import com.alibaba.fastjson.JSONObject;
import com.quhong.mysql.data.RobotConfigData;
import com.quhong.mysql.mapper.ustar.RobotConfigMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class RobotConfigDao {
    private static final Logger logger = LoggerFactory.getLogger(RobotConfigDao.class);

    @Autowired
    private RobotConfigMapper configMapper;

    public List<RobotConfigData> getConfigList() {
        try {
            return configMapper.getList();
        } catch (Exception e) {
            logger.error("get robot config list error. {}", e.getMessage(), e);
        }
        return new ArrayList<>();
    }

    public Object updateParam(JSONObject param) {
        return configMapper.updateParam(param.getIntValue("initRobotCount"), param.toJSONString());
    }
}

package com.quhong;

import com.quhong.mysql.data.RobotConfigData;
import com.quhong.mysql.mapper.ustar.RobotConfigMapper;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

/**
 * Unit test for simple App.
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class RobotConfigTest {
    private static final Logger logger = LoggerFactory.getLogger(RobotConfigTest.class);

    @Autowired
    private RobotConfigMapper configMapper;

    @Test
    public void getList(){
        List<RobotConfigData> configDataList = configMapper.getList();
        logger.info("config.list.size={}", configDataList.size());
    }
}

apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  namespace: devops
  name: inner-ustar-data-resources
  annotations:
    alb.ingress.kubernetes.io/group.name: ustar-prod-inner
    alb.ingress.kubernetes.io/load-balancer-name: prod-inner
    kubernetes.io/ingress.class: alb
    alb.ingress.kubernetes.io/scheme: internal
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}]'
    alb.ingress.kubernetes.io/healthcheck-protocol: HTTP
    alb.ingress.kubernetes.io/backend-protocol: HTTP
    alb.ingress.kubernetes.io/healthcheck-path: /system/health_check
    alb.ingress.kubernetes.io/healthcheck-interval-seconds: '5'
    alb.ingress.kubernetes.io/healthcheck-timeout-seconds: '2'  #健康检查超时时间设置为2s
spec:
  rules:
    - http:
        paths:
          - path: /inner/data_resources/
            pathType: Prefix
            backend:
              service:
                name: ustar-data-resources
                port:
                  number: 8080

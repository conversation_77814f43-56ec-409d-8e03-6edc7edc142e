package com.quhong.msg.room;

import com.alibaba.fastjson.JSONObject;
import com.quhong.core.annotation.Message;
import com.quhong.enums.Cmd;
import com.quhong.msg.MarsServerMsg;
import com.quhong.proto.YoustarProtoUser;

@Message(cmd = Cmd.UNLOCK_GIFT_MSG)
public class UnlockGiftMsg extends MarsServerMsg {
    private int giftId;
    private int selectGiftId;

    @Override
    public void fillFrom(JSONObject data) {
        this.giftId = data.getIntValue("giftId");
        this.selectGiftId = data.getIntValue("selectGiftId");
    }

    @Override
    protected void doFromBody(byte[] bytes) throws Exception {
        YoustarProtoUser.UnlockGiftMessage msg = YoustarProtoUser.UnlockGiftMessage.parseFrom(bytes);
        this.protoHeader.doFromBody(msg.getHeader());
        this.giftId = msg.getGiftId();
        this.selectGiftId = msg.getSelectGiftId();
    }

    @Override
    protected byte[] doToBody() throws Exception {
        YoustarProtoUser.UnlockGiftMessage.Builder builder = YoustarProtoUser.UnlockGiftMessage.newBuilder();
        builder.setHeader(this.protoHeader.doToBody());
        builder.setGiftId(giftId);
        builder.setSelectGiftId(selectGiftId);
        return builder.build().toByteArray();
    }

    public int getGiftId() {
        return giftId;
    }

    public void setGiftId(int giftId) {
        this.giftId = giftId;
    }

    public int getSelectGiftId() {
        return selectGiftId;
    }

    public void setSelectGiftId(int selectGiftId) {
        this.selectGiftId = selectGiftId;
    }
}

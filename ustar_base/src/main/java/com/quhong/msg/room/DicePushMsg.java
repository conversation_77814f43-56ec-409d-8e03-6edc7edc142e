package com.quhong.msg.room;

import com.alibaba.fastjson.JSONObject;
import com.quhong.core.annotation.Message;
import com.quhong.enums.Cmd;
import com.quhong.msg.MarsServerMsg;
import com.quhong.msg.obj.UNameObject;
import com.quhong.proto.YoustarProtoRoom;

@Message(cmd = Cmd.DICE_ROOM_PUSH)
public class DicePushMsg extends MarsServerMsg {
    private UNameObject uname;
    private int num;

    public void fillFrom(JSONObject object){
        JSONObject unameObject = object.getJSONObject("user_info");
        if(unameObject != null){
            this.uname = new UNameObject();
            this.uname.fillFrom(unameObject);
        }
        this.num = object.getIntValue("num");
    }

    @Override
    protected void doFromBody(byte[] bytes) throws Exception {
        YoustarProtoRoom.DiceMessage msg = YoustarProtoRoom.DiceMessage.parseFrom(bytes);
        this.protoHeader.doFromBody(msg.getHeader());
        if(msg.getUname() != null) {
            this.uname = new UNameObject();
            this.uname.doFromBody(msg.getUname());
        }
        this.num = msg.getNum();
    }

    @Override
    protected byte[] doToBody() throws Exception {
        YoustarProtoRoom.DiceMessage.Builder builder = YoustarProtoRoom.DiceMessage.newBuilder();
        builder.setHeader(this.protoHeader.doToBody());
        if(this.uname != null){
            builder.setUname(this.uname.doToBody());
        }
        builder.setNum(num);
        return builder.build().toByteArray();
    }

    public UNameObject getUname() {
        return uname;
    }

    public void setUname(UNameObject uname) {
        this.uname = uname;
    }

    public int getNum() {
        return num;
    }

    public void setNum(int num) {
        this.num = num;
    }
}

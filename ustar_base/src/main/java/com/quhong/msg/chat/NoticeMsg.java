package com.quhong.msg.chat;

import com.alibaba.fastjson.JSONObject;
import com.quhong.core.annotation.Message;
import com.quhong.enums.Cmd;
import com.quhong.msg.MarsServerMsg;
import com.quhong.proto.YoustarProtoChat;

@Message(cmd = Cmd.NOTICE_MSG)
public class NoticeMsg extends MarsServerMsg {
    private int ntype;//通知类型

    @Override
    public void fillFrom(JSONObject object) {
        this.ntype = object.getIntValue("ntype");
    }

    @Override
    protected void doFromBody(byte[] bytes) throws Exception {
        YoustarProtoChat.NoticeMessage msg = YoustarProtoChat.NoticeMessage.parseFrom(bytes);
        this.protoHeader.doFromBody(msg.getHeader());
        this.ntype = msg.getNtype();
    }

    @Override
    protected byte[] doToBody() throws Exception {
        YoustarProtoChat.NoticeMessage.Builder builder = YoustarProtoChat.NoticeMessage.newBuilder();
        builder.setHeader(this.protoHeader.doToBody());
        builder.setNtype(this.ntype);
        return builder.build().toByteArray();
    }

    public int getNtype() {
        return ntype;
    }

    public void setNtype(int ntype) {
        this.ntype = ntype;
    }
}

package com.quhong.data;

import com.alibaba.fastjson.annotation.JSONField;
import com.quhong.enums.ClientOS;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.List;

public class ActorData {
    private String uid;
    private int beans;
    private int gold;
    private int rid; // 用户rid （包含旧版本中的数字靓号）
    private int originalRid; // 原始rid
    private String name;
    private int gender;
    private int fb_gender;
    private String head;
    private String os;
    private int ride_option;
    private String video_option;
    private int age;
    private String desc;
    private String country;
    private int valid;
    private int version_code;
    private String version_name; // 版本号

    private String imei;
    private String oid;
    private int accept_talk; // 0关闭在线状态
    private int robot;
    @JSONField(name = "alpha_rid")
    private String alphaRid; // 靓号
    private int alphaLevel; // 靓号等级
    private int slang;
    private String channel; // 华为渠道专属字段
    @JSONField(name = "app_package_name")
    private String appPackageName; // 华为渠道专属字段
    @JSONField(name = "is_face")
    private int isFace; // 1真人
    private String tn_id; // 图灵id
    @JSONField(name = "label_list")
    private List<Integer> labelList = new ArrayList<>();
    private String ip; // ip
    private String ipCodeCountry; // 国家码及国家
    @JSONField(name = "account_status")
    private int accountStatus;  // 1：已标记删除，30天内删除  2：账号已删除，无法登录
    @JSONField(name = "general_conf")
    private GeneralConfActorData generalConfActorData;
    /**
     * 登录登出信息
     */
    @JSONField(name = "last_login")
    private ActorLastLoginData lastLogin;

    private ActorPushData push;

    @JSONField(name = "login_type")
    private int loginType;
    @JSONField(name = "heart_got")
    private int heartGot;
    private String idfa;
    private String birthday;
    private String android_id;
    private int vchat_status;
    private String promotion_id;
    private int is_delete;
    private int familyId; // 公会id，主键id
    private String shuMeiId;
    private String firstTnId; // 新账号的新设备id（仅新注册账号且为新设备时才会写入）

    public ActorData() {

    }

    public RidData getRidData() {
        if (ObjectUtils.isEmpty(alphaRid)) {
            return new RidData(String.valueOf(rid), 0);
        }
        return new RidData(alphaRid, alphaLevel);
    }

    public String getStrRid() {
        if (ObjectUtils.isEmpty(alphaRid)) {
            return String.valueOf(rid);
        }
        return alphaRid;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public int getBeans() {
        return beans;
    }

    public void setBeans(int beans) {
        this.beans = beans;
    }

    public int getRid() {
        return rid;
    }

    public void setRid(int rid) {
        this.rid = rid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getGender() {
        return gender;
    }

    public void setGender(int gender) {
        this.gender = gender;
    }

    public int getFb_gender() {
        return fb_gender;
    }

    public void setFb_gender(int fb_gender) {
        this.fb_gender = fb_gender;
    }

    public String getHead() {
        return head;
    }

    public void setHead(String head) {
        this.head = head;
    }

    public String getOs() {
        return os;
    }

    public void setOs(String os) {
        this.os = os;
    }

    public int getRide_option() {
        return ride_option;
    }

    public void setRide_option(int ride_option) {
        this.ride_option = ride_option;
    }

    public int getAge() {
        return age;
    }

    public void setAge(int age) {
        this.age = age;
    }

    public int getIntOs() {
        if ("1".equals(os)) {
            return ClientOS.IOS;
        }
        return ClientOS.ANDROID;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public int getValid() {
        return valid;
    }

    public void setValid(int valid) {
        this.valid = valid;
    }

    public int getVersion_code() {
        return version_code;
    }

    public void setVersion_code(int version_code) {
        this.version_code = version_code;
    }

    public String getImei() {
        return imei;
    }

    public void setImei(String imei) {
        this.imei = imei;
    }

    public String getOid() {
        return oid;
    }

    public void setOid(String oid) {
        this.oid = oid;
    }

    public int getAccept_talk() {
        return accept_talk;
    }

    public void setAccept_talk(int accept_talk) {
        this.accept_talk = accept_talk;
    }

    public int getRobot() {
        return robot;
    }

    public void setRobot(int robot) {
        this.robot = robot;
    }

    public int getSlang() {
        return slang;
    }

    public void setSlang(int slang) {
        this.slang = slang;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public int getIsFace() {
        return isFace;
    }

    public void setIsFace(int isFace) {
        this.isFace = isFace;
    }

    public String getAppPackageName() {
        return appPackageName;
    }

    public void setAppPackageName(String appPackageName) {
        this.appPackageName = appPackageName;
    }

    public String getTn_id() {
        return tn_id;
    }

    public void setTn_id(String tn_id) {
        this.tn_id = tn_id;
    }

    public List<Integer> getLabelList() {
        return labelList;
    }

    public void setLabelList(List<Integer> labelList) {
        this.labelList = labelList;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getIpCodeCountry() {
        return ipCodeCountry;
    }

    public void setIpCodeCountry(String ipCodeCountry) {
        this.ipCodeCountry = ipCodeCountry;
    }

    public int getAccountStatus() {
        return accountStatus;
    }

    public void setAccountStatus(int accountStatus) {
        this.accountStatus = accountStatus;
    }

    public GeneralConfActorData getGeneralConfActorData() {
        return generalConfActorData;
    }

    public void setGeneralConfActorData(GeneralConfActorData generalConfActorData) {
        this.generalConfActorData = generalConfActorData;
    }

    public ActorLastLoginData getLastLogin() {
        return lastLogin;
    }

    public void setLastLogin(ActorLastLoginData lastLogin) {
        this.lastLogin = lastLogin;
    }

    public int getLoginType() {
        return loginType;
    }

    public void setLoginType(int loginType) {
        this.loginType = loginType;
    }

    public int getHeartGot() {
        return heartGot;
    }

    public void setHeartGot(int heartGot) {
        this.heartGot = heartGot;
    }

    public String getIdfa() {
        return idfa;
    }

    public void setIdfa(String idfa) {
        this.idfa = idfa;
    }

    public String getBirthday() {
        return birthday;
    }

    public void setBirthday(String birthday) {
        this.birthday = birthday;
    }

    public String getAndroid_id() {
        return android_id;
    }

    public void setAndroid_id(String android_id) {
        this.android_id = android_id;
    }

    public int getVchat_status() {
        return vchat_status;
    }

    public void setVchat_status(int vchat_status) {
        this.vchat_status = vchat_status;
    }

    public String getPromotion_id() {
        return promotion_id;
    }

    public void setPromotion_id(String promotion_id) {
        this.promotion_id = promotion_id;
    }

    public ActorPushData getPush() {
        return push;
    }

    public void setPush(ActorPushData push) {
        this.push = push;
    }

    public String getVideo_option() {
        return video_option;
    }

    public void setVideo_option(String video_option) {
        this.video_option = video_option;
    }

    public int getGold() {
        return gold;
    }

    public void setGold(int gold) {
        this.gold = gold;
    }

    public int getIs_delete() {
        return is_delete;
    }

    public void setIs_delete(int is_delete) {
        this.is_delete = is_delete;
    }

    public String getAlphaRid() {
        return alphaRid;
    }

    public void setAlphaRid(String alphaRid) {
        this.alphaRid = alphaRid;
    }

    public int getAlphaLevel() {
        return alphaLevel;
    }

    public void setAlphaLevel(int alphaLevel) {
        this.alphaLevel = alphaLevel;
    }

    public int getOriginalRid() {
        return originalRid != 0 ? originalRid : rid;
    }

    public void setOriginalRid(int originalRid) {
        this.originalRid = originalRid;
    }

    public int getFamilyId() {
        return familyId;
    }

    public void setFamilyId(int familyId) {
        this.familyId = familyId;
    }

    public String getShuMeiId() {
        return shuMeiId;
    }

    public void setShuMeiId(String shuMeiId) {
        this.shuMeiId = shuMeiId;
    }

    public String getFirstTnId() {
        return firstTnId;
    }

    public void setFirstTnId(String firstTnId) {
        this.firstTnId = firstTnId;
    }

    public String getVersion_name() {
        return version_name;
    }

    public void setVersion_name(String version_name) {
        this.version_name = version_name;
    }
}

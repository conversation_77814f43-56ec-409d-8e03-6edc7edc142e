package com.quhong.core.concurrency.tasks;

import com.quhong.core.concurrency.queues.ITaskQueue;
import com.quhong.core.utils.SpringUtils;
import com.quhong.monitor.MonitorSender;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 */
public abstract class AbstractTask implements ITask {
    protected static final Logger logger = LoggerFactory.getLogger(AbstractTask.class);

    protected ITaskQueue queue;

    public AbstractTask() {
    }

    @Override
    public void setQueue(ITaskQueue queue) {
        this.queue = queue;
    }

    @Override
    public ITaskQueue getQueue() {
        return queue;
    }

    @Override
    public void run() {
        try {
            execute();
        } catch (Throwable t) {
            logger.error("{}, {}", this, t.getMessage(), t);
            try {
                MonitorSender sender = SpringUtils.getBean(MonitorSender.class);
                String message = t.getMessage();
                if(message.length() > 200){
                    message = message.substring(0, 200);
                }
                sender.info("ustar_java_exception", "任务执行报错", this.getClass().getName() + " " + message);
            } catch (Exception e) {
                logger.error("{}, {}", this, e.getMessage(), e);
            }
        }
    }

    protected abstract void execute();
}

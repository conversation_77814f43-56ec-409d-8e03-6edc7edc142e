// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: Response.proto

package com.quhong.proto;

public final class Response {
  private Response() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  /**
   * Protobuf enum {@code ResponseCode}
   */
  public enum ResponseCode
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <pre>
     * </pre>
     *
     * <code>RES_CODE_SUCCESS = 0;</code>
     */
    RES_CODE_SUCCESS(0),
    /**
     * <pre>
     *服务器错误
     * </pre>
     *
     * <code>RES_CODE_SERVER_ERROR = 1;</code>
     */
    RES_CODE_SERVER_ERROR(1),
    /**
     * <pre>
     *token验证失败，需要重新登录
     * </pre>
     *
     * <code>RES_CODE_TOKEN_VERIFICATION_ERROR = 2;</code>
     */
    RES_CODE_TOKEN_VERIFICATION_ERROR(2),
    /**
     * <pre>
     *用户远程登录，本地需要重新登录
     * </pre>
     *
     * <code>RES_CODE_KICK_OUT = 3;</code>
     */
    RES_CODE_KICK_OUT(3),
    /**
     * <pre>
     *没有找到对应的房间
     * </pre>
     *
     * <code>RES_CODE_CAN_NOT_FIND_ROOM = 2001;</code>
     */
    RES_CODE_CAN_NOT_FIND_ROOM(2001),
    UNRECOGNIZED(-1),
    ;

    /**
     * <pre>
     * </pre>
     *
     * <code>RES_CODE_SUCCESS = 0;</code>
     */
    public static final int RES_CODE_SUCCESS_VALUE = 0;
    /**
     * <pre>
     *服务器错误
     * </pre>
     *
     * <code>RES_CODE_SERVER_ERROR = 1;</code>
     */
    public static final int RES_CODE_SERVER_ERROR_VALUE = 1;
    /**
     * <pre>
     *token验证失败，需要重新登录
     * </pre>
     *
     * <code>RES_CODE_TOKEN_VERIFICATION_ERROR = 2;</code>
     */
    public static final int RES_CODE_TOKEN_VERIFICATION_ERROR_VALUE = 2;
    /**
     * <pre>
     *用户远程登录，本地需要重新登录
     * </pre>
     *
     * <code>RES_CODE_KICK_OUT = 3;</code>
     */
    public static final int RES_CODE_KICK_OUT_VALUE = 3;
    /**
     * <pre>
     *没有找到对应的房间
     * </pre>
     *
     * <code>RES_CODE_CAN_NOT_FIND_ROOM = 2001;</code>
     */
    public static final int RES_CODE_CAN_NOT_FIND_ROOM_VALUE = 2001;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static ResponseCode valueOf(int value) {
      return forNumber(value);
    }

    public static ResponseCode forNumber(int value) {
      switch (value) {
        case 0: return RES_CODE_SUCCESS;
        case 1: return RES_CODE_SERVER_ERROR;
        case 2: return RES_CODE_TOKEN_VERIFICATION_ERROR;
        case 3: return RES_CODE_KICK_OUT;
        case 2001: return RES_CODE_CAN_NOT_FIND_ROOM;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<ResponseCode>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        ResponseCode> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<ResponseCode>() {
            public ResponseCode findValueByNumber(int number) {
              return ResponseCode.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return com.quhong.proto.Response.getDescriptor().getEnumTypes().get(0);
    }

    private static final ResponseCode[] VALUES = values();

    public static ResponseCode valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private ResponseCode(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:ResponseCode)
  }


  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\016Response.proto*\236\001\n\014ResponseCode\022\024\n\020RES" +
      "_CODE_SUCCESS\020\000\022\031\n\025RES_CODE_SERVER_ERROR" +
      "\020\001\022%\n!RES_CODE_TOKEN_VERIFICATION_ERROR\020" +
      "\002\022\025\n\021RES_CODE_KICK_OUT\020\003\022\037\n\032RES_CODE_CAN" +
      "_NOT_FIND_ROOM\020\321\017B\022\n\020com.quhong.protob\006p" +
      "roto3"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
        new com.google.protobuf.Descriptors.FileDescriptor.    InternalDescriptorAssigner() {
          public com.google.protobuf.ExtensionRegistry assignDescriptors(
              com.google.protobuf.Descriptors.FileDescriptor root) {
            descriptor = root;
            return null;
          }
        };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        }, assigner);
  }

  // @@protoc_insertion_point(outer_class_scope)
}
